package com.example.sharenshop.data.local.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\t\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0018\u0010\u0007\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\b2\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u001c\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u000b0\b2\u0006\u0010\f\u001a\u00020\u0005H\'J\u001c\u0010\r\u001a\u00020\u00032\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\t0\u000bH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0016\u0010\u0010\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\u0012J\u0016\u0010\u0013\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\u0012\u00a8\u0006\u0014"}, d2 = {"Lcom/example/sharenshop/data/local/dao/InvoiceItemDao;", "", "deleteInvoiceItemById", "", "invoiceItemId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getInvoiceItemById", "Lkotlinx/coroutines/flow/Flow;", "Lcom/example/sharenshop/data/model/InvoiceItem;", "getInvoiceItemsByInvoiceId", "", "invoiceId", "insertAllInvoiceItems", "invoiceItems", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertInvoiceItem", "invoiceItem", "(Lcom/example/sharenshop/data/model/InvoiceItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateInvoiceItem", "app_debug"})
@androidx.room.Dao()
public abstract interface InvoiceItemDao {
    
    @androidx.room.Query(value = "SELECT * FROM invoice_items WHERE id = :invoiceItemId")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.example.sharenshop.data.model.InvoiceItem> getInvoiceItemById(@org.jetbrains.annotations.NotNull()
    java.lang.String invoiceItemId);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertInvoiceItem(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.InvoiceItem invoiceItem, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateInvoiceItem(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.InvoiceItem invoiceItem, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM invoice_items WHERE id = :invoiceItemId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteInvoiceItemById(@org.jetbrains.annotations.NotNull()
    java.lang.String invoiceItemId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM invoice_items WHERE invoiceId = :invoiceId")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharenshop.data.model.InvoiceItem>> getInvoiceItemsByInvoiceId(@org.jetbrains.annotations.NotNull()
    java.lang.String invoiceId);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertAllInvoiceItems(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.sharenshop.data.model.InvoiceItem> invoiceItems, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}