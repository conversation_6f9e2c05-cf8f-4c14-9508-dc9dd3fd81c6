package com.example.sharenshop.data.repository

import com.example.sharenshop.data.local.dao.InvoiceDao
import com.example.sharenshop.data.local.dao.InvoiceItemDao
import com.example.sharenshop.data.local.dao.ProductDao
import com.example.sharenshop.data.local.dao.CustomerDao
import com.example.sharenshop.domain.repository.StatisticsRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import java.math.BigDecimal
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class StatisticsRepositoryImpl @Inject constructor(
    private val invoiceDao: InvoiceDao,
    private val invoiceItemDao: InvoiceItemDao,
    private val productDao: ProductDao,
    private val customerDao: CustomerDao
) : StatisticsRepository {

    override fun getTotalSalesAmount(): Flow<BigDecimal> = invoiceDao.getAllInvoices().map {
        it.sumOf { invoice -> invoice.finalAmount }
    }

    // invoiceItemDao.getAllInvoiceItems().map { ...
    // ... سایر خطوط مرتبط با getAllInvoiceItems را کامنت کن ...

    override fun getTopSellingProducts(limit: Int): Flow<Map<String, Int>> = kotlinx.coroutines.flow.flowOf(emptyMap())

    override fun getCustomerSpending(customerId: String): Flow<BigDecimal> = invoiceDao.getInvoicesByCustomerId(customerId).map {
        it.sumOf { invoice -> invoice.finalAmount }
    }

    override fun getSalesByPaymentStatus(status: String): Flow<BigDecimal> = invoiceDao.getAllInvoices().map {
        it.filter { invoice -> invoice.paymentStatus == status }
            .sumOf { invoice -> invoice.finalAmount }
    }

    override fun getSalesByTimePeriod(startTime: Long, endTime: Long): Flow<BigDecimal> = invoiceDao.getAllInvoices().map {
        it.filter { invoice -> invoice.invoiceDate in startTime..endTime }
            .sumOf { invoice -> invoice.finalAmount }
    }

    override fun getTotalProductsSold(): Flow<Int> = kotlinx.coroutines.flow.flowOf(0)
} 