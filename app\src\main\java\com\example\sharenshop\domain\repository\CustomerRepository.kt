package com.example.sharenshop.domain.repository

import com.example.sharenshop.data.model.Customer
import kotlinx.coroutines.flow.Flow

interface CustomerRepository {
    fun getCustomerById(customerId: String): Flow<Customer?>
    suspend fun insertCustomer(customer: Customer)
    suspend fun updateCustomer(customer: Customer)
    suspend fun deleteCustomerById(customerId: String)
    fun getAllCustomers(): Flow<List<Customer>>
    fun searchCustomers(query: String): Flow<List<Customer>>
} 