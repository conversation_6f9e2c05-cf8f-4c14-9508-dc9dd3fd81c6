package com.example.sharenshop.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable

// User roles enum for type safety
enum class UserRole(val value: String, val displayName: String) {
    SUPER_ADMIN("super_admin", "مدیر کل"),
    CASHIE<PERSON>("cashier", "فروشنده"),
    CUSTOMER("customer", "مشتری");

    companion object {
        fun fromString(value: String): UserRole {
            return values().find { it.value == value } ?: CUSTOMER
        }

        fun fromValue(value: String): UserRole {
            return fromString(value)
        }
    }
}

@Serializable
@Entity(tableName = "users")
data class User(
    @PrimaryKey(autoGenerate = false)
    val id: String,
    val email: String,
    val username: String,
    val userType: String, // Use UserRole.value
    val createdAt: Long
) {
    // Helper property to get role as enum
    val role: UserRole
        get() = UserRole.fromString(userType)
}