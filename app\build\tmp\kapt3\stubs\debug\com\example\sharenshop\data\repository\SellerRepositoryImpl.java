package com.example.sharenshop.data.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\b\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0096@\u00a2\u0006\u0002\u0010\tJ\u0014\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000bH\u0016J\u0018\u0010\u000e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\r0\u000b2\u0006\u0010\u0007\u001a\u00020\bH\u0016J\u0016\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\rH\u0096@\u00a2\u0006\u0002\u0010\u0011J\u001c\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000b2\u0006\u0010\u0013\u001a\u00020\bH\u0016J\u0016\u0010\u0014\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\rH\u0096@\u00a2\u0006\u0002\u0010\u0011R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0015"}, d2 = {"Lcom/example/sharenshop/data/repository/SellerRepositoryImpl;", "Lcom/example/sharenshop/domain/repository/SellerRepository;", "sellerDao", "Lcom/example/sharenshop/data/local/dao/SellerDao;", "(Lcom/example/sharenshop/data/local/dao/SellerDao;)V", "deleteSellerById", "", "sellerId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllSellers", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/example/sharenshop/data/model/Seller;", "getSellerById", "insertSeller", "seller", "(Lcom/example/sharenshop/data/model/Seller;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchSellers", "query", "updateSeller", "app_debug"})
public final class SellerRepositoryImpl implements com.example.sharenshop.domain.repository.SellerRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.data.local.dao.SellerDao sellerDao = null;
    
    @javax.inject.Inject()
    public SellerRepositoryImpl(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.local.dao.SellerDao sellerDao) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<com.example.sharenshop.data.model.Seller> getSellerById(@org.jetbrains.annotations.NotNull()
    java.lang.String sellerId) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object insertSeller(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Seller seller, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object updateSeller(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Seller seller, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object deleteSellerById(@org.jetbrains.annotations.NotNull()
    java.lang.String sellerId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharenshop.data.model.Seller>> getAllSellers() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharenshop.data.model.Seller>> searchSellers(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
        return null;
    }
}