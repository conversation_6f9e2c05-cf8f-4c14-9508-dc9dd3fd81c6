package com.example.sharenshop.di

import com.example.sharenshop.BuildConfig
// import com.example.sharenshop.data.remote.SupabaseManager // اگر از SupabaseManager استفاده نمی‌کنید، این خط می‌تواند حذف شود یا کامنت بماند
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.github.jan.supabase.SupabaseClient // ایمپورت جدید برای SupabaseClient از کتابخانه رسمی
import io.github.jan.supabase.createSupabaseClient
import io.github.jan.supabase.gotrue.Auth // برای ماژول احراز هویت
import io.github.jan.supabase.postgrest.Postgrest // برای ماژول پایگاه داده (اختیاری، اگر استفاده می‌کنید)
// import io.github.jan.supabase.realtime.Realtime // برای ماژول Realtime (اختیاری)
// import io.github.jan.supabase.storage.Storage // برای ماژول Storage (اختیاری)
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    @Provides
    @Singleton
    fun provideSupabaseClient(): SupabaseClient {
        return createSupabaseClient(
            supabaseUrl = BuildConfig.SUPABASE_URL,
            supabaseKey = BuildConfig.SUPABASE_PUBLIC_KEY
        ) {
            // ماژول‌های مورد نیاز Supabase را اینجا نصب کنید
            install(Auth) {
                // تنظیمات اختیاری برای Auth
                // autoLoadFromStorage = true // برای بارگذاری خودکار جلسه کاربر از حافظه پایدار
                // alwaysAutoRefresh = true // برای رفرش خودکار توکن
            }
            install(Postgrest) {
                // تنظیمات اختیاری برای Postgrest
            }
            // install(Realtime) { /* تنظیمات اختیاری */ }
            // install(Storage) { /* تنظیمات اختیاری */ }

            // TODO: بررسی کنید که آیا URL یا کلید خالی هستند و در این صورت خطا لاگ کنید یا مدیریت خطا انجام دهید
            if (BuildConfig.SUPABASE_URL.isBlank() || BuildConfig.SUPABASE_PUBLIC_KEY.isBlank()) {
                // این لاگ را در Logcat خواهید دید و نشانه‌ای از عدم تنظیم صحیح local.properties است
                android.util.Log.e("NetworkModule", "Supabase URL or Key is blank. Check local.properties and Gradle sync.")
            }
        }
    }

    // اگر قبلاً از یک کلاس SupabaseManager استفاده می‌کردید و می‌خواهید آن را حفظ کنید:
    /*
    @Provides
    @Singleton
    fun provideSupabaseManager(supabaseClient: SupabaseClient): SupabaseManager {
        // return SupabaseManager(supabaseClient) // فرض بر اینکه SupabaseManager شما SupabaseClient را به عنوان پارامتر می‌گیرد
        // یا اگر SupabaseManager خودتان SupabaseClient را می‌سازد، کد بالا را کامنت کنید و کد اصلی خودتان را برای SupabaseManager اینجا قرار دهید
        // اما اطمینان حاصل کنید که از BuildConfig.SUPABASE_URL و BuildConfig.SUPABASE_PUBLIC_KEY استفاده می‌کند.
        // مثال اگر SupabaseManager خودش کلاینت را می‌سازد:
        // return SupabaseManager(
        //     supabaseUrl = BuildConfig.SUPABASE_URL,
        //     supabaseKey = BuildConfig.SUPABASE_PUBLIC_KEY
        // )
    }
    */

    // TODO: Add other network-related dependencies like OkHttpClient with interceptors if needed
}