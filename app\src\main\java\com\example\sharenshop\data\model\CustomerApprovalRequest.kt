package com.example.sharenshop.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable

/**
 * درخواست تایید مشتری توسط فروشنده
 */
@Serializable
@Entity(tableName = "customer_approval_requests")
data class CustomerApprovalRequest(
    @PrimaryKey(autoGenerate = false)
    val id: String,
    val customerEmail: String,
    val customerName: String,
    val customerPhone: String,
    val referrerCode: String, // کد معرف (نام کاربری فروشنده)
    val referrerId: String, // ID فروشنده معرف
    val status: ApprovalStatus,
    val requestDate: Long,
    val responseDate: Long? = null,
    val notes: String? = null
)

/**
 * وضعیت درخواست تایید
 */
@Serializable
enum class ApprovalStatus(val value: String, val displayName: String) {
    PENDING("pending", "در انتظار تایید"),
    APPROVED("approved", "تایید شده"),
    REJECTED("rejected", "رد شده"),
    EXPIRED("expired", "منقضی شده");
    
    companion object {
        fun fromString(value: String): ApprovalStatus {
            return values().find { it.value == value } ?: PENDING
        }
    }
}

/**
 * نوتیفیکیشن برای فروشنده
 */
@Serializable
@Entity(tableName = "notifications")
data class Notification(
    @PrimaryKey(autoGenerate = false)
    val id: String,
    val userId: String, // ID فروشنده
    val type: NotificationType,
    val title: String,
    val message: String,
    val data: String? = null, // JSON data برای اطلاعات اضافی
    val isRead: Boolean = false,
    val createdAt: Long
)

/**
 * انواع نوتیفیکیشن
 */
@Serializable
enum class NotificationType(val value: String, val displayName: String) {
    CUSTOMER_APPROVAL_REQUEST("customer_approval", "درخواست تایید مشتری"),
    CUSTOMER_APPROVED("customer_approved", "مشتری تایید شد"),
    CUSTOMER_REJECTED("customer_rejected", "مشتری رد شد"),
    SYSTEM_MESSAGE("system", "پیام سیستم");
    
    companion object {
        fun fromString(value: String): NotificationType {
            return values().find { it.value == value } ?: SYSTEM_MESSAGE
        }
    }
}

/**
 * اطلاعات کامل درخواست برای نمایش
 */
data class CustomerApprovalRequestDetails(
    val request: CustomerApprovalRequest,
    val referrerName: String,
    val referrerEmail: String
)
