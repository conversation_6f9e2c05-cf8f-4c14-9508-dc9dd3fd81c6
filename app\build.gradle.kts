import java.util.Properties
import java.io.FileInputStream

plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("com.google.dagger.hilt.android")
    id("org.jetbrains.kotlin.kapt")
    id("org.jetbrains.kotlin.plugin.serialization")
}

// Function to load properties from a file
fun getProps(filePath: String): Properties {
    val props = Properties()
    val file = project.rootProject.file(filePath)
    if (file.exists()) {
        props.load(FileInputStream(file))
    } else {
        project.logger.warn("local.properties file not found at ${file.absolutePath}. Ensure it's created in the project root with SUPABASE_URL and SUPABASE_PUBLIC_KEY.")
    }
    return props
}

val localProperties = getProps("local.properties")

android {
    namespace = "com.example.sharenshop"
    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        applicationId = "com.example.sharenshop"
        minSdk = libs.versions.minSdk.get().toInt()
        targetSdk = libs.versions.targetSdk.get().toInt()
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }

        val supabaseUrl = localProperties.getProperty("SUPABASE_URL", "")
        val supabasePublicKey = localProperties.getProperty("SUPABASE_PUBLIC_KEY", "")

        buildConfigField("String", "SUPABASE_URL", "\"$supabaseUrl\"")
        buildConfigField("String", "SUPABASE_PUBLIC_KEY", "\"$supabasePublicKey\"")
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        isCoreLibraryDesugaringEnabled = true
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
    buildFeatures {
        compose = true
        buildConfig = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = libs.versions.composeCompiler.get()
    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
}

dependencies {

    // Android Core
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)

    // DataStore
    implementation(libs.androidx.datastore.preferences)

    // Hilt
    implementation(libs.hilt.android)
    kapt(libs.hilt.compiler)

    // Room
    implementation(libs.bundles.room)
    kapt(libs.room.compiler)

    // Kotlinx Serialization
    implementation(libs.kotlinx.serialization.json)

    // Supabase BOM (برای مدیریت نسخه‌ها)
    implementation(platform(libs.supabase.bom))

    // Supabase
    implementation(libs.supabase.gotrue)
    implementation(libs.supabase.postgrest)
    implementation(libs.supabase.functions)

    // Ktor HTTP client engine (برای Supabase)
    implementation(libs.ktor.client.android)

    // Compose
    implementation(libs.bundles.compose.essentials) // استفاده از باندل جدید
    implementation(libs.androidx.compose.ui.tooling.preview) // این برای implementation است
    debugImplementation(libs.androidx.compose.ui.tooling) // این برای debugImplementation است

    // Navigation and other lifecycle compose
    implementation(libs.navigation.compose)
    implementation(libs.hilt.navigation.compose)
    implementation(libs.lifecycle.runtime.compose)

    // Testing
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(libs.androidx.compose.ui.test.junit4)
    debugImplementation(libs.androidx.compose.ui.test.manifest)

    // Core library desugaring
    coreLibraryDesugaring(libs.android.desugarJdkLibs)
}