package com.example.sharenshop.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.sharenshop.data.model.UserRole
import com.example.sharenshop.presentation.auth.AuthViewModel
import com.example.sharenshop.ui.utils.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AuthScreen(
    viewModel: AuthViewModel = hiltViewModel(),
    onNavigateToMain: () -> Unit = {},
    onNavigateToPendingApproval: () -> Unit = {}
) {
    val email by viewModel.email.collectAsStateWithLifecycle()
    val password by viewModel.password.collectAsStateWithLifecycle()
    val name by viewModel.name.collectAsStateWithLifecycle()
    val phone by viewModel.phone.collectAsStateWithLifecycle()
    val role by viewModel.role.collectAsStateWithLifecycle()
    val referrerCode by viewModel.referrerCode.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    val authError by viewModel.authError.collectAsStateWithLifecycle()
    val isLoginMode by viewModel.isLoginMode.collectAsStateWithLifecycle()
    val isAuthenticated by viewModel.isAuthenticated.collectAsStateWithLifecycle()
    val isCustomerSignupMode by viewModel.isCustomerSignupMode.collectAsStateWithLifecycle()
    val pendingApproval by viewModel.pendingApproval.collectAsStateWithLifecycle()

    // Navigate to main when authenticated
    LaunchedEffect(isAuthenticated) {
        android.util.Log.d("AuthScreen", "LaunchedEffect triggered, isAuthenticated: $isAuthenticated")
        if (isAuthenticated) {
            android.util.Log.d("AuthScreen", "Navigating to main screen...")
            onNavigateToMain()
        }
    }

    // Navigate to pending approval when needed
    LaunchedEffect(pendingApproval) {
        if (pendingApproval) {
            android.util.Log.d("AuthScreen", "Navigating to pending approval screen...")
            onNavigateToPendingApproval()
        }
    }

    val adaptivePadding = getAdaptivePadding()
    val isLandscapeMode = isLandscape()
    val scrollState = rememberScrollState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(adaptivePadding)
            .then(
                if (isLandscapeMode) {
                    Modifier.verticalScroll(scrollState)
                } else {
                    Modifier
                }
            ),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = if (isLandscapeMode) Arrangement.Top else Arrangement.Center
    ) {
        if (isLandscapeMode) {
            Spacer(modifier = Modifier.height(16.dp))
        }
        Text(
            text = when {
                isLoginMode -> "ورود به SharenShop"
                isCustomerSignupMode -> "ثبت نام مشتری"
                else -> "ثبت نام"
            },
            style = MaterialTheme.typography.headlineMedium,
            color = MaterialTheme.colorScheme.secondary // قهوه‌ای تیره
        )

        Spacer(modifier = Modifier.height(16.dp))

        OutlinedTextField(
            value = email,
            onValueChange = { viewModel.onEmailChange(it) },
            label = { Text("ایمیل") },
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(8.dp))

        OutlinedTextField(
            value = password,
            onValueChange = { viewModel.onPasswordChange(it) },
            label = { Text("رمز عبور") },
            visualTransformation = PasswordVisualTransformation(),
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(8.dp))

        // فیلد کد معرف برای مشتریان
        if (isCustomerSignupMode) {
            OutlinedTextField(
                value = referrerCode,
                onValueChange = { viewModel.onReferrerCodeChange(it) },
                label = { Text("کد معرف (نام کاربری فروشنده)") },
                placeholder = { Text("نام کاربری فروشنده معرف") },
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(8.dp))
        }

        if (isCustomerSignupMode) {
            OutlinedTextField(
                value = name,
                onValueChange = { viewModel.onNameChange(it) },
                label = { Text("نام") },
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(8.dp))

            OutlinedTextField(
                value = phone,
                onValueChange = { viewModel.onPhoneChange(it) },
                label = { Text("شماره تلفن") },
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(16.dp))

            // نوتیس برای مشتریان
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.tertiaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "📋 نکته مهم:",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.secondary
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "• کد معرف باید نام کاربری یکی از فروشندگان باشد\n• پس از ثبت نام، درخواست شما برای فروشنده ارسال می‌شود\n• تا تایید فروشنده، امکان ورود ندارید",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onTertiaryContainer
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
        }

        Button(
            onClick = { viewModel.authenticate() },
            modifier = Modifier.fillMaxWidth(),
            enabled = !isLoading,
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.primary // آبی عمیق
            )
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    color = MaterialTheme.colorScheme.onPrimary
                )
            } else {
                Text(
                    text = when {
                        isLoginMode -> "ورود"
                        isCustomerSignupMode -> "ارسال درخواست"
                        else -> "ثبت نام"
                    }
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // دکمه تغییر حالت
        TextButton(
            onClick = { viewModel.toggleAuthMode() },
            colors = ButtonDefaults.textButtonColors(
                contentColor = MaterialTheme.colorScheme.secondary // قهوه‌ای تیره
            )
        ) {
            Text(
                text = when {
                    isLoginMode -> "مشتری هستید؟ ثبت نام کنید"
                    isCustomerSignupMode -> "قبلاً ثبت نام کرده‌اید؟ وارد شوید"
                    else -> "وارد شوید"
                }
            )
        }

        // راهنمای ورود مدیر/فروشنده
        if (isLoginMode) {
            Spacer(modifier = Modifier.height(16.dp))

            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                )
            ) {
                Column(
                    modifier = Modifier.padding(12.dp)
                ) {
                    Text(
                        text = "💼 مدیر یا فروشنده هستید؟",
                        style = MaterialTheme.typography.titleSmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "با ایمیل و رمز عبور از قبل تعریف شده وارد شوید",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
            }
        }

        authError?.let { error ->
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
} 