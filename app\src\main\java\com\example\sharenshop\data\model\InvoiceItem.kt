package com.example.sharenshop.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable
import kotlinx.serialization.Contextual // این خط را اضافه کن
import java.math.BigDecimal

@Serializable
@Entity(tableName = "invoice_items")
data class InvoiceItem(
    @PrimaryKey(autoGenerate = false)
    val id: String,
    val invoiceId: String,
    val productId: String,
    val quantity: Int,
    @Contextual
    val unitPrice: BigDecimal, // Use BigDecimal for unit price
    @Contextual
    val totalPrice: BigDecimal, // quantity * unitPrice (Use BigDecimal for total price)
    val createdAt: Long
)