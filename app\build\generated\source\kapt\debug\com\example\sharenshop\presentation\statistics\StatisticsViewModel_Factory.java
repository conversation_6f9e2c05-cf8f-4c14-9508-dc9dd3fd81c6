// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.presentation.statistics;

import com.example.sharenshop.domain.use_case.StatisticsUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class StatisticsViewModel_Factory implements Factory<StatisticsViewModel> {
  private final Provider<StatisticsUseCases> statisticsUseCasesProvider;

  public StatisticsViewModel_Factory(Provider<StatisticsUseCases> statisticsUseCasesProvider) {
    this.statisticsUseCasesProvider = statisticsUseCasesProvider;
  }

  @Override
  public StatisticsViewModel get() {
    return newInstance(statisticsUseCasesProvider.get());
  }

  public static StatisticsViewModel_Factory create(
      Provider<StatisticsUseCases> statisticsUseCasesProvider) {
    return new StatisticsViewModel_Factory(statisticsUseCasesProvider);
  }

  public static StatisticsViewModel newInstance(StatisticsUseCases statisticsUseCases) {
    return new StatisticsViewModel(statisticsUseCases);
  }
}
