package com.example.sharenshop.domain.use_case;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0018\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B=\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\u0002\u0010\u0010J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010 \u001a\u00020\u0005H\u00c6\u0003J\t\u0010!\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\"\u001a\u00020\tH\u00c6\u0003J\t\u0010#\u001a\u00020\u000bH\u00c6\u0003J\t\u0010$\u001a\u00020\rH\u00c6\u0003J\t\u0010%\u001a\u00020\u000fH\u00c6\u0003JO\u0010&\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u00c6\u0001J\u0013\u0010\'\u001a\u00020(2\b\u0010)\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010*\u001a\u00020+H\u00d6\u0001J\t\u0010,\u001a\u00020-H\u00d6\u0001R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001e\u00a8\u0006."}, d2 = {"Lcom/example/sharenshop/domain/use_case/InvoiceUseCases;", "", "getInvoice", "Lcom/example/sharenshop/domain/use_case/GetInvoice;", "insertInvoice", "Lcom/example/sharenshop/domain/use_case/InsertInvoice;", "updateInvoice", "Lcom/example/sharenshop/domain/use_case/UpdateInvoice;", "deleteInvoice", "Lcom/example/sharenshop/domain/use_case/DeleteInvoice;", "getAllInvoices", "Lcom/example/sharenshop/domain/use_case/GetAllInvoices;", "getInvoicesByCustomer", "Lcom/example/sharenshop/domain/use_case/GetInvoicesByCustomer;", "getInvoicesByUser", "Lcom/example/sharenshop/domain/use_case/GetInvoicesByUser;", "(Lcom/example/sharenshop/domain/use_case/GetInvoice;Lcom/example/sharenshop/domain/use_case/InsertInvoice;Lcom/example/sharenshop/domain/use_case/UpdateInvoice;Lcom/example/sharenshop/domain/use_case/DeleteInvoice;Lcom/example/sharenshop/domain/use_case/GetAllInvoices;Lcom/example/sharenshop/domain/use_case/GetInvoicesByCustomer;Lcom/example/sharenshop/domain/use_case/GetInvoicesByUser;)V", "getDeleteInvoice", "()Lcom/example/sharenshop/domain/use_case/DeleteInvoice;", "getGetAllInvoices", "()Lcom/example/sharenshop/domain/use_case/GetAllInvoices;", "getGetInvoice", "()Lcom/example/sharenshop/domain/use_case/GetInvoice;", "getGetInvoicesByCustomer", "()Lcom/example/sharenshop/domain/use_case/GetInvoicesByCustomer;", "getGetInvoicesByUser", "()Lcom/example/sharenshop/domain/use_case/GetInvoicesByUser;", "getInsertInvoice", "()Lcom/example/sharenshop/domain/use_case/InsertInvoice;", "getUpdateInvoice", "()Lcom/example/sharenshop/domain/use_case/UpdateInvoice;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class InvoiceUseCases {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.GetInvoice getInvoice = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.InsertInvoice insertInvoice = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.UpdateInvoice updateInvoice = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.DeleteInvoice deleteInvoice = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.GetAllInvoices getAllInvoices = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.GetInvoicesByCustomer getInvoicesByCustomer = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.GetInvoicesByUser getInvoicesByUser = null;
    
    public InvoiceUseCases(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetInvoice getInvoice, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.InsertInvoice insertInvoice, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.UpdateInvoice updateInvoice, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.DeleteInvoice deleteInvoice, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetAllInvoices getAllInvoices, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetInvoicesByCustomer getInvoicesByCustomer, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetInvoicesByUser getInvoicesByUser) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetInvoice getGetInvoice() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.InsertInvoice getInsertInvoice() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.UpdateInvoice getUpdateInvoice() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.DeleteInvoice getDeleteInvoice() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetAllInvoices getGetAllInvoices() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetInvoicesByCustomer getGetInvoicesByCustomer() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetInvoicesByUser getGetInvoicesByUser() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetInvoice component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.InsertInvoice component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.UpdateInvoice component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.DeleteInvoice component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetAllInvoices component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetInvoicesByCustomer component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetInvoicesByUser component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.InvoiceUseCases copy(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetInvoice getInvoice, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.InsertInvoice insertInvoice, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.UpdateInvoice updateInvoice, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.DeleteInvoice deleteInvoice, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetAllInvoices getAllInvoices, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetInvoicesByCustomer getInvoicesByCustomer, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetInvoicesByUser getInvoicesByUser) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}