package com.example.sharenshop.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.sharenshop.data.model.*
import com.example.sharenshop.ui.theme.SharenShopColors
import java.math.BigDecimal
import java.text.NumberFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettlementScreen(
    userRole: UserRole = UserRole.CASHIER
) {
    var selectedTab by remember { mutableStateOf(0) }
    val tabs = when (userRole) {
        UserRole.CASHIER -> listOf("تصویه جدید", "درخواست‌های من", "تاریخچه تصویه")
        UserRole.SUPER_ADMIN -> listOf("درخواست‌های تصویه", "تاریخچه تصویه‌ها", "آمار تصویه")
        else -> listOf("تصویه حساب")
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(SharenShopColors.FutureDusk)
            .padding(16.dp)
    ) {
        // Header
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "🏦 مدیریت تصویه حساب",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = SharenShopColors.DeepBlue
                )
                Text(
                    text = when (userRole) {
                        UserRole.CASHIER -> "تصویه پول‌های دریافتی با مدیر"
                        UserRole.SUPER_ADMIN -> "بررسی و تایید درخواست‌های تصویه"
                        else -> "مدیریت تصویه حساب"
                    },
                    fontSize = 14.sp,
                    color = SharenShopColors.SoftGray
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Tab Row
        TabRow(
            selectedTabIndex = selectedTab,
            containerColor = Color.White,
            contentColor = SharenShopColors.DeepBlue
        ) {
            tabs.forEachIndexed { index, title ->
                Tab(
                    selected = selectedTab == index,
                    onClick = { selectedTab = index },
                    text = { Text(title) }
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Content based on selected tab and user role
        when (userRole) {
            UserRole.CASHIER -> {
                when (selectedTab) {
                    0 -> CashierNewSettlementTab()
                    1 -> CashierMyRequestsTab()
                    2 -> CashierSettlementHistoryTab()
                }
            }
            UserRole.SUPER_ADMIN -> {
                when (selectedTab) {
                    0 -> AdminPendingSettlementsTab()
                    1 -> AdminSettlementHistoryTab()
                    2 -> AdminSettlementStatsTab()
                }
            }
            else -> {
                Text(
                    text = "دسترسی محدود",
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
fun CashierNewSettlementTab() {
    var settlementAmount by remember { mutableStateOf("") }
    var settlementType by remember { mutableStateOf(SettlementType.CASH_RECEIVED) }
    var description by remember { mutableStateOf("") }
    var trackingNumber by remember { mutableStateOf("") }

    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // Current Balance Summary
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "💰 موجودی فعلی",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = SharenShopColors.DeepBlue
                    )
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text("پول نقد دریافتی:", fontWeight = FontWeight.Medium)
                        Text(
                            "۸,۵۰۰,۰۰۰ تومان",
                            color = SharenShopColors.ProfitColor,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text("نسیه وصول شده:", fontWeight = FontWeight.Medium)
                        Text(
                            "۳,۲۰۰,۰۰۰ تومان",
                            color = SharenShopColors.Info,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    HorizontalDivider(modifier = Modifier.padding(vertical = 8.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text("تصویه شده:", fontWeight = FontWeight.Medium)
                        Text(
                            "۵,۰۰۰,۰۰۰ تومان",
                            color = SharenShopColors.SoftGray,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text("مانده تصویه:", fontWeight = FontWeight.Bold, fontSize = 16.sp)
                        Text(
                            "۶,۷۰۰,۰۰۰ تومان",
                            color = SharenShopColors.Warning,
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }

        item {
            // Settlement Form
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "📝 درخواست تصویه جدید",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = SharenShopColors.DeepBlue
                    )
                    Spacer(modifier = Modifier.height(16.dp))

                    // Settlement Type
                    Text(
                        text = "نوع تصویه:",
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    Column(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        SettlementType.values().forEach { type ->
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                RadioButton(
                                    selected = settlementType == type,
                                    onClick = { settlementType = type }
                                )
                                Text(
                                    text = type.displayName,
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Settlement Amount
                    OutlinedTextField(
                        value = settlementAmount,
                        onValueChange = { settlementAmount = it },
                        label = { Text("مبلغ تصویه (تومان)") },
                        modifier = Modifier.fillMaxWidth(),
                        leadingIcon = {
                            Icon(Icons.Default.Star, contentDescription = null)
                        }
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    // Tracking Number (optional)
                    OutlinedTextField(
                        value = trackingNumber,
                        onValueChange = { trackingNumber = it },
                        label = { Text("شماره پیگیری (اختیاری)") },
                        modifier = Modifier.fillMaxWidth(),
                        leadingIcon = {
                            Icon(Icons.Default.Star, contentDescription = null)
                        }
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    // Description
                    OutlinedTextField(
                        value = description,
                        onValueChange = { description = it },
                        label = { Text("توضیحات") },
                        modifier = Modifier.fillMaxWidth(),
                        maxLines = 3,
                        leadingIcon = {
                            Icon(Icons.Default.Star, contentDescription = null)
                        }
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Submit Button
                    Button(
                        onClick = { /* Handle settlement submission */ },
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = SharenShopColors.DeepBlue
                        )
                    ) {
                        @Suppress("DEPRECATION")
                        Icon(Icons.Default.Send, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("ارسال درخواست تصویه")
                    }
                }
            }
        }
    }
}

@Composable
fun CashierMyRequestsTab() {
    // Sample settlement requests
    val settlements = remember {
        listOf(
            SettlementDetails(
                settlement = Settlement(
                    id = "1",
                    sellerId = "seller1",
                    managerId = "manager1",
                    amount = BigDecimal("2000000"),
                    settlementType = SettlementType.CASH_RECEIVED,
                    status = SettlementStatus.PENDING,
                    requestDate = System.currentTimeMillis(),
                    createdAt = System.currentTimeMillis()
                ),
                sellerName = "شما",
                managerName = "مدیر کل"
            ),
            SettlementDetails(
                settlement = Settlement(
                    id = "2",
                    sellerId = "seller1",
                    managerId = "manager1",
                    amount = BigDecimal("1500000"),
                    settlementType = SettlementType.CREDIT_COLLECTION,
                    status = SettlementStatus.APPROVED,
                    requestDate = System.currentTimeMillis() - 86400000,
                    responseDate = System.currentTimeMillis() - 43200000,
                    createdAt = System.currentTimeMillis() - 86400000
                ),
                sellerName = "شما",
                managerName = "مدیر کل"
            )
        )
    }

    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(settlements) { settlementDetail ->
            SettlementRequestCard(settlementDetail)
        }
    }
}

@Composable
fun SettlementRequestCard(settlementDetail: SettlementDetails) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = settlementDetail.settlement.settlementType.displayName,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
                
                SettlementStatusChip(settlementDetail.settlement.status)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("مبلغ:")
                Text(
                    text = NumberFormat.getNumberInstance(Locale("fa", "IR"))
                        .format(settlementDetail.settlement.amount) + " تومان",
                    fontWeight = FontWeight.Bold,
                    color = SharenShopColors.DeepBlue
                )
            }
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("تاریخ درخواست:")
                Text("۱۴۰۳/۰۹/۱۵")
            }
            
            if (settlementDetail.settlement.responseDate != null) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("تاریخ پاسخ:")
                    Text("۱۴۰۳/۰۹/۱۶")
                }
            }
            
            if (settlementDetail.settlement.description != null) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "توضیحات: ${settlementDetail.settlement.description}",
                    fontSize = 14.sp,
                    color = SharenShopColors.SoftGray
                )
            }
            
            if (settlementDetail.settlement.rejectionReason != null) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "دلیل رد: ${settlementDetail.settlement.rejectionReason}",
                    fontSize = 14.sp,
                    color = SharenShopColors.LossColor
                )
            }
        }
    }
}

@Composable
fun SettlementStatusChip(status: SettlementStatus) {
    val (backgroundColor, textColor) = when (status) {
        SettlementStatus.PENDING -> SharenShopColors.Warning to Color.White
        SettlementStatus.APPROVED -> SharenShopColors.Success to Color.White
        SettlementStatus.REJECTED -> SharenShopColors.LossColor to Color.White
        SettlementStatus.PARTIALLY_APPROVED -> SharenShopColors.Info to Color.White
    }
    
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundColor)
            .padding(horizontal = 12.dp, vertical = 4.dp)
    ) {
        Text(
            text = status.displayName,
            color = textColor,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
fun CashierSettlementHistoryTab() {
    Text(
        text = "تاریخچه تصویه‌های تایید شده",
        modifier = Modifier.fillMaxWidth(),
        textAlign = TextAlign.Center,
        fontSize = 16.sp,
        color = SharenShopColors.SoftGray
    )
}

@Composable
fun AdminPendingSettlementsTab() {
    // Sample pending settlements for admin review
    val pendingSettlements = remember {
        listOf(
            SettlementDetails(
                settlement = Settlement(
                    id = "1",
                    sellerId = "seller1",
                    managerId = "manager1",
                    amount = BigDecimal("2000000"),
                    settlementType = SettlementType.CASH_RECEIVED,
                    status = SettlementStatus.PENDING,
                    requestDate = System.currentTimeMillis(),
                    description = "تصویه پول نقد دریافتی از مشتریان",
                    createdAt = System.currentTimeMillis()
                ),
                sellerName = "احمد محمدی",
                managerName = "شما"
            )
        )
    }

    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(pendingSettlements) { settlementDetail ->
            AdminSettlementReviewCard(settlementDetail)
        }
    }
}

@Composable
fun AdminSettlementReviewCard(settlementDetail: SettlementDetails) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = settlementDetail.sellerName,
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp
                    )
                    Text(
                        text = settlementDetail.settlement.settlementType.displayName,
                        fontSize = 14.sp,
                        color = SharenShopColors.SoftGray
                    )
                }
                
                Text(
                    text = NumberFormat.getNumberInstance(Locale("fa", "IR"))
                        .format(settlementDetail.settlement.amount) + " تومان",
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp,
                    color = SharenShopColors.DeepBlue
                )
            }
            
            if (settlementDetail.settlement.description != null) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = settlementDetail.settlement.description,
                    fontSize = 14.sp,
                    color = SharenShopColors.SoftGray
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Button(
                    onClick = { /* Handle approval */ },
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = SharenShopColors.Success
                    )
                ) {
                    Icon(Icons.Default.Check, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("تایید")
                }
                
                OutlinedButton(
                    onClick = { /* Handle rejection */ },
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = SharenShopColors.LossColor
                    )
                ) {
                    Icon(Icons.Default.Close, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("رد")
                }
            }
        }
    }
}

@Composable
fun AdminSettlementHistoryTab() {
    Text(
        text = "تاریخچه همه تصویه‌ها",
        modifier = Modifier.fillMaxWidth(),
        textAlign = TextAlign.Center,
        fontSize = 16.sp,
        color = SharenShopColors.SoftGray
    )
}

@Composable
fun AdminSettlementStatsTab() {
    Text(
        text = "آمار و گزارش تصویه‌ها",
        modifier = Modifier.fillMaxWidth(),
        textAlign = TextAlign.Center,
        fontSize = 16.sp,
        color = SharenShopColors.SoftGray
    )
}
