// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.CustomerApprovalRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RejectCustomer_Factory implements Factory<RejectCustomer> {
  private final Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider;

  public RejectCustomer_Factory(
      Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider) {
    this.customerApprovalRepositoryProvider = customerApprovalRepositoryProvider;
  }

  @Override
  public RejectCustomer get() {
    return newInstance(customerApprovalRepositoryProvider.get());
  }

  public static RejectCustomer_Factory create(
      Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider) {
    return new RejectCustomer_Factory(customerApprovalRepositoryProvider);
  }

  public static RejectCustomer newInstance(CustomerApprovalRepository customerApprovalRepository) {
    return new RejectCustomer(customerApprovalRepository);
  }
}
