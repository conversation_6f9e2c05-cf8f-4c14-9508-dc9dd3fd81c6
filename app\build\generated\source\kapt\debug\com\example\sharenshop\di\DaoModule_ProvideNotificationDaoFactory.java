// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.local.dao.NotificationDao;
import com.example.sharenshop.data.local.database.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaoModule_ProvideNotificationDaoFactory implements Factory<NotificationDao> {
  private final Provider<AppDatabase> appDatabaseProvider;

  public DaoModule_ProvideNotificationDaoFactory(Provider<AppDatabase> appDatabaseProvider) {
    this.appDatabaseProvider = appDatabaseProvider;
  }

  @Override
  public NotificationDao get() {
    return provideNotificationDao(appDatabaseProvider.get());
  }

  public static DaoModule_ProvideNotificationDaoFactory create(
      Provider<AppDatabase> appDatabaseProvider) {
    return new DaoModule_ProvideNotificationDaoFactory(appDatabaseProvider);
  }

  public static NotificationDao provideNotificationDao(AppDatabase appDatabase) {
    return Preconditions.checkNotNullFromProvides(DaoModule.INSTANCE.provideNotificationDao(appDatabase));
  }
}
