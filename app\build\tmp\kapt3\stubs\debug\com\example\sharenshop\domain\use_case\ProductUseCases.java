package com.example.sharenshop.domain.use_case;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B5\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\tH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u000bH\u00c6\u0003J\t\u0010 \u001a\u00020\rH\u00c6\u0003JE\u0010!\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u00c6\u0001J\u0013\u0010\"\u001a\u00020#2\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020&H\u00d6\u0001J\t\u0010\'\u001a\u00020(H\u00d6\u0001R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001a\u00a8\u0006)"}, d2 = {"Lcom/example/sharenshop/domain/use_case/ProductUseCases;", "", "getProduct", "Lcom/example/sharenshop/domain/use_case/GetProduct;", "insertProduct", "Lcom/example/sharenshop/domain/use_case/InsertProduct;", "updateProduct", "Lcom/example/sharenshop/domain/use_case/UpdateProduct;", "deleteProduct", "Lcom/example/sharenshop/domain/use_case/DeleteProduct;", "getAllProducts", "Lcom/example/sharenshop/domain/use_case/GetAllProducts;", "searchProducts", "Lcom/example/sharenshop/domain/use_case/SearchProducts;", "(Lcom/example/sharenshop/domain/use_case/GetProduct;Lcom/example/sharenshop/domain/use_case/InsertProduct;Lcom/example/sharenshop/domain/use_case/UpdateProduct;Lcom/example/sharenshop/domain/use_case/DeleteProduct;Lcom/example/sharenshop/domain/use_case/GetAllProducts;Lcom/example/sharenshop/domain/use_case/SearchProducts;)V", "getDeleteProduct", "()Lcom/example/sharenshop/domain/use_case/DeleteProduct;", "getGetAllProducts", "()Lcom/example/sharenshop/domain/use_case/GetAllProducts;", "getGetProduct", "()Lcom/example/sharenshop/domain/use_case/GetProduct;", "getInsertProduct", "()Lcom/example/sharenshop/domain/use_case/InsertProduct;", "getSearchProducts", "()Lcom/example/sharenshop/domain/use_case/SearchProducts;", "getUpdateProduct", "()Lcom/example/sharenshop/domain/use_case/UpdateProduct;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class ProductUseCases {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.GetProduct getProduct = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.InsertProduct insertProduct = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.UpdateProduct updateProduct = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.DeleteProduct deleteProduct = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.GetAllProducts getAllProducts = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.SearchProducts searchProducts = null;
    
    public ProductUseCases(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetProduct getProduct, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.InsertProduct insertProduct, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.UpdateProduct updateProduct, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.DeleteProduct deleteProduct, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetAllProducts getAllProducts, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.SearchProducts searchProducts) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetProduct getGetProduct() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.InsertProduct getInsertProduct() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.UpdateProduct getUpdateProduct() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.DeleteProduct getDeleteProduct() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetAllProducts getGetAllProducts() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.SearchProducts getSearchProducts() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetProduct component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.InsertProduct component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.UpdateProduct component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.DeleteProduct component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetAllProducts component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.SearchProducts component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.ProductUseCases copy(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetProduct getProduct, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.InsertProduct insertProduct, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.UpdateProduct updateProduct, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.DeleteProduct deleteProduct, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetAllProducts getAllProducts, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.SearchProducts searchProducts) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}