package com.example.sharenshop.data.model;

/**
 * System permissions for different user roles
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b*\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0005R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007j\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013j\u0002\b\u0014j\u0002\b\u0015j\u0002\b\u0016j\u0002\b\u0017j\u0002\b\u0018j\u0002\b\u0019j\u0002\b\u001aj\u0002\b\u001bj\u0002\b\u001cj\u0002\b\u001dj\u0002\b\u001ej\u0002\b\u001fj\u0002\b j\u0002\b!j\u0002\b\"j\u0002\b#j\u0002\b$j\u0002\b%j\u0002\b&j\u0002\b\'j\u0002\b(j\u0002\b)j\u0002\b*j\u0002\b+j\u0002\b,\u00a8\u0006-"}, d2 = {"Lcom/example/sharenshop/data/model/Permission;", "", "value", "", "description", "(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V", "getDescription", "()Ljava/lang/String;", "getValue", "VIEW_USERS", "CREATE_USERS", "EDIT_USERS", "DELETE_USERS", "VIEW_PRODUCTS", "CREATE_PRODUCTS", "EDIT_PRODUCTS", "DELETE_PRODUCTS", "EDIT_PRODUCT_PRICES", "EDIT_PRODUCT_STOCK", "VIEW_CUSTOMERS", "CREATE_CUSTOMERS", "EDIT_CUSTOMERS", "DELETE_CUSTOMERS", "VIEW_CUSTOMER_BALANCE", "VIEW_SELLERS", "CREATE_SELLERS", "EDIT_SELLERS", "DELETE_SELLERS", "VIEW_INVOICES", "CREATE_INVOICES", "EDIT_INVOICES", "DELETE_INVOICES", "VIEW_ALL_INVOICES", "VIEW_OWN_INVOICES", "VIEW_STATISTICS", "VIEW_DETAILED_REPORTS", "VIEW_FINANCIAL_REPORTS", "VIEW_ALL_SELLERS_STATS", "VIEW_CASH_SALES", "VIEW_CREDIT_SALES", "VIEW_INVOICE_COUNT", "VIEW_CUSTOMER_ADDITIONS", "MANAGE_SETTINGS", "MANAGE_SECURITY", "BACKUP_RESTORE", "app_debug"})
public enum Permission {
    /*public static final*/ VIEW_USERS /* = new VIEW_USERS(null, null) */,
    /*public static final*/ CREATE_USERS /* = new CREATE_USERS(null, null) */,
    /*public static final*/ EDIT_USERS /* = new EDIT_USERS(null, null) */,
    /*public static final*/ DELETE_USERS /* = new DELETE_USERS(null, null) */,
    /*public static final*/ VIEW_PRODUCTS /* = new VIEW_PRODUCTS(null, null) */,
    /*public static final*/ CREATE_PRODUCTS /* = new CREATE_PRODUCTS(null, null) */,
    /*public static final*/ EDIT_PRODUCTS /* = new EDIT_PRODUCTS(null, null) */,
    /*public static final*/ DELETE_PRODUCTS /* = new DELETE_PRODUCTS(null, null) */,
    /*public static final*/ EDIT_PRODUCT_PRICES /* = new EDIT_PRODUCT_PRICES(null, null) */,
    /*public static final*/ EDIT_PRODUCT_STOCK /* = new EDIT_PRODUCT_STOCK(null, null) */,
    /*public static final*/ VIEW_CUSTOMERS /* = new VIEW_CUSTOMERS(null, null) */,
    /*public static final*/ CREATE_CUSTOMERS /* = new CREATE_CUSTOMERS(null, null) */,
    /*public static final*/ EDIT_CUSTOMERS /* = new EDIT_CUSTOMERS(null, null) */,
    /*public static final*/ DELETE_CUSTOMERS /* = new DELETE_CUSTOMERS(null, null) */,
    /*public static final*/ VIEW_CUSTOMER_BALANCE /* = new VIEW_CUSTOMER_BALANCE(null, null) */,
    /*public static final*/ VIEW_SELLERS /* = new VIEW_SELLERS(null, null) */,
    /*public static final*/ CREATE_SELLERS /* = new CREATE_SELLERS(null, null) */,
    /*public static final*/ EDIT_SELLERS /* = new EDIT_SELLERS(null, null) */,
    /*public static final*/ DELETE_SELLERS /* = new DELETE_SELLERS(null, null) */,
    /*public static final*/ VIEW_INVOICES /* = new VIEW_INVOICES(null, null) */,
    /*public static final*/ CREATE_INVOICES /* = new CREATE_INVOICES(null, null) */,
    /*public static final*/ EDIT_INVOICES /* = new EDIT_INVOICES(null, null) */,
    /*public static final*/ DELETE_INVOICES /* = new DELETE_INVOICES(null, null) */,
    /*public static final*/ VIEW_ALL_INVOICES /* = new VIEW_ALL_INVOICES(null, null) */,
    /*public static final*/ VIEW_OWN_INVOICES /* = new VIEW_OWN_INVOICES(null, null) */,
    /*public static final*/ VIEW_STATISTICS /* = new VIEW_STATISTICS(null, null) */,
    /*public static final*/ VIEW_DETAILED_REPORTS /* = new VIEW_DETAILED_REPORTS(null, null) */,
    /*public static final*/ VIEW_FINANCIAL_REPORTS /* = new VIEW_FINANCIAL_REPORTS(null, null) */,
    /*public static final*/ VIEW_ALL_SELLERS_STATS /* = new VIEW_ALL_SELLERS_STATS(null, null) */,
    /*public static final*/ VIEW_CASH_SALES /* = new VIEW_CASH_SALES(null, null) */,
    /*public static final*/ VIEW_CREDIT_SALES /* = new VIEW_CREDIT_SALES(null, null) */,
    /*public static final*/ VIEW_INVOICE_COUNT /* = new VIEW_INVOICE_COUNT(null, null) */,
    /*public static final*/ VIEW_CUSTOMER_ADDITIONS /* = new VIEW_CUSTOMER_ADDITIONS(null, null) */,
    /*public static final*/ MANAGE_SETTINGS /* = new MANAGE_SETTINGS(null, null) */,
    /*public static final*/ MANAGE_SECURITY /* = new MANAGE_SECURITY(null, null) */,
    /*public static final*/ BACKUP_RESTORE /* = new BACKUP_RESTORE(null, null) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String value = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String description = null;
    
    Permission(java.lang.String value, java.lang.String description) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getValue() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.sharenshop.data.model.Permission> getEntries() {
        return null;
    }
}