package com.example.sharenshop.data.local

import android.content.Context
import android.content.SharedPreferences
import com.example.sharenshop.data.model.UserRole
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SessionManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private val prefs: SharedPreferences = context.getSharedPreferences(
        PREFS_NAME, Context.MODE_PRIVATE
    )

    companion object {
        private const val PREFS_NAME = "sharenshop_session"
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
        private const val KEY_USER_EMAIL = "user_email"
        private const val KEY_USER_NAME = "user_name"
        private const val KEY_USER_ROLE = "user_role"
        private const val KEY_USER_ID = "user_id"
        private const val KEY_ACCESS_TOKEN = "access_token"
        private const val KEY_REFRESH_TOKEN = "refresh_token"
        private const val KEY_LOGIN_TIME = "login_time"
    }

    // ذخیره session کاربر
    fun saveUserSession(
        email: String,
        name: String,
        role: UserRole,
        userId: String,
        accessToken: String? = null,
        refreshToken: String? = null
    ) {
        android.util.Log.d("SessionManager", "💾 Saving session for: $email with role: ${role.value}")

        prefs.edit().apply {
            putBoolean(KEY_IS_LOGGED_IN, true)
            putString(KEY_USER_EMAIL, email)
            putString(KEY_USER_NAME, name)
            putString(KEY_USER_ROLE, role.value)
            putString(KEY_USER_ID, userId)
            putString(KEY_ACCESS_TOKEN, accessToken)
            putString(KEY_REFRESH_TOKEN, refreshToken)
            putLong(KEY_LOGIN_TIME, System.currentTimeMillis())
            apply()
        }

        android.util.Log.d("SessionManager", "✅ Session saved successfully")
    }

    // بررسی وضعیت login
    fun isLoggedIn(): Boolean {
        return prefs.getBoolean(KEY_IS_LOGGED_IN, false)
    }

    // دریافت اطلاعات کاربر
    fun getUserEmail(): String? {
        return prefs.getString(KEY_USER_EMAIL, null)
    }

    fun getUserName(): String? {
        return prefs.getString(KEY_USER_NAME, null)
    }

    fun getUserRole(): UserRole? {
        val roleValue = prefs.getString(KEY_USER_ROLE, null)
        val role = roleValue?.let { UserRole.fromValue(it) }
        android.util.Log.d("SessionManager", "🔍 Getting role: $roleValue -> $role")
        return role
    }

    fun getUserId(): String? {
        return prefs.getString(KEY_USER_ID, null)
    }

    fun getAccessToken(): String? {
        return prefs.getString(KEY_ACCESS_TOKEN, null)
    }

    fun getRefreshToken(): String? {
        return prefs.getString(KEY_REFRESH_TOKEN, null)
    }

    fun getLoginTime(): Long {
        return prefs.getLong(KEY_LOGIN_TIME, 0)
    }

    // بررسی انقضای session (اختیاری - 30 روز)
    fun isSessionExpired(): Boolean {
        val loginTime = getLoginTime()
        val currentTime = System.currentTimeMillis()
        val thirtyDaysInMillis = 30L * 24 * 60 * 60 * 1000 // 30 روز
        return (currentTime - loginTime) > thirtyDaysInMillis
    }

    // پاک کردن session (logout)
    fun clearSession() {
        prefs.edit().clear().apply()
    }

    // بروزرسانی token ها
    fun updateTokens(accessToken: String?, refreshToken: String?) {
        prefs.edit().apply {
            putString(KEY_ACCESS_TOKEN, accessToken)
            putString(KEY_REFRESH_TOKEN, refreshToken)
            apply()
        }
    }

    // بررسی کامل session
    fun isValidSession(): Boolean {
        return isLoggedIn() && !isSessionExpired() && 
               getUserEmail() != null && getUserRole() != null
    }
}
