// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.repository;

import com.example.sharenshop.data.local.dao.InvoiceDao;
import com.example.sharenshop.data.local.dao.InvoiceItemDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class InvoiceRepositoryImpl_Factory implements Factory<InvoiceRepositoryImpl> {
  private final Provider<InvoiceDao> invoiceDaoProvider;

  private final Provider<InvoiceItemDao> invoiceItemDaoProvider;

  public InvoiceRepositoryImpl_Factory(Provider<InvoiceDao> invoiceDaoProvider,
      Provider<InvoiceItemDao> invoiceItemDaoProvider) {
    this.invoiceDaoProvider = invoiceDaoProvider;
    this.invoiceItemDaoProvider = invoiceItemDaoProvider;
  }

  @Override
  public InvoiceRepositoryImpl get() {
    return newInstance(invoiceDaoProvider.get(), invoiceItemDaoProvider.get());
  }

  public static InvoiceRepositoryImpl_Factory create(Provider<InvoiceDao> invoiceDaoProvider,
      Provider<InvoiceItemDao> invoiceItemDaoProvider) {
    return new InvoiceRepositoryImpl_Factory(invoiceDaoProvider, invoiceItemDaoProvider);
  }

  public static InvoiceRepositoryImpl newInstance(InvoiceDao invoiceDao,
      InvoiceItemDao invoiceItemDao) {
    return new InvoiceRepositoryImpl(invoiceDao, invoiceItemDao);
  }
}
