package com.example.sharenshop.utils;

/**
 * Helper class for checking user permissions
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\"\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0011\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006J\u0010\u0010\u0007\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006J\u0010\u0010\b\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006J\u0010\u0010\t\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006J\u0010\u0010\n\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006J\u0010\u0010\u000b\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006J\u0010\u0010\f\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006J\u0010\u0010\r\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006J\u0010\u0010\u000e\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006J\u0010\u0010\u000f\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006J\u0016\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u00112\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006J)\u0010\u0013\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u00062\u0012\u0010\u0014\u001a\n\u0012\u0006\b\u0001\u0012\u00020\u00120\u0015\"\u00020\u0012\u00a2\u0006\u0002\u0010\u0016J)\u0010\u0017\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u00062\u0012\u0010\u0014\u001a\n\u0012\u0006\b\u0001\u0012\u00020\u00120\u0015\"\u00020\u0012\u00a2\u0006\u0002\u0010\u0016J\u0018\u0010\u0018\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0019\u001a\u00020\u0012J\u0016\u0010\u0018\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u0019\u001a\u00020\u0012J\u0010\u0010\u001c\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006J\u0010\u0010\u001d\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006J\u0010\u0010\u001e\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a8\u0006\u001f"}, d2 = {"Lcom/example/sharenshop/utils/PermissionHelper;", "", "()V", "canManageInvoices", "", "user", "Lcom/example/sharenshop/data/model/User;", "canManageProducts", "canManageSettings", "canManageUsers", "canViewAllSellersStats", "canViewCashSales", "canViewCreditSales", "canViewCustomerAdditions", "canViewInvoiceCount", "canViewStatistics", "getUserPermissions", "", "Lcom/example/sharenshop/data/model/Permission;", "hasAllPermissions", "permissions", "", "(Lcom/example/sharenshop/data/model/User;[Lcom/example/sharenshop/data/model/Permission;)Z", "hasAnyPermission", "hasPermission", "permission", "userRole", "Lcom/example/sharenshop/data/model/UserRole;", "isCashier", "isCustomer", "isSuperAdmin", "app_debug"})
public final class PermissionHelper {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.sharenshop.utils.PermissionHelper INSTANCE = null;
    
    private PermissionHelper() {
        super();
    }
    
    /**
     * Check if a user has a specific permission
     */
    public final boolean hasPermission(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Permission permission) {
        return false;
    }
    
    /**
     * Check if a user role has a specific permission
     */
    public final boolean hasPermission(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Permission permission) {
        return false;
    }
    
    /**
     * Check if a user has any of the specified permissions
     */
    public final boolean hasAnyPermission(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Permission... permissions) {
        return false;
    }
    
    /**
     * Check if a user has all of the specified permissions
     */
    public final boolean hasAllPermissions(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Permission... permissions) {
        return false;
    }
    
    /**
     * Get all permissions for a user
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Set<com.example.sharenshop.data.model.Permission> getUserPermissions(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user) {
        return null;
    }
    
    /**
     * Check if user is super admin
     */
    public final boolean isSuperAdmin(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user) {
        return false;
    }
    
    /**
     * Check if user is cashier
     */
    public final boolean isCashier(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user) {
        return false;
    }
    
    /**
     * Check if user is customer
     */
    public final boolean isCustomer(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user) {
        return false;
    }
    
    /**
     * Check if user can manage users
     */
    public final boolean canManageUsers(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user) {
        return false;
    }
    
    /**
     * Check if user can manage products
     */
    public final boolean canManageProducts(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user) {
        return false;
    }
    
    /**
     * Check if user can manage invoices
     */
    public final boolean canManageInvoices(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user) {
        return false;
    }
    
    /**
     * Check if user can view statistics
     */
    public final boolean canViewStatistics(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user) {
        return false;
    }
    
    /**
     * Check if user can manage settings
     */
    public final boolean canManageSettings(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user) {
        return false;
    }
    
    /**
     * Check if user can view all sellers statistics
     */
    public final boolean canViewAllSellersStats(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user) {
        return false;
    }
    
    /**
     * Check if user can view cash sales
     */
    public final boolean canViewCashSales(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user) {
        return false;
    }
    
    /**
     * Check if user can view credit sales
     */
    public final boolean canViewCreditSales(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user) {
        return false;
    }
    
    /**
     * Check if user can view invoice count
     */
    public final boolean canViewInvoiceCount(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user) {
        return false;
    }
    
    /**
     * Check if user can view customer additions
     */
    public final boolean canViewCustomerAdditions(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.User user) {
        return false;
    }
}