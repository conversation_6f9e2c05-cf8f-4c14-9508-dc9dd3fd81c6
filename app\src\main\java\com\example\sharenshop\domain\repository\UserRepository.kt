package com.example.sharenshop.domain.repository

import com.example.sharenshop.data.model.User
import kotlinx.coroutines.flow.Flow

interface UserRepository {
    fun getUserById(userId: String): Flow<User?>
    suspend fun insertUser(user: User)
    suspend fun updateUser(user: User)
    suspend fun deleteUserById(userId: String)
    fun getAllUsers(): Flow<List<User>>
    fun getUsersByUserType(userType: String): Flow<List<User>>

    suspend fun getUserByUsername(username: String): User?

    suspend fun getUserByEmail(email: String): User?
}