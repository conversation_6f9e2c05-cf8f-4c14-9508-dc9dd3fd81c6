package com.example.sharenshop.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable
import kotlinx.serialization.Contextual // این خط را اضافه کن
import java.math.BigDecimal

@Serializable
@Entity(tableName = "sellers")
data class Seller(
    @PrimaryKey(autoGenerate = false)
    val id: String,
    val name: String,
    val contactPhone: String? = null,
    val contactEmail: String? = null,
    val address: String? = null,
    @Contextual
    val commissionRate: BigDecimal = BigDecimal.ZERO, // Use BigDecimal for commission rate
    val createdAt: Long,
    val updatedAt: Long? = null
)