package com.example.sharenshop.data.repository;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\b\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\'\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010\u000e\u001a\u00020\u000fH\u0016J\u0016\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010\u0011\u001a\u00020\u000fH\u0016J\u001e\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0014H\u0016J\"\u0010\u0016\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00180\u00170\f2\u0006\u0010\u0019\u001a\u00020\u0018H\u0016J\u000e\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00180\fH\u0016J\u000e\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\r0\fH\u0016R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/example/sharenshop/data/repository/StatisticsRepositoryImpl;", "Lcom/example/sharenshop/domain/repository/StatisticsRepository;", "invoiceDao", "Lcom/example/sharenshop/data/local/dao/InvoiceDao;", "invoiceItemDao", "Lcom/example/sharenshop/data/local/dao/InvoiceItemDao;", "productDao", "Lcom/example/sharenshop/data/local/dao/ProductDao;", "customerDao", "Lcom/example/sharenshop/data/local/dao/CustomerDao;", "(Lcom/example/sharenshop/data/local/dao/InvoiceDao;Lcom/example/sharenshop/data/local/dao/InvoiceItemDao;Lcom/example/sharenshop/data/local/dao/ProductDao;Lcom/example/sharenshop/data/local/dao/CustomerDao;)V", "getCustomerSpending", "Lkotlinx/coroutines/flow/Flow;", "Ljava/math/BigDecimal;", "customerId", "", "getSalesByPaymentStatus", "status", "getSalesByTimePeriod", "startTime", "", "endTime", "getTopSellingProducts", "", "", "limit", "getTotalProductsSold", "getTotalSalesAmount", "app_debug"})
public final class StatisticsRepositoryImpl implements com.example.sharenshop.domain.repository.StatisticsRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.data.local.dao.InvoiceDao invoiceDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.data.local.dao.InvoiceItemDao invoiceItemDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.data.local.dao.ProductDao productDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.data.local.dao.CustomerDao customerDao = null;
    
    @javax.inject.Inject()
    public StatisticsRepositoryImpl(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.local.dao.InvoiceDao invoiceDao, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.local.dao.InvoiceItemDao invoiceItemDao, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.local.dao.ProductDao productDao, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.local.dao.CustomerDao customerDao) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.math.BigDecimal> getTotalSalesAmount() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.util.Map<java.lang.String, java.lang.Integer>> getTopSellingProducts(int limit) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.math.BigDecimal> getCustomerSpending(@org.jetbrains.annotations.NotNull()
    java.lang.String customerId) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.math.BigDecimal> getSalesByPaymentStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String status) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.math.BigDecimal> getSalesByTimePeriod(long startTime, long endTime) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.lang.Integer> getTotalProductsSold() {
        return null;
    }
}