package com.example.sharenshop.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable
import kotlinx.serialization.Contextual // این خط را اضافه کن
import java.math.BigDecimal

@Serializable
@Entity(tableName = "invoices")
data class Invoice(
    @PrimaryKey(autoGenerate = false)
    val id: String,
    val customerId: String,
    val userId: String, // User who created the invoice (e.g., cashier)
    val invoiceDate: Long,
    @Contextual
    val totalAmount: BigDecimal, // Use BigDecimal for monetary values
    @Contextual
    val discountAmount: BigDecimal = BigDecimal.ZERO,
    @Contextual
    val finalAmount: BigDecimal, // totalAmount - discountAmount
    val paymentStatus: String, // وضعیت پرداخت فاکتور
    val paymentType: String, // نوع پرداخت فاکتور
    @Contextual
    val paidAmount: BigDecimal = BigDecimal.ZERO, // مبلغ پرداخت شده
    @Contextual
    val remainingAmount: BigDecimal, // مبلغ باقی‌مانده
    val notes: String? = null,
    val createdAt: Long,
    val updatedAt: Long? = null
)

/**
 * وضعیت پرداخت فاکتور
 */
@Serializable
enum class InvoicePaymentStatus(val value: String, val displayName: String) {
    PENDING("pending", "در انتظار پرداخت"),
    PARTIAL("partial", "پرداخت جزئی"),
    PAID("paid", "پرداخت شده"),
    OVERDUE("overdue", "معوقه");

    companion object {
        fun fromString(value: String): InvoicePaymentStatus {
            return values().find { it.value == value } ?: PENDING
        }
    }
}

/**
 * نوع پرداخت فاکتور
 */
@Serializable
enum class InvoicePaymentType(val value: String, val displayName: String) {
    CASH("cash", "نقدی"),
    CREDIT("credit", "نسیه"),
    MIXED("mixed", "ترکیبی");

    companion object {
        fun fromString(value: String): InvoicePaymentType {
            return values().find { it.value == value } ?: CREDIT
        }
    }
}