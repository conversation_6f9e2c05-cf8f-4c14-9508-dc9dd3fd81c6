package com.example.sharenshop.di

import com.example.sharenshop.domain.repository.CustomerRepository
import com.example.sharenshop.domain.repository.InvoiceRepository
import com.example.sharenshop.domain.repository.ProductRepository
import com.example.sharenshop.domain.repository.SellerRepository
import com.example.sharenshop.domain.repository.UserRepository
import com.example.sharenshop.domain.repository.SecurityRepository
import com.example.sharenshop.domain.repository.SettingsRepository
import com.example.sharenshop.domain.repository.StatisticsRepository
import com.example.sharenshop.domain.use_case.CustomerUseCases
import com.example.sharenshop.domain.use_case.DeleteCustomer
import com.example.sharenshop.domain.use_case.DeleteInvoice
import com.example.sharenshop.domain.use_case.DeleteProduct
import com.example.sharenshop.domain.use_case.DeleteSeller
import com.example.sharenshop.domain.use_case.DeleteUser
import com.example.sharenshop.domain.use_case.GetAllCustomers
import com.example.sharenshop.domain.use_case.GetAllInvoices
import com.example.sharenshop.domain.use_case.GetAllProducts
import com.example.sharenshop.domain.use_case.GetAllSellers
import com.example.sharenshop.domain.use_case.GetAllUsers
import com.example.sharenshop.domain.use_case.GetCustomer
import com.example.sharenshop.domain.use_case.GetInvoice
import com.example.sharenshop.domain.use_case.GetInvoicesByCustomer
import com.example.sharenshop.domain.use_case.GetInvoicesByUser
import com.example.sharenshop.domain.use_case.GetProduct
import com.example.sharenshop.domain.use_case.GetSeller
import com.example.sharenshop.domain.use_case.GetUser
import com.example.sharenshop.domain.use_case.InsertCustomer
import com.example.sharenshop.domain.use_case.InsertInvoice
import com.example.sharenshop.domain.use_case.InsertProduct
import com.example.sharenshop.domain.use_case.InsertSeller
import com.example.sharenshop.domain.use_case.InsertUser
import com.example.sharenshop.domain.use_case.InvoiceUseCases
import com.example.sharenshop.domain.use_case.ProductUseCases
import com.example.sharenshop.domain.use_case.SellerUseCases
import com.example.sharenshop.domain.use_case.UpdateCustomer
import com.example.sharenshop.domain.use_case.UpdateInvoice
import com.example.sharenshop.domain.use_case.UpdateProduct
import com.example.sharenshop.domain.use_case.UpdateSeller
import com.example.sharenshop.domain.use_case.UpdateUser
import com.example.sharenshop.domain.use_case.UserUseCases
import com.example.sharenshop.domain.use_case.GetUsersByType
import com.example.sharenshop.domain.use_case.SearchProducts
import com.example.sharenshop.domain.use_case.SearchCustomers
import com.example.sharenshop.domain.use_case.SearchSellers
import com.example.sharenshop.domain.use_case.SecurityUseCases
import com.example.sharenshop.domain.use_case.DecryptData
import com.example.sharenshop.domain.use_case.EncryptData
import com.example.sharenshop.domain.use_case.GetEncryptionSettings
import com.example.sharenshop.domain.use_case.SaveEncryptionSettings
import com.example.sharenshop.domain.use_case.SettingsUseCases
import com.example.sharenshop.domain.use_case.GetAppSettings
import com.example.sharenshop.domain.use_case.SaveAppSettings
import com.example.sharenshop.domain.use_case.StatisticsUseCases
import com.example.sharenshop.domain.use_case.GetTotalSalesAmount
import com.example.sharenshop.domain.use_case.GetTotalProductsSold
import com.example.sharenshop.domain.use_case.GetTopSellingProducts
import com.example.sharenshop.domain.use_case.GetCustomerSpending
import com.example.sharenshop.domain.use_case.GetSalesByPaymentStatus
import com.example.sharenshop.domain.use_case.GetSalesByTimePeriod
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object UseCaseModule {

    @Provides
    @Singleton
    fun provideUserUseCases(repository: UserRepository): UserUseCases {
        return UserUseCases(
            getUser = GetUser(repository),
            insertUser = InsertUser(repository),
            updateUser = UpdateUser(repository),
            deleteUser = DeleteUser(repository),
            getAllUsers = GetAllUsers(repository),
            getUsersByType = GetUsersByType(repository)
        )
    }

    @Provides
    @Singleton
    fun provideProductUseCases(repository: ProductRepository): ProductUseCases {
        return ProductUseCases(
            getProduct = GetProduct(repository),
            insertProduct = InsertProduct(repository),
            updateProduct = UpdateProduct(repository),
            deleteProduct = DeleteProduct(repository),
            getAllProducts = GetAllProducts(repository),
            searchProducts = SearchProducts(repository)
        )
    }

    @Provides
    @Singleton
    fun provideCustomerUseCases(repository: CustomerRepository): CustomerUseCases {
        return CustomerUseCases(
            getCustomer = GetCustomer(repository),
            insertCustomer = InsertCustomer(repository),
            updateCustomer = UpdateCustomer(repository),
            deleteCustomer = DeleteCustomer(repository),
            getAllCustomers = GetAllCustomers(repository),
            searchCustomers = SearchCustomers(repository)
        )
    }

    @Provides
    @Singleton
    fun provideSellerUseCases(repository: SellerRepository): SellerUseCases {
        return SellerUseCases(
            getSeller = GetSeller(repository),
            insertSeller = InsertSeller(repository),
            updateSeller = UpdateSeller(repository),
            deleteSeller = DeleteSeller(repository),
            getAllSellers = GetAllSellers(repository),
            searchSellers = SearchSellers(repository)
        )
    }

    @Provides
    @Singleton
    fun provideInvoiceUseCases(repository: InvoiceRepository): InvoiceUseCases {
        return InvoiceUseCases(
            getInvoice = GetInvoice(repository),
            insertInvoice = InsertInvoice(repository),
            updateInvoice = UpdateInvoice(repository),
            deleteInvoice = DeleteInvoice(repository),
            getAllInvoices = GetAllInvoices(repository),
            getInvoicesByCustomer = GetInvoicesByCustomer(repository),
            getInvoicesByUser = GetInvoicesByUser(repository)
        )
    }

    @Provides
    @Singleton
    fun provideSecurityUseCases(repository: SecurityRepository): SecurityUseCases {
        return SecurityUseCases(
            getEncryptionSettings = GetEncryptionSettings(repository),
            saveEncryptionSettings = SaveEncryptionSettings(repository),
            encryptData = EncryptData(repository),
            decryptData = DecryptData(repository)
        )
    }

    @Provides
    @Singleton
    fun provideSettingsUseCases(repository: SettingsRepository): SettingsUseCases {
        return SettingsUseCases(
            getAppSettings = GetAppSettings(repository),
            saveAppSettings = SaveAppSettings(repository)
        )
    }

    @Provides
    @Singleton
    fun provideStatisticsUseCases(repository: StatisticsRepository): StatisticsUseCases {
        return StatisticsUseCases(
            getTotalSalesAmount = GetTotalSalesAmount(repository),
            getTotalProductsSold = GetTotalProductsSold(repository),
            getTopSellingProducts = GetTopSellingProducts(repository),
            getCustomerSpending = GetCustomerSpending(repository),
            getSalesByPaymentStatus = GetSalesByPaymentStatus(repository),
            getSalesByTimePeriod = GetSalesByTimePeriod(repository)
        )
    }
} 