-- تنظیم Row Level Security برای SharenShop
-- اجرا کنید در SQL Editor پنل Supabase

-- 1. فعال کردن RLS برای تمام جداول
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE sellers ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoice_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_approval_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- 2. Policy برای جدول users
-- مدیر کل: دسترسی کامل
CREATE POLICY "Super admin full access to users" ON users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND user_type = 'super_admin'
        )
    );

-- فروشندگان: فقط مشاهده
CREATE POLICY "Cashiers can view users" ON users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND user_type IN ('cashier', 'super_admin')
        )
    );

-- مشتریان: فقط اطلاعات خود
CREATE POLICY "Customers can view own data" ON users
    FOR SELECT USING (id = auth.uid());

-- 3. Policy برای جدول products
-- همه می‌توانند محصولات را ببینند
CREATE POLICY "Everyone can view products" ON products
    FOR SELECT USING (true);

-- فقط مدیر کل و فروشندگان می‌توانند محصولات را مدیریت کنند
CREATE POLICY "Admin and cashiers can manage products" ON products
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND user_type IN ('super_admin', 'cashier')
        )
    );

-- 4. Policy برای جدول customers
-- مدیر کل: دسترسی کامل
CREATE POLICY "Super admin full access to customers" ON customers
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND user_type = 'super_admin'
        )
    );

-- فروشندگان: دسترسی کامل
CREATE POLICY "Cashiers full access to customers" ON customers
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND user_type = 'cashier'
        )
    );

-- 5. Policy برای جدول invoices
-- مدیر کل: دسترسی کامل
CREATE POLICY "Super admin full access to invoices" ON invoices
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND user_type = 'super_admin'
        )
    );

-- فروشندگان: فقط فاکتورهای خود
CREATE POLICY "Cashiers can manage own invoices" ON invoices
    FOR ALL USING (
        user_id = auth.uid() OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND user_type = 'super_admin'
        )
    );

-- مشتریان: فقط فاکتورهای خود
CREATE POLICY "Customers can view own invoices" ON invoices
    FOR SELECT USING (
        customer_id IN (
            SELECT id FROM customers 
            WHERE email = (SELECT email FROM auth.users WHERE id = auth.uid())
        )
    );

-- 6. Policy برای جدول invoice_items
-- مدیر کل: دسترسی کامل
CREATE POLICY "Super admin full access to invoice_items" ON invoice_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND user_type = 'super_admin'
        )
    );

-- فروشندگان: فقط آیتم‌های فاکتورهای خود
CREATE POLICY "Cashiers can manage own invoice items" ON invoice_items
    FOR ALL USING (
        invoice_id IN (
            SELECT id FROM invoices WHERE user_id = auth.uid()
        ) OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND user_type = 'super_admin'
        )
    );

-- مشتریان: فقط آیتم‌های فاکتورهای خود
CREATE POLICY "Customers can view own invoice items" ON invoice_items
    FOR SELECT USING (
        invoice_id IN (
            SELECT i.id FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            WHERE c.email = (SELECT email FROM auth.users WHERE id = auth.uid())
        )
    );

-- 7. Policy برای جدول customer_approval_requests
-- مدیر کل: دسترسی کامل
CREATE POLICY "Super admin full access to approval requests" ON customer_approval_requests
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND user_type = 'super_admin'
        )
    );

-- فروشندگان: فقط درخواست‌های مربوط به خود
CREATE POLICY "Cashiers can manage own approval requests" ON customer_approval_requests
    FOR ALL USING (
        referrer_id = auth.uid() OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND user_type = 'super_admin'
        )
    );

-- 8. Policy برای جدول notifications
-- مدیر کل: دسترسی کامل
CREATE POLICY "Super admin full access to notifications" ON notifications
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND user_type = 'super_admin'
        )
    );

-- کاربران: فقط نوتیفیکیشن‌های خود
CREATE POLICY "Users can manage own notifications" ON notifications
    FOR ALL USING (user_id = auth.uid());

-- 9. Policy برای جدول sellers
-- مدیر کل: دسترسی کامل
CREATE POLICY "Super admin full access to sellers" ON sellers
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND user_type = 'super_admin'
        )
    );

-- فروشندگان: فقط مشاهده
CREATE POLICY "Cashiers can view sellers" ON sellers
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND user_type IN ('cashier', 'super_admin')
        )
    );
