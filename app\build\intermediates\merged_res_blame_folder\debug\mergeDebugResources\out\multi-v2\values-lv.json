{"logs": [{"outputFile": "com.example.sharenshop.app-mergeDebugResources-2:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8bf4b3fb7fecc46740c95cb34de6afa1\\transformed\\core-1.12.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,405,506,613,721,8717", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "198,300,400,501,608,716,831,8813"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\61acd70f2f91261ed7f816e4fc4161c1\\transformed\\ui-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,388,491,581,667,755,848,932,1002,1072,1157,1244,1317,1395,1463", "endColumns": "97,88,95,102,89,85,87,92,83,69,69,84,86,72,77,67,121", "endOffsets": "198,287,383,486,576,662,750,843,927,997,1067,1152,1239,1312,1390,1458,1580"}, "to": {"startLines": "9,10,12,13,14,18,19,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,934,1126,1222,1325,1732,1818,8155,8248,8332,8402,8472,8557,8644,8818,8896,8964", "endColumns": "97,88,95,102,89,85,87,92,83,69,69,84,86,72,77,67,121", "endOffsets": "929,1018,1217,1320,1410,1813,1901,8243,8327,8397,8467,8552,8639,8712,8891,8959,9081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88c123eabfc6d4b207c77cc8294f818f\\transformed\\material3-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,309,424,549,658,758,875,1013,1131,1278,1364,1462,1556,1657,1776,1900,2003,2141,2272,2410,2593,2725,2844,2971,3091,3186,3285,3406,3541,3643,3757,3863,3998,4143,4252,4355,4438,4533,4627,4717,4804,4915,4995,5081,5176,5280,5371,5469,5558,5665,5767,5867,6020,6100,6205", "endColumns": "126,126,114,124,108,99,116,137,117,146,85,97,93,100,118,123,102,137,130,137,182,131,118,126,119,94,98,120,134,101,113,105,134,144,108,102,82,94,93,89,86,110,79,85,94,103,90,97,88,106,101,99,152,79,104,98", "endOffsets": "177,304,419,544,653,753,870,1008,1126,1273,1359,1457,1551,1652,1771,1895,1998,2136,2267,2405,2588,2720,2839,2966,3086,3181,3280,3401,3536,3638,3752,3858,3993,4138,4247,4350,4433,4528,4622,4712,4799,4910,4990,5076,5171,5275,5366,5464,5553,5660,5762,5862,6015,6095,6200,6299"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1906,2033,2160,2275,2400,2509,2609,2726,2864,2982,3129,3215,3313,3407,3508,3627,3751,3854,3992,4123,4261,4444,4576,4695,4822,4942,5037,5136,5257,5392,5494,5608,5714,5849,5994,6103,6206,6289,6384,6478,6568,6655,6766,6846,6932,7027,7131,7222,7320,7409,7516,7618,7718,7871,7951,8056", "endColumns": "126,126,114,124,108,99,116,137,117,146,85,97,93,100,118,123,102,137,130,137,182,131,118,126,119,94,98,120,134,101,113,105,134,144,108,102,82,94,93,89,86,110,79,85,94,103,90,97,88,106,101,99,152,79,104,98", "endOffsets": "2028,2155,2270,2395,2504,2604,2721,2859,2977,3124,3210,3308,3402,3503,3622,3746,3849,3987,4118,4256,4439,4571,4690,4817,4937,5032,5131,5252,5387,5489,5603,5709,5844,5989,6098,6201,6284,6379,6473,6563,6650,6761,6841,6927,7022,7126,7217,7315,7404,7511,7613,7713,7866,7946,8051,8150"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88b5b0acc1ef090d93ba86ccb1fae5cf\\transformed\\foundation-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "87,88", "startColumns": "4,4", "startOffsets": "9086,9176", "endColumns": "89,89", "endOffsets": "9171,9261"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\beb880474af4802d615c3712a7fe5cf4\\transformed\\browser-1.8.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,373", "endColumns": "102,98,115,101", "endOffsets": "153,252,368,470"}, "to": {"startLines": "11,15,16,17", "startColumns": "4,4,4,4", "startOffsets": "1023,1415,1514,1630", "endColumns": "102,98,115,101", "endOffsets": "1121,1509,1625,1727"}}]}]}