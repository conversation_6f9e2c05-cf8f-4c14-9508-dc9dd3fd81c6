// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.presentation.seller;

import com.example.sharenshop.domain.use_case.SellerUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SellerViewModel_Factory implements Factory<SellerViewModel> {
  private final Provider<SellerUseCases> sellerUseCasesProvider;

  public SellerViewModel_Factory(Provider<SellerUseCases> sellerUseCasesProvider) {
    this.sellerUseCasesProvider = sellerUseCasesProvider;
  }

  @Override
  public SellerViewModel get() {
    return newInstance(sellerUseCasesProvider.get());
  }

  public static SellerViewModel_Factory create(Provider<SellerUseCases> sellerUseCasesProvider) {
    return new SellerViewModel_Factory(sellerUseCasesProvider);
  }

  public static SellerViewModel newInstance(SellerUseCases sellerUseCases) {
    return new SellerViewModel(sellerUseCases);
  }
}
