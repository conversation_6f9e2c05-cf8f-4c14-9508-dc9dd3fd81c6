package com.example.sharenshop.presentation.product

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharenshop.data.model.Product
import com.example.sharenshop.domain.use_case.ProductUseCases
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProductViewModel @Inject constructor(
    private val productUseCases: ProductUseCases
) : ViewModel() {

    private val _products = MutableStateFlow<List<Product>>(emptyList())
    val products: StateFlow<List<Product>> = _products.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()

    init {
        loadProducts()
    }

    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
        if (query.isBlank()) {
            loadProducts()
        } else {
            searchProducts(query)
        }
    }

    fun loadProducts() {
        _isLoading.value = true
        productUseCases.getAllProducts()
            .onEach { products ->
                _products.value = products
            }
            .catch { e ->
                _error.value = e.localizedMessage ?: "Unknown error loading products"
            }
            .onEach { _isLoading.value = false }
            .launchIn(viewModelScope)
    }

    fun insertProduct(product: Product) {
        viewModelScope.launch {
            try {
                productUseCases.insertProduct(product)
                loadProducts() // Refresh the list after insertion
            } catch (e: Exception) {
                _error.value = e.localizedMessage ?: "Error inserting product"
            }
        }
    }

    fun updateProduct(product: Product) {
        viewModelScope.launch {
            try {
                productUseCases.updateProduct(product)
                loadProducts() // Refresh the list after update
            } catch (e: Exception) {
                _error.value = e.localizedMessage ?: "Error updating product"
            }
        }
    }

    fun addProduct(product: Product) {
        insertProduct(product)
    }

    fun deleteProduct(productId: String) {
        viewModelScope.launch {
            try {
                productUseCases.deleteProduct(productId)
                loadProducts() // Refresh the list after deletion
            } catch (e: Exception) {
                _error.value = e.localizedMessage ?: "Error deleting product"
            }
        }
    }

    fun deleteProduct(product: Product) {
        deleteProduct(product.id)
    }

    fun searchProducts(query: String) {
        viewModelScope.launch {
            _isLoading.value = true
            productUseCases.searchProducts(query)
                .onEach { products ->
                    _products.value = products
                }
                .catch { e ->
                    _error.value = e.localizedMessage ?: "Unknown error searching products"
                }
                .onEach { _isLoading.value = false }
                .launchIn(viewModelScope)
        }
    }
} 