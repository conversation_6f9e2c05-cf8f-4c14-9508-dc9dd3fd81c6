package com.example.sharenshop.di;

@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000l\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\'\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\'J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\'J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\'J\u0010\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0012H\'J\u0010\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0016H\'J\u0010\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aH\'J\u0010\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001eH\'J\u0010\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"H\'\u00a8\u0006#"}, d2 = {"Lcom/example/sharenshop/di/RepositoryModule;", "", "()V", "bindCustomerRepository", "Lcom/example/sharenshop/domain/repository/CustomerRepository;", "customerRepositoryImpl", "Lcom/example/sharenshop/data/repository/CustomerRepositoryImpl;", "bindInvoiceRepository", "Lcom/example/sharenshop/domain/repository/InvoiceRepository;", "invoiceRepositoryImpl", "Lcom/example/sharenshop/data/repository/InvoiceRepositoryImpl;", "bindNotificationRepository", "Lcom/example/sharenshop/domain/repository/NotificationRepository;", "notificationRepositoryImpl", "Lcom/example/sharenshop/data/repository/NotificationRepositoryImpl;", "bindProductRepository", "Lcom/example/sharenshop/domain/repository/ProductRepository;", "productRepositoryImpl", "Lcom/example/sharenshop/data/repository/ProductRepositoryImpl;", "bindSellerRepository", "Lcom/example/sharenshop/domain/repository/SellerRepository;", "sellerRepositoryImpl", "Lcom/example/sharenshop/data/repository/SellerRepositoryImpl;", "bindSettingsRepository", "Lcom/example/sharenshop/domain/repository/SettingsRepository;", "settingsRepositoryImpl", "Lcom/example/sharenshop/data/repository/SettingsRepositoryImpl;", "bindStatisticsRepository", "Lcom/example/sharenshop/domain/repository/StatisticsRepository;", "statisticsRepositoryImpl", "Lcom/example/sharenshop/data/repository/StatisticsRepositoryImpl;", "bindUserRepository", "Lcom/example/sharenshop/domain/repository/UserRepository;", "userRepositoryImpl", "Lcom/example/sharenshop/data/repository/UserRepositoryImpl;", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public abstract class RepositoryModule {
    
    public RepositoryModule() {
        super();
    }
    
    @dagger.Binds()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.domain.repository.UserRepository bindUserRepository(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.repository.UserRepositoryImpl userRepositoryImpl);
    
    @dagger.Binds()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.domain.repository.ProductRepository bindProductRepository(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.repository.ProductRepositoryImpl productRepositoryImpl);
    
    @dagger.Binds()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.domain.repository.CustomerRepository bindCustomerRepository(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.repository.CustomerRepositoryImpl customerRepositoryImpl);
    
    @dagger.Binds()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.domain.repository.SellerRepository bindSellerRepository(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.repository.SellerRepositoryImpl sellerRepositoryImpl);
    
    @dagger.Binds()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.domain.repository.InvoiceRepository bindInvoiceRepository(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.repository.InvoiceRepositoryImpl invoiceRepositoryImpl);
    
    @dagger.Binds()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.domain.repository.SettingsRepository bindSettingsRepository(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.repository.SettingsRepositoryImpl settingsRepositoryImpl);
    
    @dagger.Binds()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.domain.repository.StatisticsRepository bindStatisticsRepository(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.repository.StatisticsRepositoryImpl statisticsRepositoryImpl);
    
    @dagger.Binds()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.domain.repository.NotificationRepository bindNotificationRepository(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.repository.NotificationRepositoryImpl notificationRepositoryImpl);
}