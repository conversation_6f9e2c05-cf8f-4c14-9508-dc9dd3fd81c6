package com.example.sharenshop.data.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0010\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0096@\u00a2\u0006\u0002\u0010\u000bJ\u0016\u0010\f\u001a\u00020\b2\u0006\u0010\r\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u000bJ\u0014\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00100\u000fH\u0016J\u0018\u0010\u0012\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00110\u000f2\u0006\u0010\t\u001a\u00020\nH\u0016J\u001a\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00100\u000f2\u0006\u0010\t\u001a\u00020\nJ\u001c\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00100\u000f2\u0006\u0010\u0016\u001a\u00020\nH\u0016J\u001c\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00100\u000f2\u0006\u0010\u0018\u001a\u00020\nH\u0016J\u001c\u0010\u0019\u001a\u00020\b2\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00140\u0010H\u0086@\u00a2\u0006\u0002\u0010\u001bJ\u0016\u0010\u001c\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\u0011H\u0096@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u0010\u001f\u001a\u00020\b2\u0006\u0010 \u001a\u00020\u0014H\u0086@\u00a2\u0006\u0002\u0010!J\u0016\u0010\"\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\u0011H\u0096@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u0010#\u001a\u00020\b2\u0006\u0010 \u001a\u00020\u0014H\u0086@\u00a2\u0006\u0002\u0010!R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006$"}, d2 = {"Lcom/example/sharenshop/data/repository/InvoiceRepositoryImpl;", "Lcom/example/sharenshop/domain/repository/InvoiceRepository;", "invoiceDao", "Lcom/example/sharenshop/data/local/dao/InvoiceDao;", "invoiceItemDao", "Lcom/example/sharenshop/data/local/dao/InvoiceItemDao;", "(Lcom/example/sharenshop/data/local/dao/InvoiceDao;Lcom/example/sharenshop/data/local/dao/InvoiceItemDao;)V", "deleteInvoiceById", "", "invoiceId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteInvoiceItemById", "invoiceItemId", "getAllInvoices", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/example/sharenshop/data/model/Invoice;", "getInvoiceById", "getInvoiceItemsByInvoiceId", "Lcom/example/sharenshop/data/model/InvoiceItem;", "getInvoicesByCustomerId", "customerId", "getInvoicesByUserId", "userId", "insertAllInvoiceItems", "invoiceItems", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertInvoice", "invoice", "(Lcom/example/sharenshop/data/model/Invoice;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertInvoiceItem", "invoiceItem", "(Lcom/example/sharenshop/data/model/InvoiceItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateInvoice", "updateInvoiceItem", "app_debug"})
public final class InvoiceRepositoryImpl implements com.example.sharenshop.domain.repository.InvoiceRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.data.local.dao.InvoiceDao invoiceDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.data.local.dao.InvoiceItemDao invoiceItemDao = null;
    
    @javax.inject.Inject()
    public InvoiceRepositoryImpl(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.local.dao.InvoiceDao invoiceDao, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.local.dao.InvoiceItemDao invoiceItemDao) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<com.example.sharenshop.data.model.Invoice> getInvoiceById(@org.jetbrains.annotations.NotNull()
    java.lang.String invoiceId) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object insertInvoice(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Invoice invoice, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object updateInvoice(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Invoice invoice, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object deleteInvoiceById(@org.jetbrains.annotations.NotNull()
    java.lang.String invoiceId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharenshop.data.model.Invoice>> getAllInvoices() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharenshop.data.model.Invoice>> getInvoicesByCustomerId(@org.jetbrains.annotations.NotNull()
    java.lang.String customerId) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharenshop.data.model.Invoice>> getInvoicesByUserId(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharenshop.data.model.InvoiceItem>> getInvoiceItemsByInvoiceId(@org.jetbrains.annotations.NotNull()
    java.lang.String invoiceId) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertInvoiceItem(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.InvoiceItem invoiceItem, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertAllInvoiceItems(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.sharenshop.data.model.InvoiceItem> invoiceItems, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateInvoiceItem(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.InvoiceItem invoiceItem, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteInvoiceItemById(@org.jetbrains.annotations.NotNull()
    java.lang.String invoiceItemId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}