package com.example.sharenshop.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.sharenshop.data.model.InvoiceItem
import kotlinx.coroutines.flow.Flow

@Dao
interface InvoiceItemDao {
    @Query("SELECT * FROM invoice_items WHERE id = :invoiceItemId")
    fun getInvoiceItemById(invoiceItemId: String): Flow<InvoiceItem?>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertInvoiceItem(invoiceItem: InvoiceItem)

    @Update
    suspend fun updateInvoiceItem(invoiceItem: InvoiceItem)

    @Query("DELETE FROM invoice_items WHERE id = :invoiceItemId")
    suspend fun deleteInvoiceItemById(invoiceItemId: String)

    @Query("SELECT * FROM invoice_items WHERE invoiceId = :invoiceId")
    fun getInvoiceItemsByInvoiceId(invoiceId: String): Flow<List<InvoiceItem>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAllInvoiceItems(invoiceItems: List<InvoiceItem>)
} 