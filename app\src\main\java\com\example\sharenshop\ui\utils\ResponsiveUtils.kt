package com.example.sharenshop.ui.utils

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * Utility functions for responsive design
 */

@Composable
fun isLandscape(): <PERSON><PERSON><PERSON> {
    val configuration = LocalConfiguration.current
    return configuration.screenWidthDp > configuration.screenHeightDp
}

@Composable
fun isTablet(): <PERSON><PERSON><PERSON> {
    val configuration = LocalConfiguration.current
    return configuration.screenWidthDp >= 600
}

@Composable
fun getScreenWidth(): Dp {
    val configuration = LocalConfiguration.current
    return configuration.screenWidthDp.dp
}

@Composable
fun getScreenHeight(): Dp {
    val configuration = LocalConfiguration.current
    return configuration.screenHeightDp.dp
}

/**
 * Get number of columns for grid based on screen size
 */
@Composable
fun getGridColumns(): Int {
    val configuration = LocalConfiguration.current
    return when {
        configuration.screenWidthDp >= 900 -> 4 // Large tablets/desktop
        configuration.screenWidthDp >= 600 -> 3 // Small tablets
        isLandscape() -> 3 // Phone landscape
        else -> 2 // Phone portrait
    }
}

/**
 * Get adaptive padding based on screen size
 */
@Composable
fun getAdaptivePadding(): Dp {
    val configuration = LocalConfiguration.current
    return when {
        configuration.screenWidthDp >= 600 -> 24.dp // Tablet
        isLandscape() -> 12.dp // Phone landscape
        else -> 16.dp // Phone portrait
    }
}

/**
 * Get adaptive card height based on orientation
 */
@Composable
fun getAdaptiveCardHeight(): Dp {
    return if (isLandscape()) 60.dp else 80.dp
}

/**
 * Get adaptive stats card width
 */
@Composable
fun getStatsCardWidth(): Dp {
    val configuration = LocalConfiguration.current
    return when {
        configuration.screenWidthDp >= 600 -> 160.dp // Tablet
        isLandscape() -> 120.dp // Phone landscape
        else -> 140.dp // Phone portrait
    }
}

/**
 * Get adaptive stats card height
 */
@Composable
fun getStatsCardHeight(): Dp {
    return if (isLandscape()) 80.dp else 100.dp
}

/**
 * Check if we should use horizontal layout
 */
@Composable
fun shouldUseHorizontalLayout(): Boolean {
    return isLandscape() || isTablet()
}

/**
 * Get adaptive spacing between items
 */
@Composable
fun getAdaptiveSpacing(): Dp {
    return if (isLandscape()) 8.dp else 16.dp
}

/**
 * Get adaptive text size multiplier
 */
@Composable
fun getTextSizeMultiplier(): Float {
    return if (isLandscape()) 0.9f else 1.0f
}
