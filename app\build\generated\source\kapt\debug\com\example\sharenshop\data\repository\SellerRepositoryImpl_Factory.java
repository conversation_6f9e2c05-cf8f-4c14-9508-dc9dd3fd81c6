// Generated by Da<PERSON> (https://dagger.dev).
package com.example.sharenshop.data.repository;

import com.example.sharenshop.data.local.dao.SellerDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SellerRepositoryImpl_Factory implements Factory<SellerRepositoryImpl> {
  private final Provider<SellerDao> sellerDaoProvider;

  public SellerRepositoryImpl_Factory(Provider<SellerDao> sellerDaoProvider) {
    this.sellerDaoProvider = sellerDaoProvider;
  }

  @Override
  public SellerRepositoryImpl get() {
    return newInstance(sellerDaoProvider.get());
  }

  public static SellerRepositoryImpl_Factory create(Provider<SellerDao> sellerDaoProvider) {
    return new SellerRepositoryImpl_Factory(sellerDaoProvider);
  }

  public static SellerRepositoryImpl newInstance(SellerDao sellerDao) {
    return new SellerRepositoryImpl(sellerDao);
  }
}
