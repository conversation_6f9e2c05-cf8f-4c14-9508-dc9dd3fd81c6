package com.example.sharenshop.di;

@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0005\u001a\u00020\u0006H\u0007\u00a8\u0006\u0013"}, d2 = {"Lcom/example/sharenshop/di/DaoModule;", "", "()V", "provideCustomerDao", "Lcom/example/sharenshop/data/local/dao/CustomerDao;", "appDatabase", "Lcom/example/sharenshop/data/local/database/AppDatabase;", "provideInvoiceDao", "Lcom/example/sharenshop/data/local/dao/InvoiceDao;", "provideInvoiceItemDao", "Lcom/example/sharenshop/data/local/dao/InvoiceItemDao;", "provideNotificationDao", "Lcom/example/sharenshop/data/local/dao/NotificationDao;", "provideProductDao", "Lcom/example/sharenshop/data/local/dao/ProductDao;", "provideSellerDao", "Lcom/example/sharenshop/data/local/dao/SellerDao;", "provideUserDao", "Lcom/example/sharenshop/data/local/dao/UserDao;", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class DaoModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.sharenshop.di.DaoModule INSTANCE = null;
    
    private DaoModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.local.dao.UserDao provideUserDao(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.local.database.AppDatabase appDatabase) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.local.dao.ProductDao provideProductDao(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.local.database.AppDatabase appDatabase) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.local.dao.CustomerDao provideCustomerDao(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.local.database.AppDatabase appDatabase) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.local.dao.SellerDao provideSellerDao(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.local.database.AppDatabase appDatabase) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.local.dao.InvoiceDao provideInvoiceDao(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.local.database.AppDatabase appDatabase) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.local.dao.InvoiceItemDao provideInvoiceItemDao(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.local.database.AppDatabase appDatabase) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.local.dao.NotificationDao provideNotificationDao(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.local.database.AppDatabase appDatabase) {
        return null;
    }
}