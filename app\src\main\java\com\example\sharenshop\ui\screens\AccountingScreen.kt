package com.example.sharenshop.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.sharenshop.data.model.*
import com.example.sharenshop.ui.theme.SharenShopColors
import java.math.BigDecimal
import java.text.NumberFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AccountingScreen(
    userRole: UserRole = UserRole.SUPER_ADMIN
) {
    var selectedTab by remember { mutableStateOf(0) }
    val tabs = when (userRole) {
        UserRole.SUPER_ADMIN -> listOf("داشبورد مالی", "تراکنش‌ها", "حساب‌ها", "گزارشات")
        UserRole.CASHIER -> listOf("حساب من", "تراکنش‌های من")
        else -> listOf("حسابداری")
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(SharenShopColors.FutureDusk)
            .padding(16.dp)
    ) {
        // Header
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "📊 سیستم حسابداری",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = SharenShopColors.DeepBlue
                )
                Text(
                    text = when (userRole) {
                        UserRole.SUPER_ADMIN -> "مدیریت کامل مالی و حسابداری"
                        UserRole.CASHIER -> "مشاهده حساب و تراکنش‌های شخصی"
                        else -> "دسترسی محدود به حسابداری"
                    },
                    fontSize = 14.sp,
                    color = SharenShopColors.SoftGray
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Tab Row
        TabRow(
            selectedTabIndex = selectedTab,
            containerColor = Color.White,
            contentColor = SharenShopColors.DeepBlue
        ) {
            tabs.forEachIndexed { index, title ->
                Tab(
                    selected = selectedTab == index,
                    onClick = { selectedTab = index },
                    text = { Text(title) }
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Content based on selected tab and user role
        when (userRole) {
            UserRole.SUPER_ADMIN -> {
                when (selectedTab) {
                    0 -> AdminFinancialDashboardTab()
                    1 -> AdminTransactionsTab()
                    2 -> AdminAccountsTab()
                    3 -> AdminReportsTab()
                }
            }
            UserRole.CASHIER -> {
                when (selectedTab) {
                    0 -> CashierMyAccountTab()
                    1 -> CashierMyTransactionsTab()
                }
            }
            else -> {
                Text(
                    text = "دسترسی محدود",
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
fun AdminFinancialDashboardTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // Financial Overview Cards
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                FinancialSummaryCard(
                    title = "کل فروش",
                    amount = "۴۵,۲۰۰,۰۰۰",
                    icon = Icons.Default.Star,
                    color = SharenShopColors.Success,
                    modifier = Modifier.weight(1f)
                )
                
                FinancialSummaryCard(
                    title = "دریافت نقدی",
                    amount = "۲۸,۵۰۰,۰۰۰",
                    icon = Icons.Default.Star,
                    color = SharenShopColors.ProfitColor,
                    modifier = Modifier.weight(1f)
                )
            }
        }
        
        item {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                FinancialSummaryCard(
                    title = "مطالبات",
                    amount = "۱۶,۷۰۰,۰۰۰",
                    icon = Icons.Default.Star,
                    color = SharenShopColors.Warning,
                    modifier = Modifier.weight(1f)
                )
                
                FinancialSummaryCard(
                    title = "تصویه معوق",
                    amount = "۳,۲۰۰,۰۰۰",
                    icon = Icons.Default.Star,
                    color = SharenShopColors.LossColor,
                    modifier = Modifier.weight(1f)
                )
            }
        }

        item {
            // Recent Transactions
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "📋 آخرین تراکنش‌ها",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = SharenShopColors.DeepBlue
                        )
                        
                        TextButton(
                            onClick = { /* Navigate to all transactions */ }
                        ) {
                            Text("مشاهده همه")
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // Sample recent transactions
                    repeat(3) { index ->
                        TransactionItem(
                            description = "دریافت پرداخت از محمد حسینی",
                            amount = "۵۰۰,۰۰۰",
                            type = TransactionCategory.CASH_RECEIPT,
                            date = "۱۴۰۳/۰۹/۱۵"
                        )
                        
                        if (index < 2) {
                            HorizontalDivider(modifier = Modifier.padding(vertical = 8.dp))
                        }
                    }
                }
            }
        }

        item {
            // Sellers Balance Summary
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "👥 خلاصه حساب فروشندگان",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = SharenShopColors.DeepBlue
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // Sample seller balances
                    SellerBalanceItem("احمد محمدی", "۲,۵۰۰,۰۰۰", "۵۰۰,۰۰۰")
                    HorizontalDivider(modifier = Modifier.padding(vertical = 8.dp))
                    SellerBalanceItem("علی رضایی", "۱,۸۰۰,۰۰۰", "۳۰۰,۰۰۰")
                    HorizontalDivider(modifier = Modifier.padding(vertical = 8.dp))
                    SellerBalanceItem("فاطمه احمدی", "۳,۱۰۰,۰۰۰", "۷۰۰,۰۰۰")
                }
            }
        }
    }
}

@Composable
fun FinancialSummaryCard(
    title: String,
    amount: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(32.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = title,
                fontSize = 14.sp,
                color = SharenShopColors.SoftGray,
                textAlign = TextAlign.Center
            )
            
            Text(
                text = "$amount تومان",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = color,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun TransactionItem(
    description: String,
    amount: String,
    type: TransactionCategory,
    date: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = description,
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp
            )
            Text(
                text = date,
                fontSize = 12.sp,
                color = SharenShopColors.SoftGray
            )
        }
        
        Column(
            horizontalAlignment = Alignment.End
        ) {
            Text(
                text = "$amount تومان",
                fontWeight = FontWeight.Bold,
                color = when (type) {
                    TransactionCategory.CASH_RECEIPT, TransactionCategory.CREDIT_RECEIPT -> SharenShopColors.ProfitColor
                    TransactionCategory.SETTLEMENT_OUT -> SharenShopColors.LossColor
                    else -> SharenShopColors.DeepBlue
                }
            )
            Text(
                text = type.displayName,
                fontSize = 12.sp,
                color = SharenShopColors.SoftGray
            )
        }
    }
}

@Composable
fun SellerBalanceItem(
    sellerName: String,
    totalReceived: String,
    pendingSettlement: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = sellerName,
            fontWeight = FontWeight.Medium
        )
        
        Column(
            horizontalAlignment = Alignment.End
        ) {
            Text(
                text = "دریافتی: $totalReceived",
                fontSize = 14.sp,
                color = SharenShopColors.ProfitColor
            )
            Text(
                text = "معوق: $pendingSettlement",
                fontSize = 12.sp,
                color = SharenShopColors.Warning
            )
        }
    }
}

@Composable
fun AdminTransactionsTab() {
    Text(
        text = "لیست کامل تراکنش‌ها",
        modifier = Modifier.fillMaxWidth(),
        textAlign = TextAlign.Center,
        fontSize = 16.sp,
        color = SharenShopColors.SoftGray
    )
}

@Composable
fun AdminAccountsTab() {
    Text(
        text = "مدیریت حساب‌های مالی",
        modifier = Modifier.fillMaxWidth(),
        textAlign = TextAlign.Center,
        fontSize = 16.sp,
        color = SharenShopColors.SoftGray
    )
}

@Composable
fun AdminReportsTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "📈 گزارشات مالی",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = SharenShopColors.DeepBlue
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    ReportButton(
                        title = "گزارش فروش ماهانه",
                        description = "آمار فروش و درآمد ماهانه",
                        icon = Icons.Default.Star
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    ReportButton(
                        title = "گزارش مطالبات",
                        description = "وضعیت بدهی‌ها و مطالبات",
                        icon = Icons.Default.Star
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    ReportButton(
                        title = "گزارش تصویه حساب",
                        description = "وضعیت تصویه‌های فروشندگان",
                        icon = Icons.Default.Star
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    ReportButton(
                        title = "گزارش جریان نقدینگی",
                        description = "ورود و خروج نقدینگی",
                        icon = Icons.Default.Star
                    )
                }
            }
        }
    }
}

@Composable
fun ReportButton(
    title: String,
    description: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = SharenShopColors.FutureDusk),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = SharenShopColors.DeepBlue,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    fontWeight = FontWeight.Medium,
                    fontSize = 16.sp
                )
                Text(
                    text = description,
                    fontSize = 14.sp,
                    color = SharenShopColors.SoftGray
                )
            }
            
            @Suppress("DEPRECATION")
            Icon(
                imageVector = Icons.Default.ArrowForward,
                contentDescription = null,
                tint = SharenShopColors.SoftGray
            )
        }
    }
}

@Composable
fun CashierMyAccountTab() {
    Text(
        text = "حساب شخصی فروشنده",
        modifier = Modifier.fillMaxWidth(),
        textAlign = TextAlign.Center,
        fontSize = 16.sp,
        color = SharenShopColors.SoftGray
    )
}

@Composable
fun CashierMyTransactionsTab() {
    Text(
        text = "تراکنش‌های شخصی فروشنده",
        modifier = Modifier.fillMaxWidth(),
        textAlign = TextAlign.Center,
        fontSize = 16.sp,
        color = SharenShopColors.SoftGray
    )
}
