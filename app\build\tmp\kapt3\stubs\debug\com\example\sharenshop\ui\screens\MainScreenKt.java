package com.example.sharenshop.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000v\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\b\u0012\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b%\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0002\u001a\u00020\u0001H\u0007\u001a<\u0010\u0003\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u00052\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u001a\b\u0010\u000b\u001a\u00020\u0001H\u0007\u001a\u0018\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0007\u001a\b\u0010\u0011\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0012\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0013\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0014\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0015\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0016\u001a\u00020\u0001H\u0007\u001a6\u0010\u0017\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0018\u001a\u00020\u00052\u0006\u0010\u0019\u001a\u00020\u00052\u0006\u0010\u001a\u001a\u00020\u00052\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u001a$\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\u0012\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u001eH\u0007\u001aV\u0010\u001f\u001a\u00020\u00012\u0006\u0010 \u001a\u00020\u00052\u0006\u0010!\u001a\u00020\u00052\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00050#2\u0006\u0010$\u001a\u00020\u00052\u0006\u0010%\u001a\u00020\u00052\u0006\u0010&\u001a\u00020\u00052\u0006\u0010\'\u001a\u00020\u00052\u0006\u0010(\u001a\u00020\u0005H\u0007\u001a\b\u0010)\u001a\u00020\u0001H\u0007\u001aH\u0010*\u001a\u00020\u00012\u0006\u0010+\u001a\u00020\u00052\u0006\u0010 \u001a\u00020\u00052\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010!\u001a\u00020\u00052\u0006\u0010,\u001a\u00020\u00052\u0006\u0010-\u001a\u00020\u00052\u0006\u0010\'\u001a\u00020\u00052\u0006\u0010.\u001a\u00020\u0005H\u0007\u001a\b\u0010/\u001a\u00020\u0001H\u0007\u001a\b\u00100\u001a\u00020\u0001H\u0007\u001a\b\u00101\u001a\u00020\u0001H\u0007\u001a\b\u00102\u001a\u00020\u0001H\u0007\u001a*\u00103\u001a\u00020\u00012\u0006\u00104\u001a\u00020\u00052\u0006\u0010,\u001a\u00020\u00052\u0006\u00105\u001a\u000206H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b7\u00108\u001a0\u00109\u001a\u00020\u00012\u0006\u0010 \u001a\u00020\u00052\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010,\u001a\u00020\u00052\u0006\u0010\'\u001a\u00020\u00052\u0006\u0010.\u001a\u00020\u0005H\u0007\u001a$\u0010:\u001a\u00020\u00012\f\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010<\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u001a$\u0010=\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\u0012\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u001eH\u0007\u001a(\u0010>\u001a\u00020\u00012\u0006\u0010?\u001a\u00020\u00102\f\u0010@\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010A\u001a\u00020BH\u0007\u001a\b\u0010C\u001a\u00020\u0001H\u0007\u001a\b\u0010D\u001a\u00020\u0001H\u0007\u001a(\u0010E\u001a\u00020\u00012\u0006\u0010F\u001a\u00020G2\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010H\u001a\u00020IH\u0007\u001a0\u0010J\u001a\u00020\u00012\u0006\u00104\u001a\u00020\u00052\u0006\u0010K\u001a\u00020\u00052\u0006\u0010L\u001a\u00020\u00052\u0006\u0010M\u001a\u00020N2\u0006\u0010O\u001a\u00020\u0005H\u0007\u001a\u0010\u0010P\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u0010H\u0007\u001a\b\u0010Q\u001a\u00020\u0001H\u0007\u001a\b\u0010R\u001a\u00020\u0001H\u0007\u001a \u0010S\u001a\u00020\u00012\u0006\u0010T\u001a\u00020\u00052\u0006\u0010U\u001a\u00020V2\u0006\u0010W\u001a\u00020\u0005H\u0007\u001a \u0010X\u001a\u00020\u00012\u0006\u0010Y\u001a\u00020\u00052\u0006\u0010Z\u001a\u00020\u00052\u0006\u0010[\u001a\u00020\\H\u0007\u001a \u0010]\u001a\u00020\u00012\u0006\u0010^\u001a\u00020_2\u000e\b\u0002\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u001a&\u0010`\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\u0014\b\u0002\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u001eH\u0007\u001a\u0010\u0010a\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u0010H\u0007\u001a \u0010b\u001a\u00020\u00012\u0006\u00104\u001a\u00020\u00052\u0006\u0010L\u001a\u00020\u00052\u0006\u0010[\u001a\u00020\\H\u0007\u001a6\u0010c\u001a\u00020\u00012\u0006\u0010!\u001a\u00020\u00052\u0006\u0010d\u001a\u00020\u00052\u0006\u0010e\u001a\u00020V2\u0006\u0010f\u001a\u00020\u00052\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u001a\u0010\u0010g\u001a\u00020\u00012\u0006\u0010h\u001a\u00020\u0005H\u0007\u001a\u0010\u0010i\u001a\u00020\u00012\u0006\u0010h\u001a\u00020\u0005H\u0007\u001a\u0010\u0010j\u001a\u00020\u00012\u0006\u0010h\u001a\u00020\u0005H\u0007\u001a(\u0010k\u001a\u00020\u00012\u0006\u0010!\u001a\u00020\u00052\u0006\u0010l\u001a\u00020\u00052\u0006\u0010m\u001a\u00020V2\u0006\u0010n\u001a\u00020VH\u0007\u001a\u0010\u0010o\u001a\u00020\u00012\u0006\u0010h\u001a\u00020\u0005H\u0007\u001a\b\u0010p\u001a\u00020\u0001H\u0007\u001a\b\u0010q\u001a\u00020\u0001H\u0007\u001a<\u0010r\u001a\u00020\u00012\u0006\u00104\u001a\u00020\u00052\u0006\u0010Z\u001a\u00020\u00052\u0006\u0010[\u001a\u00020\\2\u0006\u00105\u001a\u0002062\b\b\u0002\u0010H\u001a\u00020IH\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\bs\u0010t\u001a\b\u0010u\u001a\u00020\u0001H\u0007\u001a \u0010v\u001a\u00020\u00012\u0006\u0010w\u001a\u00020\u00052\u0006\u0010l\u001a\u00020\u00052\u0006\u0010x\u001a\u00020\u0005H\u0007\u001a,\u0010y\u001a\u00020\u00012\u0006\u0010?\u001a\u00020\u00102\f\u0010<\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010z\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u001a\u0010\u0010{\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u0010H\u0007\u001a\u0014\u0010|\u001a\b\u0012\u0004\u0012\u00020G0#2\u0006\u0010\u000f\u001a\u00020\u0010\u001a\f\u0010}\u001a\b\u0012\u0004\u0012\u00020_0#\u001a\f\u0010~\u001a\b\u0012\u0004\u0012\u00020_0#\u001a\u0014\u0010\u007f\u001a\b\u0012\u0004\u0012\u00020G0#2\u0006\u0010\u000f\u001a\u00020\u0010\u001a\r\u0010\u0080\u0001\u001a\b\u0012\u0004\u0012\u00020_0#\u001a\u000f\u0010\u0081\u0001\u001a\u00020\u00052\u0006\u0010\u000f\u001a\u00020\u0010\u001a\u000f\u0010\u0082\u0001\u001a\u00020\u00052\u0006\u0010\u000f\u001a\u00020\u0010\u001a\u000f\u0010\u0083\u0001\u001a\u00020\u00052\u0006\u0010\u000f\u001a\u00020\u0010\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0084\u0001"}, d2 = {"AccountingScreen", "", "AllMessagesTab", "ApprovalRequestCard", "customerName", "", "referrerName", "requestTime", "onApprove", "Lkotlin/Function0;", "onReject", "ApprovalRequestsTab", "BottomNavigationBar", "navController", "Landroidx/navigation/NavHostController;", "userRole", "Lcom/example/sharenshop/data/model/UserRole;", "CashierApprovalRequestsTab", "CashierMessagesTab", "CustomerApprovalStatusTab", "CustomerManagementScreen", "CustomerMessagesTab", "CustomerNotificationsTab", "CustomerSummaryCard", "totalPurchases", "totalPayments", "balance", "onClick", "DashboardContent", "onNavigateToScreen", "Lkotlin/Function1;", "DetailedInvoiceCard", "invoiceNumber", "sellerName", "items", "", "totalAmount", "paidAmount", "remainingAmount", "date", "paymentType", "DetailedInvoicesTab", "DetailedPaymentCard", "paymentId", "amount", "paymentMethod", "status", "DetailedPaymentsTab", "DetailedStatsTab", "DetailsScreen", "FinancialReportTab", "FinancialSummaryItem", "title", "color", "Landroidx/compose/ui/graphics/Color;", "FinancialSummaryItem-mxwnekA", "(Ljava/lang/String;Ljava/lang/String;J)V", "InvoiceSummaryCard", "LogoutConfirmationDialog", "onConfirm", "onDismiss", "MainMenuGrid", "MainScreen", "currentUserRole", "onNavigateToAuth", "viewModel", "Lcom/example/sharenshop/presentation/main/MainViewModel;", "MainStatisticsScreen", "MainUserManagementScreen", "MenuItemCard", "menuItem", "Lcom/example/sharenshop/ui/screens/MenuItem;", "modifier", "Landroidx/compose/ui/Modifier;", "MessageCard", "message", "time", "isRead", "", "type", "MessagesScreen", "NotificationsTab", "PaymentScreen", "ProductStatsRow", "productName", "soldCount", "", "revenue", "ProfileInfoRow", "label", "value", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "QuickStatCard", "stat", "Lcom/example/sharenshop/ui/screens/QuickStat;", "QuickStatsCards", "RecentActivities", "RecentActivityItem", "SellerCard", "sellerEmail", "customerCount", "totalSales", "SellerCustomersTab", "sellerId", "SellerDetailsScreen", "SellerInvoicesTab", "SellerPerformanceRow", "sales", "customers", "performance", "SellerStatsTab", "SellersManagementScreen", "SettlementScreen", "StatCard", "StatCard-42QJj7c", "(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/graphics/vector/ImageVector;JLandroidx/compose/ui/Modifier;)V", "SystemMessagesTab", "TimeStatsRow", "period", "invoices", "UserProfileDialog", "onLogout", "WelcomeHeader", "getBottomNavItemsForRole", "getCashierStats", "getCustomerStats", "getMenuItemsForRole", "getSuperAdminStats", "getUserDisplayName", "getUserEmail", "getUserPhone", "app_debug"})
public final class MainScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void MainScreen(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole currentUserRole, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToAuth, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.main.MainViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DashboardContent(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onNavigateToScreen) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void WelcomeHeader(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void QuickStatsCards(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onNavigateToScreen) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void QuickStatCard(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.ui.screens.QuickStat stat, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @kotlin.Suppress(names = {"DEPRECATION"})
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.example.sharenshop.ui.screens.QuickStat> getSuperAdminStats() {
        return null;
    }
    
    @kotlin.Suppress(names = {"DEPRECATION"})
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.example.sharenshop.ui.screens.QuickStat> getCashierStats() {
        return null;
    }
    
    @kotlin.Suppress(names = {"DEPRECATION"})
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.example.sharenshop.ui.screens.QuickStat> getCustomerStats() {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MainMenuGrid(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onNavigateToScreen) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void MenuItemCard(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.ui.screens.MenuItem menuItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @kotlin.Suppress(names = {"DEPRECATION"})
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.example.sharenshop.ui.screens.MenuItem> getMenuItemsForRole(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole) {
        return null;
    }
    
    @kotlin.Suppress(names = {"DEPRECATION"})
    @androidx.compose.runtime.Composable()
    public static final void RecentActivities(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void RecentActivityItem(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String time, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.graphics.vector.ImageVector icon) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void BottomNavigationBar(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavHostController navController, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole) {
    }
    
    @kotlin.Suppress(names = {"DEPRECATION"})
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.example.sharenshop.ui.screens.MenuItem> getBottomNavItemsForRole(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole) {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CustomerManagementScreen() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MainStatisticsScreen() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MainUserManagementScreen() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PaymentScreen() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AccountingScreen() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SettlementScreen() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void UserProfileDialog(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole currentUserRole, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onLogout) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ProfileInfoRow(@org.jetbrains.annotations.NotNull()
    java.lang.String label, @org.jetbrains.annotations.NotNull()
    java.lang.String value, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.graphics.vector.ImageVector icon) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void LogoutConfirmationDialog(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getUserDisplayName(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getUserPhone(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getUserEmail(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole) {
        return null;
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void MessagesScreen(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AllMessagesTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ApprovalRequestsTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SystemMessagesTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CashierMessagesTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CashierApprovalRequestsTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void NotificationsTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CustomerMessagesTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CustomerApprovalStatusTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CustomerNotificationsTab() {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void MessageCard(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String message, @org.jetbrains.annotations.NotNull()
    java.lang.String time, boolean isRead, @org.jetbrains.annotations.NotNull()
    java.lang.String type) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ApprovalRequestCard(@org.jetbrains.annotations.NotNull()
    java.lang.String customerName, @org.jetbrains.annotations.NotNull()
    java.lang.String referrerName, @org.jetbrains.annotations.NotNull()
    java.lang.String requestTime, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onApprove, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onReject) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SellersManagementScreen() {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SellerCard(@org.jetbrains.annotations.NotNull()
    java.lang.String sellerName, @org.jetbrains.annotations.NotNull()
    java.lang.String sellerEmail, int customerCount, @org.jetbrains.annotations.NotNull()
    java.lang.String totalSales, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SellerDetailsScreen(@org.jetbrains.annotations.NotNull()
    java.lang.String sellerId) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SellerCustomersTab(@org.jetbrains.annotations.NotNull()
    java.lang.String sellerId) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SellerStatsTab(@org.jetbrains.annotations.NotNull()
    java.lang.String sellerId) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SellerInvoicesTab(@org.jetbrains.annotations.NotNull()
    java.lang.String sellerId) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void CustomerSummaryCard(@org.jetbrains.annotations.NotNull()
    java.lang.String customerName, @org.jetbrains.annotations.NotNull()
    java.lang.String totalPurchases, @org.jetbrains.annotations.NotNull()
    java.lang.String totalPayments, @org.jetbrains.annotations.NotNull()
    java.lang.String balance, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void InvoiceSummaryCard(@org.jetbrains.annotations.NotNull()
    java.lang.String invoiceNumber, @org.jetbrains.annotations.NotNull()
    java.lang.String customerName, @org.jetbrains.annotations.NotNull()
    java.lang.String amount, @org.jetbrains.annotations.NotNull()
    java.lang.String date, @org.jetbrains.annotations.NotNull()
    java.lang.String status) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void DetailsScreen() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DetailedInvoicesTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DetailedPaymentsTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void FinancialReportTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DetailedStatsTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DetailedInvoiceCard(@org.jetbrains.annotations.NotNull()
    java.lang.String invoiceNumber, @org.jetbrains.annotations.NotNull()
    java.lang.String sellerName, @org.jetbrains.annotations.NotNull()
    java.lang.String customerName, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> items, @org.jetbrains.annotations.NotNull()
    java.lang.String totalAmount, @org.jetbrains.annotations.NotNull()
    java.lang.String paidAmount, @org.jetbrains.annotations.NotNull()
    java.lang.String remainingAmount, @org.jetbrains.annotations.NotNull()
    java.lang.String date, @org.jetbrains.annotations.NotNull()
    java.lang.String paymentType) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DetailedPaymentCard(@org.jetbrains.annotations.NotNull()
    java.lang.String paymentId, @org.jetbrains.annotations.NotNull()
    java.lang.String invoiceNumber, @org.jetbrains.annotations.NotNull()
    java.lang.String customerName, @org.jetbrains.annotations.NotNull()
    java.lang.String sellerName, @org.jetbrains.annotations.NotNull()
    java.lang.String amount, @org.jetbrains.annotations.NotNull()
    java.lang.String paymentMethod, @org.jetbrains.annotations.NotNull()
    java.lang.String date, @org.jetbrains.annotations.NotNull()
    java.lang.String status) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SellerPerformanceRow(@org.jetbrains.annotations.NotNull()
    java.lang.String sellerName, @org.jetbrains.annotations.NotNull()
    java.lang.String sales, int customers, int performance) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TimeStatsRow(@org.jetbrains.annotations.NotNull()
    java.lang.String period, @org.jetbrains.annotations.NotNull()
    java.lang.String sales, @org.jetbrains.annotations.NotNull()
    java.lang.String invoices) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ProductStatsRow(@org.jetbrains.annotations.NotNull()
    java.lang.String productName, int soldCount, @org.jetbrains.annotations.NotNull()
    java.lang.String revenue) {
    }
}