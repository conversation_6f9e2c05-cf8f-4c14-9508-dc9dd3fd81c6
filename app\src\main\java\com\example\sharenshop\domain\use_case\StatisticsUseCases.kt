package com.example.sharenshop.domain.use_case

import com.example.sharenshop.domain.repository.StatisticsRepository
import kotlinx.coroutines.flow.Flow
import java.math.BigDecimal

// Grouping all statistics-related use cases
data class StatisticsUseCases(
    val getTotalSalesAmount: GetTotalSalesAmount,
    val getTotalProductsSold: GetTotalProductsSold,
    val getTopSellingProducts: GetTopSellingProducts,
    val getCustomerSpending: GetCustomerSpending,
    val getSalesByPaymentStatus: GetSalesByPaymentStatus,
    val getSalesByTimePeriod: GetSalesByTimePeriod
)

class GetTotalSalesAmount(private val repository: StatisticsRepository) {
    operator fun invoke(): Flow<BigDecimal> {
        return repository.getTotalSalesAmount()
    }
}

class GetTotalProductsSold(private val repository: StatisticsRepository) {
    operator fun invoke(): Flow<Int> {
        return repository.getTotalProductsSold()
    }
}

class GetTopSellingProducts(private val repository: StatisticsRepository) {
    operator fun invoke(limit: Int): Flow<Map<String, Int>> {
        return repository.getTopSellingProducts(limit)
    }
}

class GetCustomerSpending(private val repository: StatisticsRepository) {
    operator fun invoke(customerId: String): Flow<BigDecimal> {
        return repository.getCustomerSpending(customerId)
    }
}

class GetSalesByPaymentStatus(private val repository: StatisticsRepository) {
    operator fun invoke(status: String): Flow<BigDecimal> {
        return repository.getSalesByPaymentStatus(status)
    }
}

class GetSalesByTimePeriod(private val repository: StatisticsRepository) {
    operator fun invoke(startTime: Long, endTime: Long): Flow<BigDecimal> {
        return repository.getSalesByTimePeriod(startTime, endTime)
    }
} 