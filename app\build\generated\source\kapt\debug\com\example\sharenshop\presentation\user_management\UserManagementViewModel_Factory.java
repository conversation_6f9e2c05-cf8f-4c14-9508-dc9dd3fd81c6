// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.presentation.user_management;

import com.example.sharenshop.domain.use_case.UserUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserManagementViewModel_Factory implements Factory<UserManagementViewModel> {
  private final Provider<UserUseCases> userUseCasesProvider;

  public UserManagementViewModel_Factory(Provider<UserUseCases> userUseCasesProvider) {
    this.userUseCasesProvider = userUseCasesProvider;
  }

  @Override
  public UserManagementViewModel get() {
    return newInstance(userUseCasesProvider.get());
  }

  public static UserManagementViewModel_Factory create(
      Provider<UserUseCases> userUseCasesProvider) {
    return new UserManagementViewModel_Factory(userUseCasesProvider);
  }

  public static UserManagementViewModel newInstance(UserUseCases userUseCases) {
    return new UserManagementViewModel(userUseCases);
  }
}
