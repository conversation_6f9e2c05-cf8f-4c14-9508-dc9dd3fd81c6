package com.example.sharenshop.data.security

import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FileEncryptionService @Inject constructor() : EncryptionService {
    override fun encrypt(data: String): String {
        // TODO: Implement actual file encryption logic
        return "ENCRYPTED_FILE:" + data
    }

    override fun decrypt(encryptedData: String): String {
        // TODO: Implement actual file decryption logic
        return encryptedData.replace("ENCRYPTED_FILE:", "")
    }
} 