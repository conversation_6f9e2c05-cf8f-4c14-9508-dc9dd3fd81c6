

plugins {
    // Apply the Android Application plugin to the app module
    alias(libs.plugins.android.application) apply false
    // Apply the Kotlin Android plugin
    alias(libs.plugins.kotlin.android) apply false
    // Apply the Hilt plugin for dependency injection
    alias(libs.plugins.hilt.android) apply false
    // Apply the Kotlin Serialization plugin
    alias(libs.plugins.kotlin.serialization) apply false
}

buildscript {
    dependencies {
        classpath("com.android.tools.build:gradle:8.0.2")
        // Define classpath for navigation safe args plugin
        // classpath "androidx.navigation:navigation-safe-args-gradle-plugin:2.7.7"
    }
}

tasks.register<Delete>("clean") {
    delete(layout.buildDirectory)
}