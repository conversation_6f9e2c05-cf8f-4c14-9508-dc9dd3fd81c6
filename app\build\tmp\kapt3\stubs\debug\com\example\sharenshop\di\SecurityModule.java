package com.example.sharenshop.di;

@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 \u00072\u00020\u0001:\u0001\u0007B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\'\u00a8\u0006\b"}, d2 = {"Lcom/example/sharenshop/di/SecurityModule;", "", "()V", "bindSecurityRepository", "Lcom/example/sharenshop/domain/repository/SecurityRepository;", "securityRepositoryImpl", "Lcom/example/sharenshop/data/repository/SecurityRepositoryImpl;", "Companion", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public abstract class SecurityModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.sharenshop.di.SecurityModule.Companion Companion = null;
    
    public SecurityModule() {
        super();
    }
    
    @dagger.Binds()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.domain.repository.SecurityRepository bindSecurityRepository(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.repository.SecurityRepositoryImpl securityRepositoryImpl);
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0007J\b\u0010\u0005\u001a\u00020\u0006H\u0007J\b\u0010\u0007\u001a\u00020\bH\u0007\u00a8\u0006\t"}, d2 = {"Lcom/example/sharenshop/di/SecurityModule$Companion;", "", "()V", "provideDatabaseEncryptionService", "Lcom/example/sharenshop/data/security/DatabaseEncryptionService;", "provideFileEncryptionService", "Lcom/example/sharenshop/data/security/FileEncryptionService;", "provideSharedPreferencesEncryptionService", "Lcom/example/sharenshop/data/security/SharedPreferencesEncryptionService;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @dagger.Provides()
        @javax.inject.Singleton()
        @org.jetbrains.annotations.NotNull()
        public final com.example.sharenshop.data.security.DatabaseEncryptionService provideDatabaseEncryptionService() {
            return null;
        }
        
        @dagger.Provides()
        @javax.inject.Singleton()
        @org.jetbrains.annotations.NotNull()
        public final com.example.sharenshop.data.security.FileEncryptionService provideFileEncryptionService() {
            return null;
        }
        
        @dagger.Provides()
        @javax.inject.Singleton()
        @org.jetbrains.annotations.NotNull()
        public final com.example.sharenshop.data.security.SharedPreferencesEncryptionService provideSharedPreferencesEncryptionService() {
            return null;
        }
    }
}