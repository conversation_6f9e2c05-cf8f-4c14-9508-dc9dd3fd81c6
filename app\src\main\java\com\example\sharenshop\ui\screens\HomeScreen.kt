package com.example.sharenshop.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
// import androidx.navigation.NavHostController // ایمپورت حذف شد چون پارامتر حذف می‌شود
import androidx.compose.ui.tooling.preview.Preview

@Composable
fun HomeScreen() { // پارامتر navController حذف شد
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(text = "Home Screen")
        // TODO: Implement dashboard, quick access features, etc.
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewHomeScreen() {
    HomeScreen() // فراخوانی بدون navController
}