package com.example.sharenshop.di;

@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0005\u001a\u00020\tH\u0007J\u0010\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0005\u001a\u00020\fH\u0007J\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0005\u001a\u00020\u000fH\u0007J\u0010\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0005\u001a\u00020\u0012H\u0007J\u0010\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0005\u001a\u00020\u0015H\u0007J\u0010\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0005\u001a\u00020\u0018H\u0007J\u0010\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u0005\u001a\u00020\u001bH\u0007\u00a8\u0006\u001c"}, d2 = {"Lcom/example/sharenshop/di/UseCaseModule;", "", "()V", "provideCustomerUseCases", "Lcom/example/sharenshop/domain/use_case/CustomerUseCases;", "repository", "Lcom/example/sharenshop/domain/repository/CustomerRepository;", "provideInvoiceUseCases", "Lcom/example/sharenshop/domain/use_case/InvoiceUseCases;", "Lcom/example/sharenshop/domain/repository/InvoiceRepository;", "provideProductUseCases", "Lcom/example/sharenshop/domain/use_case/ProductUseCases;", "Lcom/example/sharenshop/domain/repository/ProductRepository;", "provideSecurityUseCases", "Lcom/example/sharenshop/domain/use_case/SecurityUseCases;", "Lcom/example/sharenshop/domain/repository/SecurityRepository;", "provideSellerUseCases", "Lcom/example/sharenshop/domain/use_case/SellerUseCases;", "Lcom/example/sharenshop/domain/repository/SellerRepository;", "provideSettingsUseCases", "Lcom/example/sharenshop/domain/use_case/SettingsUseCases;", "Lcom/example/sharenshop/domain/repository/SettingsRepository;", "provideStatisticsUseCases", "Lcom/example/sharenshop/domain/use_case/StatisticsUseCases;", "Lcom/example/sharenshop/domain/repository/StatisticsRepository;", "provideUserUseCases", "Lcom/example/sharenshop/domain/use_case/UserUseCases;", "Lcom/example/sharenshop/domain/repository/UserRepository;", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class UseCaseModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.sharenshop.di.UseCaseModule INSTANCE = null;
    
    private UseCaseModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.UserUseCases provideUserUseCases(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.UserRepository repository) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.ProductUseCases provideProductUseCases(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.ProductRepository repository) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.CustomerUseCases provideCustomerUseCases(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.CustomerRepository repository) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.SellerUseCases provideSellerUseCases(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.SellerRepository repository) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.InvoiceUseCases provideInvoiceUseCases(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.InvoiceRepository repository) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.SecurityUseCases provideSecurityUseCases(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.SecurityRepository repository) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.SettingsUseCases provideSettingsUseCases(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.SettingsRepository repository) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.StatisticsUseCases provideStatisticsUseCases(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.StatisticsRepository repository) {
        return null;
    }
}