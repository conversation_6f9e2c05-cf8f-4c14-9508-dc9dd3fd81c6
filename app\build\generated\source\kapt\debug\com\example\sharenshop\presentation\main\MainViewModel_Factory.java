// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.presentation.main;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainViewModel_Factory implements Factory<MainViewModel> {
  @Override
  public MainViewModel get() {
    return newInstance();
  }

  public static MainViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static MainViewModel newInstance() {
    return new MainViewModel();
  }

  private static final class InstanceHolder {
    private static final MainViewModel_Factory INSTANCE = new MainViewModel_Factory();
  }
}
