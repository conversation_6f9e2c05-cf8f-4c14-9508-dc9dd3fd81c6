{"logs": [{"outputFile": "com.example.sharenshop.app-mergeDebugResources-2:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8bf4b3fb7fecc46740c95cb34de6afa1\\transformed\\core-1.12.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "2,3,4,5,6,7,8,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,301,506,707,908,1115,1320,16641", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "296,501,702,903,1110,1315,1527,16840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\beb880474af4802d615c3712a7fe5cf4\\transformed\\browser-1.8.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,255,454,665", "endColumns": "199,198,210,201", "endOffsets": "250,449,660,862"}, "to": {"startLines": "11,15,16,17", "startColumns": "4,4,4,4", "startOffsets": "1908,2694,2893,3104", "endColumns": "199,198,210,201", "endOffsets": "2103,2888,3099,3301"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88b5b0acc1ef090d93ba86ccb1fae5cf\\transformed\\foundation-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,243", "endColumns": "187,186", "endOffsets": "238,425"}, "to": {"startLines": "87,88", "startColumns": "4,4", "startOffsets": "17429,17617", "endColumns": "187,186", "endOffsets": "17612,17799"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\61acd70f2f91261ed7f816e4fc4161c1\\transformed\\ui-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,296,481,678,880,1067,1252,1445,1633,1820,1985,2152,2333,2518,2684,2863,3030", "endColumns": "190,184,196,201,186,184,192,187,186,164,166,180,184,165,178,166,237", "endOffsets": "291,476,673,875,1062,1247,1440,1628,1815,1980,2147,2328,2513,2679,2858,3025,3263"}, "to": {"startLines": "9,10,12,13,14,18,19,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1532,1723,2108,2305,2507,3306,3491,15402,15590,15777,15942,16109,16290,16475,16845,17024,17191", "endColumns": "190,184,196,201,186,184,192,187,186,164,166,180,184,165,178,166,237", "endOffsets": "1718,1903,2300,2502,2689,3486,3679,15585,15772,15937,16104,16285,16470,16636,17019,17186,17424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88c123eabfc6d4b207c77cc8294f818f\\transformed\\material3-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,275,492,706,923,1124,1322,1532,1771,1989,2228,2414,2614,2808,3008,3229,3457,3664,3894,4120,4349,4610,4832,5051,5272,5497,5692,5892,6110,6336,6535,6738,6943,7173,7414,7623,7824,8003,8201,8397,8587,8777,8982,9164,9350,9554,9758,9960,10162,10352,10561,10765,10972,11191,11374,11575", "endColumns": "219,216,213,216,200,197,209,238,217,238,185,199,193,199,220,227,206,229,225,228,260,221,218,220,224,194,199,217,225,198,202,204,229,240,208,200,178,197,195,189,189,204,181,185,203,203,201,201,189,208,203,206,218,182,200,197", "endOffsets": "270,487,701,918,1119,1317,1527,1766,1984,2223,2409,2609,2803,3003,3224,3452,3659,3889,4115,4344,4605,4827,5046,5267,5492,5687,5887,6105,6331,6530,6733,6938,7168,7409,7618,7819,7998,8196,8392,8582,8772,8977,9159,9345,9549,9753,9955,10157,10347,10556,10760,10967,11186,11369,11570,11768"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3684,3904,4121,4335,4552,4753,4951,5161,5400,5618,5857,6043,6243,6437,6637,6858,7086,7293,7523,7749,7978,8239,8461,8680,8901,9126,9321,9521,9739,9965,10164,10367,10572,10802,11043,11252,11453,11632,11830,12026,12216,12406,12611,12793,12979,13183,13387,13589,13791,13981,14190,14394,14601,14820,15003,15204", "endColumns": "219,216,213,216,200,197,209,238,217,238,185,199,193,199,220,227,206,229,225,228,260,221,218,220,224,194,199,217,225,198,202,204,229,240,208,200,178,197,195,189,189,204,181,185,203,203,201,201,189,208,203,206,218,182,200,197", "endOffsets": "3899,4116,4330,4547,4748,4946,5156,5395,5613,5852,6038,6238,6432,6632,6853,7081,7288,7518,7744,7973,8234,8456,8675,8896,9121,9316,9516,9734,9960,10159,10362,10567,10797,11038,11247,11448,11627,11825,12021,12211,12401,12606,12788,12974,13178,13382,13584,13786,13976,14185,14389,14596,14815,14998,15199,15397"}}]}]}