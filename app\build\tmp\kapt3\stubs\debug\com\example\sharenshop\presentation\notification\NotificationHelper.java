package com.example.sharenshop.presentation.notification;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\b\u001a\u00020\u0006J\u0016\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u0006J\u001e\u0010\u000b\u001a\u00020\u00042\u0006\u0010\f\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\u0006\u00a8\u0006\u000f"}, d2 = {"Lcom/example/sharenshop/presentation/notification/NotificationHelper;", "", "()V", "createCustomerApprovalRequest", "Lcom/example/sharenshop/data/model/Notification;", "customerName", "", "customerEmail", "referrerUserId", "createCustomerApproved", "customerUserId", "createPaymentNotification", "amount", "invoiceId", "receiverUserId", "app_debug"})
public final class NotificationHelper {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.sharenshop.presentation.notification.NotificationHelper INSTANCE = null;
    
    private NotificationHelper() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.model.Notification createCustomerApprovalRequest(@org.jetbrains.annotations.NotNull()
    java.lang.String customerName, @org.jetbrains.annotations.NotNull()
    java.lang.String customerEmail, @org.jetbrains.annotations.NotNull()
    java.lang.String referrerUserId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.model.Notification createCustomerApproved(@org.jetbrains.annotations.NotNull()
    java.lang.String customerName, @org.jetbrains.annotations.NotNull()
    java.lang.String customerUserId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.model.Notification createPaymentNotification(@org.jetbrains.annotations.NotNull()
    java.lang.String amount, @org.jetbrains.annotations.NotNull()
    java.lang.String invoiceId, @org.jetbrains.annotations.NotNull()
    java.lang.String receiverUserId) {
        return null;
    }
}