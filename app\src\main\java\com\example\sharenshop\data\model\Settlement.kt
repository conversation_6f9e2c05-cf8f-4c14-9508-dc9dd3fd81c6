package com.example.sharenshop.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import java.math.BigDecimal

/**
 * مدل تصویه حساب - برای تصویه فروشنده با مدیر
 */
@Serializable
@Entity(tableName = "settlements")
data class Settlement(
    @PrimaryKey(autoGenerate = false)
    val id: String,
    val sellerId: String, // فروشنده تصویه کننده
    val managerId: String, // مدیر تایید کننده
    @Contextual
    val amount: BigDecimal, // مبلغ تصویه
    val settlementType: SettlementType, // نوع تصویه
    val description: String? = null, // توضیحات
    val trackingNumber: String? = null, // شماره پیگیری (در صورت انتقال)
    val status: SettlementStatus, // وضعیت تصویه
    val requestDate: Long, // تاریخ درخواست
    val responseDate: Long? = null, // تاریخ پاسخ مدیر
    val rejectionReason: String? = null, // دلیل رد
    val createdAt: Long,
    val updatedAt: Long? = null
)

/**
 * نوع تصویه حساب
 */
@Serializable
enum class SettlementType(val value: String, val displayName: String) {
    CASH_RECEIVED("cash_received", "پول نقد دریافتی"),
    CREDIT_COLLECTION("credit_collection", "وصول نسیه"),
    COMMISSION_SETTLEMENT("commission_settlement", "تصویه کمیسیون"),
    OTHER("other", "سایر");
    
    companion object {
        fun fromString(value: String): SettlementType {
            return values().find { it.value == value } ?: CASH_RECEIVED
        }
    }
}

/**
 * وضعیت تصویه حساب
 */
@Serializable
enum class SettlementStatus(val value: String, val displayName: String) {
    PENDING("pending", "در انتظار بررسی"),
    APPROVED("approved", "تایید شده"),
    REJECTED("rejected", "رد شده"),
    PARTIALLY_APPROVED("partially_approved", "تایید جزئی");
    
    companion object {
        fun fromString(value: String): SettlementStatus {
            return values().find { it.value == value } ?: PENDING
        }
    }
}

/**
 * خلاصه تصویه حساب فروشنده
 */
data class SellerSettlementSummary(
    val sellerId: String,
    val sellerName: String,
    @Contextual
    val totalCashReceived: BigDecimal, // کل پول نقد دریافتی
    @Contextual
    val totalCreditCollected: BigDecimal, // کل نسیه وصول شده
    @Contextual
    val totalSettled: BigDecimal, // کل تصویه شده
    @Contextual
    val pendingSettlement: BigDecimal, // در انتظار تصویه
    @Contextual
    val remainingBalance: BigDecimal, // مانده حساب
    val lastSettlementDate: Long? = null
)

/**
 * جزئیات کامل تصویه برای نمایش
 */
data class SettlementDetails(
    val settlement: Settlement,
    val sellerName: String,
    val managerName: String? = null,
    val relatedPayments: List<Payment> = emptyList() // پرداخت‌های مرتبط
)
