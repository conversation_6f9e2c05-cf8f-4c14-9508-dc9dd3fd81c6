// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.CustomerApprovalRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetPendingRequests_Factory implements Factory<GetPendingRequests> {
  private final Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider;

  public GetPendingRequests_Factory(
      Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider) {
    this.customerApprovalRepositoryProvider = customerApprovalRepositoryProvider;
  }

  @Override
  public GetPendingRequests get() {
    return newInstance(customerApprovalRepositoryProvider.get());
  }

  public static GetPendingRequests_Factory create(
      Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider) {
    return new GetPendingRequests_Factory(customerApprovalRepositoryProvider);
  }

  public static GetPendingRequests newInstance(
      CustomerApprovalRepository customerApprovalRepository) {
    return new GetPendingRequests(customerApprovalRepository);
  }
}
