{"logs": [{"outputFile": "com.example.sharenshop.app-mergeDebugResources-2:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8bf4b3fb7fecc46740c95cb34de6afa1\\transformed\\core-1.12.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "2,3,4,5,6,7,8,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,199,301,398,495,596,696,8312", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "194,296,393,490,591,691,797,8408"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88c123eabfc6d4b207c77cc8294f818f\\transformed\\material3-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,288,406,521,620,715,828,955,1070,1210,1295,1393,1484,1580,1697,1817,1920,2056,2191,2312,2465,2583,2693,2808,2926,3018,3115,3227,3351,3449,3548,3652,3786,3927,4034,4134,4215,4320,4424,4510,4595,4698,4778,4862,4963,5062,5153,5248,5334,5436,5535,5632,5757,5837,5938", "endColumns": "116,115,117,114,98,94,112,126,114,139,84,97,90,95,116,119,102,135,134,120,152,117,109,114,117,91,96,111,123,97,98,103,133,140,106,99,80,104,103,85,84,102,79,83,100,98,90,94,85,101,98,96,124,79,100,95", "endOffsets": "167,283,401,516,615,710,823,950,1065,1205,1290,1388,1479,1575,1692,1812,1915,2051,2186,2307,2460,2578,2688,2803,2921,3013,3110,3222,3346,3444,3543,3647,3781,3922,4029,4129,4210,4315,4419,4505,4590,4693,4773,4857,4958,5057,5148,5243,5329,5431,5530,5627,5752,5832,5933,6029"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1807,1924,2040,2158,2273,2372,2467,2580,2707,2822,2962,3047,3145,3236,3332,3449,3569,3672,3808,3943,4064,4217,4335,4445,4560,4678,4770,4867,4979,5103,5201,5300,5404,5538,5679,5786,5886,5967,6072,6176,6262,6347,6450,6530,6614,6715,6814,6905,7000,7086,7188,7287,7384,7509,7589,7690", "endColumns": "116,115,117,114,98,94,112,126,114,139,84,97,90,95,116,119,102,135,134,120,152,117,109,114,117,91,96,111,123,97,98,103,133,140,106,99,80,104,103,85,84,102,79,83,100,98,90,94,85,101,98,96,124,79,100,95", "endOffsets": "1919,2035,2153,2268,2367,2462,2575,2702,2817,2957,3042,3140,3231,3327,3444,3564,3667,3803,3938,4059,4212,4330,4440,4555,4673,4765,4862,4974,5098,5196,5295,5399,5533,5674,5781,5881,5962,6067,6171,6257,6342,6445,6525,6609,6710,6809,6900,6995,7081,7183,7282,7379,7504,7584,7685,7781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\beb880474af4802d615c3712a7fe5cf4\\transformed\\browser-1.8.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "11,15,16,17", "startColumns": "4,4,4,4", "startOffsets": "975,1337,1437,1543", "endColumns": "90,99,105,101", "endOffsets": "1061,1432,1538,1640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88b5b0acc1ef090d93ba86ccb1fae5cf\\transformed\\foundation-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,87", "endOffsets": "137,225"}, "to": {"startLines": "87,88", "startColumns": "4,4", "startOffsets": "8677,8764", "endColumns": "86,87", "endOffsets": "8759,8847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\61acd70f2f91261ed7f816e4fc4161c1\\transformed\\ui-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,278,371,466,549,626,711,797,876,942,1008,1086,1168,1237,1311,1382", "endColumns": "88,83,92,94,82,76,84,85,78,65,65,77,81,68,73,70,118", "endOffsets": "189,273,366,461,544,621,706,792,871,937,1003,1081,1163,1232,1306,1377,1496"}, "to": {"startLines": "9,10,12,13,14,18,19,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "802,891,1066,1159,1254,1645,1722,7786,7872,7951,8017,8083,8161,8243,8413,8487,8558", "endColumns": "88,83,92,94,82,76,84,85,78,65,65,77,81,68,73,70,118", "endOffsets": "886,970,1154,1249,1332,1717,1802,7867,7946,8012,8078,8156,8238,8307,8482,8553,8672"}}]}]}