package com.example.sharenshop.utils

import com.example.sharenshop.data.model.Permission
import com.example.sharenshop.data.model.RolePermissions
import com.example.sharenshop.data.model.User
import com.example.sharenshop.data.model.UserRole

/**
 * Helper class for checking user permissions
 */
object PermissionHelper {
    
    /**
     * Check if a user has a specific permission
     */
    fun hasPermission(user: User?, permission: Permission): <PERSON><PERSON><PERSON> {
        if (user == null) return false
        return RolePermissions.hasPermission(user.role, permission)
    }
    
    /**
     * Check if a user role has a specific permission
     */
    fun hasPermission(userRole: UserRole, permission: Permission): <PERSON><PERSON><PERSON> {
        return RolePermissions.hasPermission(userRole, permission)
    }
    
    /**
     * Check if a user has any of the specified permissions
     */
    fun hasAnyPermission(user: User?, vararg permissions: Permission): <PERSON><PERSON><PERSON> {
        if (user == null) return false
        return permissions.any { hasPermission(user, it) }
    }
    
    /**
     * Check if a user has all of the specified permissions
     */
    fun hasAllPermissions(user: User?, vararg permissions: Permission): <PERSON><PERSON>an {
        if (user == null) return false
        return permissions.all { hasPermission(user, it) }
    }
    
    /**
     * Get all permissions for a user
     */
    fun getUserPermissions(user: User?): Set<Permission> {
        if (user == null) return emptySet()
        return RolePermissions.getPermissionsForRole(user.role)
    }
    
    /**
     * Check if user is super admin
     */
    fun isSuperAdmin(user: User?): Boolean {
        return user?.role == UserRole.SUPER_ADMIN
    }
    
    /**
     * Check if user is cashier
     */
    fun isCashier(user: User?): Boolean {
        return user?.role == UserRole.CASHIER
    }
    
    /**
     * Check if user is customer
     */
    fun isCustomer(user: User?): Boolean {
        return user?.role == UserRole.CUSTOMER
    }
    
    /**
     * Check if user can manage users
     */
    fun canManageUsers(user: User?): Boolean {
        return hasPermission(user, Permission.CREATE_USERS) ||
               hasPermission(user, Permission.EDIT_USERS) ||
               hasPermission(user, Permission.DELETE_USERS)
    }
    
    /**
     * Check if user can manage products
     */
    fun canManageProducts(user: User?): Boolean {
        return hasPermission(user, Permission.CREATE_PRODUCTS) ||
               hasPermission(user, Permission.EDIT_PRODUCTS) ||
               hasPermission(user, Permission.DELETE_PRODUCTS)
    }
    
    /**
     * Check if user can manage invoices
     */
    fun canManageInvoices(user: User?): Boolean {
        return hasPermission(user, Permission.CREATE_INVOICES) ||
               hasPermission(user, Permission.EDIT_INVOICES) ||
               hasPermission(user, Permission.DELETE_INVOICES)
    }
    
    /**
     * Check if user can view statistics
     */
    fun canViewStatistics(user: User?): Boolean {
        return hasPermission(user, Permission.VIEW_STATISTICS)
    }
    
    /**
     * Check if user can manage settings
     */
    fun canManageSettings(user: User?): Boolean {
        return hasPermission(user, Permission.MANAGE_SETTINGS)
    }

    /**
     * Check if user can view all sellers statistics
     */
    fun canViewAllSellersStats(user: User?): Boolean {
        return hasPermission(user, Permission.VIEW_ALL_SELLERS_STATS)
    }

    /**
     * Check if user can view cash sales
     */
    fun canViewCashSales(user: User?): Boolean {
        return hasPermission(user, Permission.VIEW_CASH_SALES)
    }

    /**
     * Check if user can view credit sales
     */
    fun canViewCreditSales(user: User?): Boolean {
        return hasPermission(user, Permission.VIEW_CREDIT_SALES)
    }

    /**
     * Check if user can view invoice count
     */
    fun canViewInvoiceCount(user: User?): Boolean {
        return hasPermission(user, Permission.VIEW_INVOICE_COUNT)
    }

    /**
     * Check if user can view customer additions
     */
    fun canViewCustomerAdditions(user: User?): Boolean {
        return hasPermission(user, Permission.VIEW_CUSTOMER_ADDITIONS)
    }
}
