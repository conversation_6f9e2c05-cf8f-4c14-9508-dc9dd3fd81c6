package com.example.sharenshop.domain.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0003H&J\u0010\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0003H&J\u000e\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH&J\u0016\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\tH\u00a6@\u00a2\u0006\u0002\u0010\r\u00a8\u0006\u000e"}, d2 = {"Lcom/example/sharenshop/domain/repository/SecurityRepository;", "", "decryptData", "", "encryptedData", "encryptData", "data", "getEncryptionSettings", "Lkotlinx/coroutines/flow/Flow;", "Lcom/example/sharenshop/data/model/EncryptionSettings;", "saveEncryptionSettings", "", "settings", "(Lcom/example/sharenshop/data/model/EncryptionSettings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface SecurityRepository {
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.example.sharenshop.data.model.EncryptionSettings> getEncryptionSettings();
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object saveEncryptionSettings(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.EncryptionSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @org.jetbrains.annotations.NotNull()
    public abstract java.lang.String encryptData(@org.jetbrains.annotations.NotNull()
    java.lang.String data);
    
    @org.jetbrains.annotations.NotNull()
    public abstract java.lang.String decryptData(@org.jetbrains.annotations.NotNull()
    java.lang.String encryptedData);
}