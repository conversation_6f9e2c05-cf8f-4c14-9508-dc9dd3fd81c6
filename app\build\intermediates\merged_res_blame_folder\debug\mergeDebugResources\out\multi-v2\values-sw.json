{"logs": [{"outputFile": "com.example.sharenshop.app-mergeDebugResources-2:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\beb880474af4802d615c3712a7fe5cf4\\transformed\\browser-1.8.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,270,387", "endColumns": "113,100,116,102", "endOffsets": "164,265,382,485"}, "to": {"startLines": "11,15,16,17", "startColumns": "4,4,4,4", "startOffsets": "1003,1405,1506,1623", "endColumns": "113,100,116,102", "endOffsets": "1112,1501,1618,1721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88b5b0acc1ef090d93ba86ccb1fae5cf\\transformed\\foundation-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,102", "endOffsets": "151,254"}, "to": {"startLines": "87,88", "startColumns": "4,4", "startOffsets": "8982,9083", "endColumns": "100,102", "endOffsets": "9078,9181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\61acd70f2f91261ed7f816e4fc4161c1\\transformed\\ui-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,280,381,482,568,649,750,841,923,993,1064,1149,1236,1310,1387,1457", "endColumns": "93,80,100,100,85,80,100,90,81,69,70,84,86,73,76,69,120", "endOffsets": "194,275,376,477,563,644,745,836,918,988,1059,1144,1231,1305,1382,1452,1573"}, "to": {"startLines": "9,10,12,13,14,18,19,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "828,922,1117,1218,1319,1726,1807,8053,8144,8226,8296,8367,8452,8539,8714,8791,8861", "endColumns": "93,80,100,100,85,80,100,90,81,69,70,84,86,73,76,69,120", "endOffsets": "917,998,1213,1314,1400,1802,1903,8139,8221,8291,8362,8447,8534,8608,8786,8856,8977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8bf4b3fb7fecc46740c95cb34de6afa1\\transformed\\core-1.12.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,199,301,398,499,606,713,8613", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "194,296,393,494,601,708,823,8709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88c123eabfc6d4b207c77cc8294f818f\\transformed\\material3-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,286,402,516,616,715,831,972,1088,1239,1325,1425,1518,1620,1738,1865,1970,2100,2229,2365,2530,2659,2783,2912,3021,3115,3211,3334,3462,3559,3671,3781,3913,4054,4166,4266,4345,4441,4538,4625,4710,4824,4904,4987,5086,5186,5281,5380,5468,5573,5673,5776,5892,5972,6090", "endColumns": "115,114,115,113,99,98,115,140,115,150,85,99,92,101,117,126,104,129,128,135,164,128,123,128,108,93,95,122,127,96,111,109,131,140,111,99,78,95,96,86,84,113,79,82,98,99,94,98,87,104,99,102,115,79,117,109", "endOffsets": "166,281,397,511,611,710,826,967,1083,1234,1320,1420,1513,1615,1733,1860,1965,2095,2224,2360,2525,2654,2778,2907,3016,3110,3206,3329,3457,3554,3666,3776,3908,4049,4161,4261,4340,4436,4533,4620,4705,4819,4899,4982,5081,5181,5276,5375,5463,5568,5668,5771,5887,5967,6085,6195"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1908,2024,2139,2255,2369,2469,2568,2684,2825,2941,3092,3178,3278,3371,3473,3591,3718,3823,3953,4082,4218,4383,4512,4636,4765,4874,4968,5064,5187,5315,5412,5524,5634,5766,5907,6019,6119,6198,6294,6391,6478,6563,6677,6757,6840,6939,7039,7134,7233,7321,7426,7526,7629,7745,7825,7943", "endColumns": "115,114,115,113,99,98,115,140,115,150,85,99,92,101,117,126,104,129,128,135,164,128,123,128,108,93,95,122,127,96,111,109,131,140,111,99,78,95,96,86,84,113,79,82,98,99,94,98,87,104,99,102,115,79,117,109", "endOffsets": "2019,2134,2250,2364,2464,2563,2679,2820,2936,3087,3173,3273,3366,3468,3586,3713,3818,3948,4077,4213,4378,4507,4631,4760,4869,4963,5059,5182,5310,5407,5519,5629,5761,5902,6014,6114,6193,6289,6386,6473,6558,6672,6752,6835,6934,7034,7129,7228,7316,7421,7521,7624,7740,7820,7938,8048"}}]}]}