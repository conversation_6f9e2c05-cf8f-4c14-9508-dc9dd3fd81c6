package com.example.sharenshop.ui.screens

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.material3.TabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.FilterChip
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.sharenshop.data.model.UserRole
import com.example.sharenshop.presentation.main.MainViewModel
import com.example.sharenshop.ui.utils.*

// Data class برای آیتم‌های منو
data class MenuItem(
    val title: String,
    val icon: ImageVector,
    val route: String,
    val color: Color = Color.Unspecified
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    currentUserRole: UserRole,
    onNavigateToAuth: () -> Unit,
    viewModel: MainViewModel = hiltViewModel()
) {
    val navController = rememberNavController()
    var showUserProfileDialog by remember { mutableStateOf(false) }

    android.util.Log.d("MainScreen", "MainScreen displayed with role: $currentUserRole")

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "SharenShop",
                        fontWeight = FontWeight.Bold,
                        fontSize = 24.sp, // اندازه استاندارد
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary
                ),
                actions = {
                    // نقش کاربر کنار آیکون
                    Text(
                        text = when (currentUserRole) {
                            UserRole.SUPER_ADMIN -> "مدیر کل"
                            UserRole.CASHIER -> "فروشنده"
                            UserRole.CUSTOMER -> "مشتری"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.9f),
                        modifier = Modifier.padding(end = 8.dp)
                    )
                    IconButton(onClick = { showUserProfileDialog = true }) {
                        Icon(
                            Icons.Default.AccountCircle,
                            contentDescription = "پروفایل کاربری",
                            tint = MaterialTheme.colorScheme.onPrimary,
                            modifier = Modifier.size(32.dp) // اندازه استاندارد
                        )
                    }
                }
            )
        },
        bottomBar = {
            BottomNavigationBar(
                navController = navController,
                userRole = currentUserRole
            )
        }
    ) { paddingValues ->
        NavHost(
            navController = navController,
            startDestination = "dashboard",
            modifier = Modifier.padding(paddingValues)
        ) {
            composable("dashboard") {
                DashboardContent(
                    userRole = currentUserRole,
                    onNavigateToScreen = { route ->
                        navController.navigate(route)
                    }
                )
            }
            composable("products") {
                ProductScreen(userRole = currentUserRole)
            }
            composable("customers") {
                CustomerManagementScreen()
            }
            composable("invoices") {
                InvoiceScreen(userRole = currentUserRole)
            }
            composable("statistics") {
                MainStatisticsScreen()
            }
            composable("users") {
                MainUserManagementScreen()
            }
            composable("payments") {
                PaymentScreen()
            }
            composable("accounting") {
                AccountingScreen()
            }
            composable("settlement") {
                SettlementScreen()
            }
            composable("messages") {
                MessagesScreen(userRole = currentUserRole)
            }
            composable("sellers") {
                SellersManagementScreen()
            }
            composable("details") {
                DetailsScreen()
            }
        }
    }

    // نمایش دیالوگ پروفایل کاربری
    if (showUserProfileDialog) {
        UserProfileDialog(
            currentUserRole = currentUserRole,
            onDismiss = { showUserProfileDialog = false },
            onLogout = {
                showUserProfileDialog = false
                onNavigateToAuth()
            }
        )
    }
}

@Composable
fun DashboardContent(
    userRole: UserRole,
    onNavigateToScreen: (String) -> Unit
) {
    val adaptivePadding = getAdaptivePadding()
    val adaptiveSpacing = getAdaptiveSpacing()
    val isLandscapeMode = isLandscape()

    if (isLandscapeMode) {
        // Layout افقی - دو ستونه
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(adaptivePadding),
            horizontalArrangement = Arrangement.spacedBy(adaptiveSpacing)
        ) {
            // ستون چپ
            LazyColumn(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight(),
                verticalArrangement = Arrangement.spacedBy(adaptiveSpacing)
            ) {
                item {
                    WelcomeHeader(userRole)
                }
                item {
                    QuickStatsCards(userRole, onNavigateToScreen)
                }
            }

            // ستون راست
            LazyColumn(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight(),
                verticalArrangement = Arrangement.spacedBy(adaptiveSpacing)
            ) {
                item {
                    MainMenuGrid(
                        userRole = userRole,
                        onNavigateToScreen = onNavigateToScreen
                    )
                }
                item {
                    RecentActivities(userRole)
                }
            }
        }
    } else {
        // Layout عمودی - تک ستونه
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(adaptivePadding),
            verticalArrangement = Arrangement.spacedBy(adaptiveSpacing)
        ) {
            item {
                // Header با خوش‌آمدگویی
                WelcomeHeader(userRole)
            }

            item {
                // کارت‌های آمار سریع
                QuickStatsCards(userRole, onNavigateToScreen)
            }

            item {
                // منوی اصلی
                MainMenuGrid(
                    userRole = userRole,
                    onNavigateToScreen = onNavigateToScreen
                )
            }

            item {
                // فعالیت‌های اخیر
                RecentActivities(userRole)
            }
        }
    }
}

@Composable
fun WelcomeHeader(userRole: UserRole) {
    // انیمیشن‌های زیبا
    val infiniteTransition = rememberInfiniteTransition(label = "welcome_animation")

    val shimmerAlpha by infiniteTransition.animateFloat(
        initialValue = 0.7f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "shimmer"
    )

    val slideOffset by infiniteTransition.animateFloat(
        initialValue = -10f,
        targetValue = 10f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "slide"
    )

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(120.dp), // ارتفاع نصف شده
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        shape = RoundedCornerShape(20.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp) // padding کمتر
        ) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                // بیت اول - از راست شروع تا وسط با انیمیشن
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .offset(x = slideOffset.dp),
                    horizontalArrangement = Arrangement.End
                ) {
                    Text(
                        text = "نه هر انتخابی ارزش موندن داره",
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = shimmerAlpha),
                        modifier = Modifier.padding(end = 4.dp)
                    )
                }

                Spacer(modifier = Modifier.height(4.dp))

                // بیت دوم - از وسط شروع تا انتها با انیمیشن
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .offset(x = (-slideOffset).dp),
                    horizontalArrangement = Arrangement.Start
                ) {
                    Text(
                        text = "نه هر لباسی ارزش پوشیدن",
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = shimmerAlpha),
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                // متن اصلی در وسط با انیمیشن درخشش
                Text(
                    text = "پوشاک شارن انتخابی ارزشمند",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.ExtraBold,
                    color = MaterialTheme.colorScheme.primary.copy(alpha = shimmerAlpha),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .background(
                            brush = Brush.horizontalGradient(
                                colors = listOf(
                                    Color.Transparent,
                                    MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                                    Color.Transparent
                                )
                            ),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                )
            }
        }
    }
}

@Composable
fun QuickStatsCards(
    userRole: UserRole,
    onNavigateToScreen: (String) -> Unit = {}
) {
    val isLandscapeMode = isLandscape()
    val adaptiveSpacing = getAdaptiveSpacing()

    val stats = when (userRole) {
        UserRole.SUPER_ADMIN -> getSuperAdminStats()
        UserRole.CASHIER -> getCashierStats()
        UserRole.CUSTOMER -> getCustomerStats()
    }

    if (isLandscapeMode && stats.size > 2) {
        // در حالت افقی از Grid استفاده کن
        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            horizontalArrangement = Arrangement.spacedBy(adaptiveSpacing),
            verticalArrangement = Arrangement.spacedBy(adaptiveSpacing),
            modifier = Modifier.height((getStatsCardHeight() * 2) + adaptiveSpacing)
        ) {
            items(stats) { stat ->
                QuickStatCard(
                    stat = stat,
                    onClick = {
                        stat.route?.let { onNavigateToScreen(it) }
                    }
                )
            }
        }
    } else {
        // در حالت عمودی از Row استفاده کن
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(adaptiveSpacing),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            items(stats) { stat ->
                QuickStatCard(
                    stat = stat,
                    onClick = {
                        stat.route?.let { onNavigateToScreen(it) }
                    }
                )
            }
        }
    }
}

data class QuickStat(
    val title: String,
    val value: String,
    val icon: ImageVector,
    val color: Color,
    val route: String? = null
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QuickStatCard(
    stat: QuickStat,
    onClick: () -> Unit = {}
) {
    val cardWidth = getStatsCardWidth()
    val cardHeight = getStatsCardHeight()
    val isLandscapeMode = isLandscape()

    Card(
        onClick = onClick,
        modifier = Modifier
            .width(cardWidth)
            .height(cardHeight),
        colors = CardDefaults.cardColors(
            containerColor = stat.color.copy(alpha = 0.1f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(if (isLandscapeMode) 8.dp else 12.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Icon(
                imageVector = stat.icon,
                contentDescription = stat.title,
                tint = stat.color,
                modifier = Modifier.size(if (isLandscapeMode) 20.dp else 24.dp)
            )

            Column {
                Text(
                    text = stat.value,
                    style = if (isLandscapeMode) MaterialTheme.typography.titleMedium else MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = stat.color
                )
                Text(
                    text = stat.title,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    maxLines = if (isLandscapeMode) 1 else 2
                )
            }
        }
    }
}

// Helper functions برای آمار سریع
@Suppress("DEPRECATION")
fun getSuperAdminStats(): List<QuickStat> = listOf(
    QuickStat("پیام‌ها", "5", Icons.Default.Notifications, Color(0xFF4CAF50), "messages"),
    QuickStat("فاکتورهای جدید", "23", Icons.Default.List, Color(0xFF2196F3), "invoices"),
    QuickStat("مشتریان", "156", Icons.Default.Person, Color(0xFF9C27B0), "customers"),
    QuickStat("جزئیات", "∞", Icons.Default.Info, Color(0xFF607D8B), "details")
)

@Suppress("DEPRECATION")
fun getCashierStats(): List<QuickStat> = listOf(
    QuickStat("پیام‌ها", "3", Icons.Default.Notifications, Color(0xFF4CAF50), "messages"),
    QuickStat("فاکتورهای من", "7", Icons.Default.List, Color(0xFF2196F3), "invoices"),
    QuickStat("مشتریان من", "34", Icons.Default.Person, Color(0xFF9C27B0), "customers")
)

@Suppress("DEPRECATION")
fun getCustomerStats(): List<QuickStat> = listOf(
    QuickStat("پیام‌ها", "2", Icons.Default.Notifications, Color(0xFF4CAF50), "messages"),
    QuickStat("فاکتورها", "12", Icons.Default.List, Color(0xFF2196F3), "invoices")
)

@Composable
fun MainMenuGrid(
    userRole: UserRole,
    onNavigateToScreen: (String) -> Unit
) {
    val menuItems = getMenuItemsForRole(userRole)
    val gridColumns = getGridColumns()
    val adaptiveSpacing = getAdaptiveSpacing()
    val isLandscapeMode = isLandscape()

    Column {
        Text(
            text = "منوی اصلی",
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = adaptiveSpacing)
        )

        if (isLandscapeMode) {
            // در حالت افقی از LazyVerticalGrid استفاده کن
            LazyVerticalGrid(
                columns = GridCells.Fixed(gridColumns),
                horizontalArrangement = Arrangement.spacedBy(adaptiveSpacing),
                verticalArrangement = Arrangement.spacedBy(adaptiveSpacing),
                modifier = Modifier.height(getAdaptiveCardHeight() * ((menuItems.size + gridColumns - 1) / gridColumns))
            ) {
                items(menuItems) { menuItem ->
                    MenuItemCard(
                        menuItem = menuItem,
                        onClick = { onNavigateToScreen(menuItem.route) }
                    )
                }
            }
        } else {
            // در حالت عمودی از Row های دستی استفاده کن
            for (i in menuItems.indices step 2) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(adaptiveSpacing)
                ) {
                    MenuItemCard(
                        menuItem = menuItems[i],
                        onClick = { onNavigateToScreen(menuItems[i].route) },
                        modifier = Modifier.weight(1f)
                    )

                    if (i + 1 < menuItems.size) {
                        MenuItemCard(
                            menuItem = menuItems[i + 1],
                            onClick = { onNavigateToScreen(menuItems[i + 1].route) },
                            modifier = Modifier.weight(1f)
                        )
                    } else {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }

                if (i + 2 < menuItems.size) {
                    Spacer(modifier = Modifier.height(adaptiveSpacing))
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MenuItemCard(
    menuItem: MenuItem,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val cardHeight = getAdaptiveCardHeight()
    val isLandscapeMode = isLandscape()

    Card(
        onClick = onClick,
        modifier = modifier.height(cardHeight),
        colors = CardDefaults.cardColors(
            containerColor = if (menuItem.color != Color.Unspecified) {
                menuItem.color.copy(alpha = 0.1f)
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(if (isLandscapeMode) 12.dp else 16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(if (isLandscapeMode) 8.dp else 12.dp)
        ) {
            Icon(
                imageVector = menuItem.icon,
                contentDescription = menuItem.title,
                tint = if (menuItem.color != Color.Unspecified) {
                    menuItem.color
                } else {
                    MaterialTheme.colorScheme.primary
                },
                modifier = Modifier.size(if (isLandscapeMode) 24.dp else 28.dp)
            )

            Text(
                text = menuItem.title,
                style = if (isLandscapeMode) MaterialTheme.typography.bodyMedium else MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface,
                maxLines = if (isLandscapeMode) 1 else 2
            )
        }
    }
}

// تعریف آیتم‌های منو برای هر نوع کاربر
@Suppress("DEPRECATION")
fun getMenuItemsForRole(userRole: UserRole): List<MenuItem> {
    return when (userRole) {
        UserRole.SUPER_ADMIN -> listOf(
            MenuItem("مدیریت محصولات", Icons.Default.Build, "products", color = Color(0xFF4CAF50)),
            MenuItem("مدیریت مشتریان", Icons.Default.Person, "customers", color = Color(0xFF2196F3)),
            MenuItem("مدیریت فاکتورها", Icons.Default.List, "invoices", color = Color(0xFFFF9800)),
            MenuItem("آمار و گزارشات", Icons.Default.Info, "statistics", color = Color(0xFF9C27B0)),
            MenuItem("مدیریت کاربران", Icons.Default.Settings, "users", color = Color(0xFFF44336)),
            MenuItem("مدیریت پرداخت‌ها", Icons.Default.Star, "payments", color = Color(0xFF00BCD4)),
            MenuItem("حسابداری", Icons.Default.AccountBox, "accounting", color = Color(0xFF795548)),
            MenuItem("تصویه حساب", Icons.Default.Check, "settlement", color = Color(0xFF607D8B))
        )

        UserRole.CASHIER -> listOf(
            MenuItem("مدیریت محصولات", Icons.Default.Build, "products", color = Color(0xFF4CAF50)),
            MenuItem("مدیریت مشتریان", Icons.Default.Person, "customers", color = Color(0xFF2196F3)),
            MenuItem("صدور فاکتور", Icons.Default.List, "invoices", color = Color(0xFFFF9800)),
            MenuItem("آمار فروش", Icons.Default.Info, "statistics", color = Color(0xFF9C27B0)),
            MenuItem("دریافت پرداخت", Icons.Default.Star, "payments", color = Color(0xFF00BCD4))
        )

        UserRole.CUSTOMER -> listOf(
            MenuItem("مشاهده محصولات", Icons.Default.Build, "products", color = Color(0xFF4CAF50)),
            MenuItem("فاکتورهای من", Icons.Default.List, "invoices", color = Color(0xFFFF9800)),
            MenuItem("پرداخت‌های من", Icons.Default.Star, "payments", color = Color(0xFF00BCD4))
        )
    }
}

@Suppress("DEPRECATION")
@Composable
fun RecentActivities(userRole: UserRole) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "فعالیت‌های اخیر",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            // نمایش فعالیت‌های اخیر بر اساس نوع کاربر
            when (userRole) {
                UserRole.SUPER_ADMIN -> {
                    RecentActivityItem("فاکتور جدید توسط احمد محمدی", "5 دقیقه پیش", Icons.Default.List)
                    RecentActivityItem("مشتری جدید اضافه شد", "15 دقیقه پیش", Icons.Default.Add)
                    RecentActivityItem("پرداخت تایید شد", "30 دقیقه پیش", Icons.Default.Check)
                }
                UserRole.CASHIER -> {
                    RecentActivityItem("فاکتور #1234 صادر شد", "10 دقیقه پیش", Icons.Default.List)
                    RecentActivityItem("پرداخت دریافت شد", "25 دقیقه پیش", Icons.Default.Star)
                }
                UserRole.CUSTOMER -> {
                    RecentActivityItem("خرید جدید انجام شد", "2 ساعت پیش", Icons.Default.ShoppingCart)
                    RecentActivityItem("پرداخت تایید شد", "1 روز پیش", Icons.Default.Check)
                }
            }
        }
    }
}

@Composable
fun RecentActivityItem(
    title: String,
    time: String,
    icon: ImageVector
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(20.dp)
        )

        Spacer(modifier = Modifier.width(12.dp))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = time,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        }
    }
}

@Composable
fun BottomNavigationBar(
    navController: NavHostController,
    userRole: UserRole
) {
    val bottomNavItems = getBottomNavItemsForRole(userRole)

    NavigationBar(
        containerColor = MaterialTheme.colorScheme.surfaceContainer
    ) {
        bottomNavItems.forEach { item ->
            NavigationBarItem(
                icon = {
                    Icon(
                        imageVector = item.icon,
                        contentDescription = item.title
                    )
                },
                label = {
                    Text(
                        text = item.title,
                        style = MaterialTheme.typography.labelSmall
                    )
                },
                selected = navController.currentDestination?.route == item.route,
                onClick = {
                    navController.navigate(item.route) {
                        // Clear back stack to avoid building up a large stack
                        popUpTo(navController.graph.startDestinationId) {
                            saveState = true
                        }
                        launchSingleTop = true
                        restoreState = true
                    }
                }
            )
        }
    }
}

@Suppress("DEPRECATION")
fun getBottomNavItemsForRole(userRole: UserRole): List<MenuItem> {
    return when (userRole) {
        UserRole.SUPER_ADMIN -> listOf(
            MenuItem("داشبورد", Icons.Default.Home, "dashboard"),
            MenuItem("فاکتورها", Icons.Default.List, "invoices"),
            MenuItem("آمار", Icons.Default.Info, "statistics"),
            MenuItem("کاربران", Icons.Default.Person, "users")
        )

        UserRole.CASHIER -> listOf(
            MenuItem("داشبورد", Icons.Default.Home, "dashboard"),
            MenuItem("فاکتورها", Icons.Default.List, "invoices"),
            MenuItem("مشتریان", Icons.Default.Person, "customers"),
            MenuItem("آمار", Icons.Default.Info, "statistics")
        )

        UserRole.CUSTOMER -> listOf(
            MenuItem("داشبورد", Icons.Default.Home, "dashboard"),
            MenuItem("خریدها", Icons.Default.ShoppingCart, "invoices"),
            MenuItem("پرداخت‌ها", Icons.Default.Star, "payments")
        )
    }
}

// Placeholder screens - بعداً جایگزین می‌شوند
@Composable
fun CustomerManagementScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "صفحه مدیریت مشتریان\n(در حال توسعه)",
            style = MaterialTheme.typography.headlineMedium,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun MainStatisticsScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "صفحه آمار و گزارشات\n(در حال توسعه)",
            style = MaterialTheme.typography.headlineMedium,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun MainUserManagementScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "صفحه مدیریت کاربران\n(در حال توسعه)",
            style = MaterialTheme.typography.headlineMedium,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun PaymentScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "صفحه مدیریت پرداخت‌ها\n(در حال توسعه)",
            style = MaterialTheme.typography.headlineMedium,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun AccountingScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "صفحه حسابداری\n(در حال توسعه)",
            style = MaterialTheme.typography.headlineMedium,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun SettlementScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "صفحه تصویه حساب\n(در حال توسعه)",
            style = MaterialTheme.typography.headlineMedium,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun UserProfileDialog(
    currentUserRole: UserRole,
    onDismiss: () -> Unit,
    onLogout: () -> Unit
) {
    var showLogoutConfirmation by remember { mutableStateOf(false) }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // آیکون پروفایل
                Icon(
                    imageVector = Icons.Default.AccountCircle,
                    contentDescription = "پروفایل کاربری",
                    modifier = Modifier.size(64.dp),
                    tint = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.height(16.dp))

                // عنوان
                Text(
                    text = "پروفایل کاربری",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )

                Spacer(modifier = Modifier.height(24.dp))

                // نقش کاربر
                ProfileInfoRow(
                    label = "نقش کاربر:",
                    value = when (currentUserRole) {
                        UserRole.SUPER_ADMIN -> "مدیر کل"
                        UserRole.CASHIER -> "فروشنده"
                        UserRole.CUSTOMER -> "مشتری"
                    },
                    icon = Icons.Default.Person
                )

                Spacer(modifier = Modifier.height(12.dp))

                // نام و نام خانوادگی (نمونه)
                ProfileInfoRow(
                    label = "نام و نام خانوادگی:",
                    value = getUserDisplayName(currentUserRole),
                    icon = Icons.Default.Face
                )

                Spacer(modifier = Modifier.height(12.dp))

                // شماره تماس (نمونه)
                ProfileInfoRow(
                    label = "شماره تماس:",
                    value = getUserPhone(currentUserRole),
                    icon = Icons.Default.Phone
                )

                Spacer(modifier = Modifier.height(12.dp))

                // ایمیل (نمونه)
                ProfileInfoRow(
                    label = "ایمیل:",
                    value = getUserEmail(currentUserRole),
                    icon = Icons.Default.Email
                )

                Spacer(modifier = Modifier.height(24.dp))

                // دکمه‌های عملیات
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // دکمه بستن
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("بستن")
                    }

                    // دکمه خروج
                    Button(
                        onClick = { showLogoutConfirmation = true },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Text("خروج از حساب")
                    }
                }
            }
        }
    }

    // دیالوگ تایید خروج
    if (showLogoutConfirmation) {
        LogoutConfirmationDialog(
            onConfirm = {
                showLogoutConfirmation = false
                onLogout()
            },
            onDismiss = { showLogoutConfirmation = false }
        )
    }
}

@Composable
fun ProfileInfoRow(
    label: String,
    value: String,
    icon: ImageVector
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            modifier = Modifier.size(20.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.width(12.dp))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
            Text(
                text = value,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

@Composable
fun LogoutConfirmationDialog(
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "خروج از حساب کاربری",
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Text("آیا مطمئن هستید که می‌خواهید از حساب کاربری خود خارج شوید؟")
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text("بله، خروج")
            }
        },
        dismissButton = {
            OutlinedButton(onClick = onDismiss) {
                Text("خیر")
            }
        }
    )
}

// Helper functions برای اطلاعات کاربر (نمونه)
fun getUserDisplayName(userRole: UserRole): String {
    return when (userRole) {
        UserRole.SUPER_ADMIN -> "علی احمدی"
        UserRole.CASHIER -> "فاطمه محمدی"
        UserRole.CUSTOMER -> "حسن رضایی"
    }
}

fun getUserPhone(userRole: UserRole): String {
    return when (userRole) {
        UserRole.SUPER_ADMIN -> "09123456789"
        UserRole.CASHIER -> "09187654321"
        UserRole.CUSTOMER -> "09191234567"
    }
}

fun getUserEmail(userRole: UserRole): String {
    return when (userRole) {
        UserRole.SUPER_ADMIN -> "<EMAIL>"
        UserRole.CASHIER -> "<EMAIL>"
        UserRole.CUSTOMER -> "<EMAIL>"
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MessagesScreen(userRole: UserRole) {
    var selectedTab by remember { mutableStateOf(0) }

    val tabs = when (userRole) {
        UserRole.SUPER_ADMIN -> listOf("همه پیام‌ها", "درخواست‌های تایید", "پیام‌های سیستم")
        UserRole.CASHIER -> listOf("پیام‌های من", "درخواست‌های تایید", "اعلان‌ها")
        UserRole.CUSTOMER -> listOf("پیام‌های من", "وضعیت تایید", "اعلان‌ها")
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("پیام‌ها و اعلان‌ها") }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            // Tab Row
            TabRow(selectedTabIndex = selectedTab) {
                tabs.forEachIndexed { index, title ->
                    Tab(
                        selected = selectedTab == index,
                        onClick = { selectedTab = index },
                        text = { Text(title) }
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Content based on selected tab
            when (userRole) {
                UserRole.SUPER_ADMIN -> {
                    when (selectedTab) {
                        0 -> AllMessagesTab()
                        1 -> ApprovalRequestsTab()
                        2 -> SystemMessagesTab()
                    }
                }
                UserRole.CASHIER -> {
                    when (selectedTab) {
                        0 -> CashierMessagesTab()
                        1 -> CashierApprovalRequestsTab()
                        2 -> NotificationsTab()
                    }
                }
                UserRole.CUSTOMER -> {
                    when (selectedTab) {
                        0 -> CustomerMessagesTab()
                        1 -> CustomerApprovalStatusTab()
                        2 -> CustomerNotificationsTab()
                    }
                }
            }
        }
    }
}

@Composable
fun AllMessagesTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(5) { index ->
            MessageCard(
                title = "درخواست تایید مشتری جدید",
                message = "مشتری جدید با نام احمد محمدی درخواست تایید کرده است",
                time = "${index + 1} ساعت پیش",
                isRead = index > 2,
                type = "approval"
            )
        }
    }
}

@Composable
fun ApprovalRequestsTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(3) { index ->
            ApprovalRequestCard(
                customerName = "مشتری ${index + 1}",
                referrerName = "فروشنده علی احمدی",
                requestTime = "${index + 1} ساعت پیش",
                onApprove = {
                    // تایید درخواست مشتری
                    android.util.Log.d("ApprovalRequest", "Customer approved: مشتری ${index + 1}")
                },
                onReject = {
                    // رد درخواست مشتری
                    android.util.Log.d("ApprovalRequest", "Customer rejected: مشتری ${index + 1}")
                }
            )
        }
    }
}

@Composable
fun SystemMessagesTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(4) { index ->
            MessageCard(
                title = "پیام سیستم",
                message = "سیستم با موفقیت به‌روزرسانی شد",
                time = "${index + 1} روز پیش",
                isRead = true,
                type = "system"
            )
        }
    }
}

@Composable
fun CashierMessagesTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(3) { index ->
            MessageCard(
                title = "پیام از مدیریت",
                message = "فاکتور شماره ${1000 + index} تایید شد",
                time = "${index + 1} ساعت پیش",
                isRead = index > 1,
                type = "management"
            )
        }
    }
}

@Composable
fun CashierApprovalRequestsTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(2) { index ->
            ApprovalRequestCard(
                customerName = "مشتری جدید ${index + 1}",
                referrerName = "شما",
                requestTime = "${index + 1} ساعت پیش",
                onApprove = {
                    // تایید درخواست مشتری جدید
                    android.util.Log.d("CashierApproval", "New customer approved: مشتری جدید ${index + 1}")
                },
                onReject = {
                    // رد درخواست مشتری جدید
                    android.util.Log.d("CashierApproval", "New customer rejected: مشتری جدید ${index + 1}")
                }
            )
        }
    }
}

@Composable
fun NotificationsTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(4) { index ->
            MessageCard(
                title = "اعلان",
                message = "یادآوری: پرداخت فاکتور ${1000 + index}",
                time = "${index + 1} ساعت پیش",
                isRead = index > 2,
                type = "notification"
            )
        }
    }
}

@Composable
fun CustomerMessagesTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(2) { index ->
            MessageCard(
                title = "پیام از فروشنده",
                message = "سفارش شما آماده تحویل است",
                time = "${index + 1} ساعت پیش",
                isRead = index > 0,
                type = "seller"
            )
        }
    }
}

@Composable
fun CustomerApprovalStatusTab() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.CheckCircle,
                contentDescription = "تایید شده",
                tint = Color(0xFF4CAF50),
                modifier = Modifier.size(48.dp)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "حساب شما تایید شده است",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Text(
                text = "می‌توانید از تمام امکانات استفاده کنید",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
fun CustomerNotificationsTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(3) { index ->
            MessageCard(
                title = "اعلان",
                message = "فاکتور جدید برای شما صادر شد",
                time = "${index + 1} ساعت پیش",
                isRead = index > 1,
                type = "notification"
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MessageCard(
    title: String,
    message: String,
    time: String,
    isRead: Boolean,
    type: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isRead)
                MaterialTheme.colorScheme.surface
            else
                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.Top
        ) {
            Icon(
                imageVector = when (type) {
                    "approval" -> Icons.Default.Person
                    "system" -> Icons.Default.Settings
                    "management" -> Icons.Default.Build
                    "seller" -> Icons.Default.ShoppingCart
                    "notification" -> Icons.Default.Notifications
                    else -> Icons.Default.Email
                },
                contentDescription = type,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column(modifier = Modifier.weight(1f)) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.Top
                ) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = if (isRead) FontWeight.Normal else FontWeight.Bold,
                        modifier = Modifier.weight(1f)
                    )

                    Text(
                        text = time,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
                )
            }

            if (!isRead) {
                Spacer(modifier = Modifier.width(8.dp))
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(
                            MaterialTheme.colorScheme.primary,
                            shape = RoundedCornerShape(4.dp)
                        )
                )
            }
        }
    }
}

@Composable
fun ApprovalRequestCard(
    customerName: String,
    referrerName: String,
    requestTime: String,
    onApprove: () -> Unit,
    onReject: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "درخواست تایید مشتری",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = "نام مشتری: $customerName",
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Text(
                        text = "معرف: $referrerName",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                Text(
                    text = requestTime,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = onReject,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("رد")
                }

                Button(
                    onClick = onApprove,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF4CAF50)
                    )
                ) {
                    Text("تایید")
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SellersManagementScreen() {
    var selectedSeller by remember { mutableStateOf<String?>(null) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("مدیریت فروشندگان") },
                navigationIcon = {
                    if (selectedSeller != null) {
                        IconButton(onClick = { selectedSeller = null }) {
                            Icon(Icons.Default.ArrowBack, contentDescription = "بازگشت")
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            if (selectedSeller == null) {
                // لیست فروشندگان
                Text(
                    text = "لیست فروشندگان",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(8) { index ->
                        SellerCard(
                            sellerName = "فروشنده ${index + 1}",
                            sellerEmail = "seller${index + 1}@sharenshop.com",
                            customerCount = (10..50).random(),
                            totalSales = "${(1000..5000).random()},000 ﷼",
                            onClick = { selectedSeller = "seller_${index + 1}" }
                        )
                    }
                }
            } else {
                // جزئیات فروشنده و مشتریان
                SellerDetailsScreen(sellerId = selectedSeller!!)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SellerCard(
    sellerName: String,
    sellerEmail: String,
    customerCount: Int,
    totalSales: String,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Person,
                contentDescription = "فروشنده",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(40.dp)
            )

            Spacer(modifier = Modifier.width(16.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = sellerName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Text(
                    text = sellerEmail,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )

                Spacer(modifier = Modifier.height(4.dp))

                Row {
                    Text(
                        text = "$customerCount مشتری",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.width(16.dp))

                    Text(
                        text = "فروش: $totalSales",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFF4CAF50)
                    )
                }
            }

            Icon(
                imageVector = Icons.Default.KeyboardArrowRight,
                contentDescription = "مشاهده جزئیات",
                tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
            )
        }
    }
}

@Composable
fun SellerDetailsScreen(sellerId: String) {
    var selectedTab by remember { mutableStateOf(0) }
    val tabs = listOf("مشتریان", "آمار فروش", "فاکتورها")

    Column {
        // اطلاعات فروشنده
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "فروشنده شماره ${sellerId.split("_")[1]}",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold
                )

                Text(
                    text = "seller${sellerId.split("_")[1]}@sharenshop.com",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Tab Row
        TabRow(selectedTabIndex = selectedTab) {
            tabs.forEachIndexed { index, title ->
                Tab(
                    selected = selectedTab == index,
                    onClick = { selectedTab = index },
                    text = { Text(title) }
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // محتوای تب‌ها
        when (selectedTab) {
            0 -> SellerCustomersTab(sellerId)
            1 -> SellerStatsTab(sellerId)
            2 -> SellerInvoicesTab(sellerId)
        }
    }
}

@Composable
fun SellerCustomersTab(sellerId: String) {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(15) { index ->
            CustomerSummaryCard(
                customerName = "مشتری ${index + 1}",
                totalPurchases = "${(500..2000).random()},000 ﷼",
                totalPayments = "${(300..1800).random()},000 ﷼",
                balance = "${(0..500).random()},000 ﷼",
                onClick = { /* TODO: Navigate to customer details */ }
            )
        }
    }
}

@Composable
fun SellerStatsTab(sellerId: String) {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // آمار کلی
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                StatCard(
                    title = "کل فروش",
                    value = "15,500,000 ﷼",
                    icon = Icons.Default.KeyboardArrowUp,
                    color = Color(0xFF4CAF50),
                    modifier = Modifier.weight(1f)
                )

                StatCard(
                    title = "تعداد فاکتور",
                    value = "45",
                    icon = Icons.Default.List,
                    color = Color(0xFF2196F3),
                    modifier = Modifier.weight(1f)
                )
            }
        }

        item {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                StatCard(
                    title = "مشتریان فعال",
                    value = "23",
                    icon = Icons.Default.Person,
                    color = Color(0xFF9C27B0),
                    modifier = Modifier.weight(1f)
                )

                StatCard(
                    title = "میانگین فروش",
                    value = "344,000 ﷼",
                    icon = Icons.Default.Info,
                    color = Color(0xFFFF9800),
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
fun SellerInvoicesTab(sellerId: String) {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(15) { index ->
            InvoiceSummaryCard(
                invoiceNumber = "INV-${1000 + index}",
                customerName = "مشتری ${(index % 5) + 1}",
                amount = "${(200..800).random()},000 ﷼",
                date = "${(1..30).random()} روز پیش",
                status = listOf("پرداخت شده", "در انتظار", "معوقه").random()
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustomerSummaryCard(
    customerName: String,
    totalPurchases: String,
    totalPayments: String,
    balance: String,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = customerName,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "کل خرید:",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = totalPurchases,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                }

                Column {
                    Text(
                        text = "کل پرداخت:",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = totalPayments,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF4CAF50)
                    )
                }

                Column {
                    Text(
                        text = "مانده:",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = balance,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = if (balance.startsWith("0")) Color(0xFF4CAF50) else Color(0xFFFF9800)
                    )
                }
            }
        }
    }
}

@Composable
fun StatCard(
    title: String,
    value: String,
    icon: ImageVector,
    color: Color,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = color,
                modifier = Modifier.size(32.dp)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = value,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = color
            )

            Text(
                text = title,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun InvoiceSummaryCard(
    invoiceNumber: String,
    customerName: String,
    amount: String,
    date: String,
    status: String
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = invoiceNumber,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )

                Text(
                    text = customerName,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )

                Text(
                    text = date,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )
            }

            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = amount,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )

                Text(
                    text = status,
                    style = MaterialTheme.typography.bodySmall,
                    color = when (status) {
                        "پرداخت شده" -> Color(0xFF4CAF50)
                        "در انتظار" -> Color(0xFFFF9800)
                        "معوقه" -> Color(0xFFF44336)
                        else -> MaterialTheme.colorScheme.onSurface
                    },
                    modifier = Modifier
                        .background(
                            when (status) {
                                "پرداخت شده" -> Color(0xFF4CAF50).copy(alpha = 0.1f)
                                "در انتظار" -> Color(0xFFFF9800).copy(alpha = 0.1f)
                                "معوقه" -> Color(0xFFF44336).copy(alpha = 0.1f)
                                else -> Color.Transparent
                            },
                            RoundedCornerShape(4.dp)
                        )
                        .padding(horizontal = 8.dp, vertical = 2.dp)
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DetailsScreen() {
    var selectedTab by remember { mutableStateOf(0) }
    val tabs = listOf("ریز فاکتورها", "ریز پرداخت‌ها", "گزارش مالی", "آمار تفصیلی")

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("جزئیات و گزارشات تفصیلی") }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            // Tab Row
            TabRow(selectedTabIndex = selectedTab) {
                tabs.forEachIndexed { index, title ->
                    Tab(
                        selected = selectedTab == index,
                        onClick = { selectedTab = index },
                        text = { Text(title, style = MaterialTheme.typography.bodySmall) }
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // محتوای تب‌ها
            when (selectedTab) {
                0 -> DetailedInvoicesTab()
                1 -> DetailedPaymentsTab()
                2 -> FinancialReportTab()
                3 -> DetailedStatsTab()
            }
        }
    }
}

@Composable
fun DetailedInvoicesTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item {
            // فیلتر و جستجو
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "فیلتر فاکتورها",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        FilterChip(
                            selected = true,
                            onClick = { },
                            label = { Text("همه") },
                            modifier = Modifier.weight(1f)
                        )
                        FilterChip(
                            selected = false,
                            onClick = { },
                            label = { Text("نقدی") },
                            modifier = Modifier.weight(1f)
                        )
                        FilterChip(
                            selected = false,
                            onClick = { },
                            label = { Text("نسیه") },
                            modifier = Modifier.weight(1f)
                        )
                    }
                }
            }
        }

        items(50) { index ->
            DetailedInvoiceCard(
                invoiceNumber = "INV-${2000 + index}",
                sellerName = "فروشنده ${(index % 8) + 1}",
                customerName = "مشتری ${(index % 20) + 1}",
                items = listOf("کالا A", "کالا B", "کالا C").take((1..3).random()),
                totalAmount = "${(500..2000).random()},000 ﷼",
                paidAmount = "${(200..1800).random()},000 ﷼",
                remainingAmount = "${(0..500).random()},000 ﷼",
                date = "${(1..30).random()} روز پیش",
                paymentType = if (index % 3 == 0) "نقدی" else "نسیه"
            )
        }
    }
}

@Composable
fun DetailedPaymentsTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(40) { index ->
            DetailedPaymentCard(
                paymentId = "PAY-${3000 + index}",
                invoiceNumber = "INV-${2000 + (index % 50)}",
                customerName = "مشتری ${(index % 20) + 1}",
                sellerName = "فروشنده ${(index % 8) + 1}",
                amount = "${(100..800).random()},000 ﷼",
                paymentMethod = listOf("نقد", "کارت به کارت", "چک").random(),
                date = "${(1..60).random()} روز پیش",
                status = listOf("تایید شده", "در انتظار", "رد شده").random()
            )
        }
    }
}

@Composable
fun FinancialReportTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // خلاصه مالی
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "خلاصه مالی کل سیستم",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        FinancialSummaryItem("کل فروش", "125,500,000 ﷼", Color(0xFF4CAF50))
                        FinancialSummaryItem("کل دریافتی", "98,200,000 ﷼", Color(0xFF2196F3))
                    }

                    Spacer(modifier = Modifier.height(12.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        FinancialSummaryItem("مانده طلب", "27,300,000 ﷼", Color(0xFFFF9800))
                        FinancialSummaryItem("سود خالص", "45,600,000 ﷼", Color(0xFF9C27B0))
                    }
                }
            }
        }

        item {
            // گزارش فروشندگان
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "عملکرد فروشندگان",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    repeat(8) { index ->
                        SellerPerformanceRow(
                            sellerName = "فروشنده ${index + 1}",
                            sales = "${(5000..15000).random()},000 ﷼",
                            customers = (10..50).random(),
                            performance = (60..95).random()
                        )

                        if (index < 7) {
                            Spacer(modifier = Modifier.height(8.dp))
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun DetailedStatsTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // آمار زمانی
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "آمار زمانی",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    TimeStatsRow("امروز", "2,500,000 ﷼", "12 فاکتور")
                    TimeStatsRow("این هفته", "15,200,000 ﷼", "78 فاکتور")
                    TimeStatsRow("این ماه", "45,800,000 ﷼", "234 فاکتور")
                    TimeStatsRow("سال جاری", "125,500,000 ﷼", "1,456 فاکتور")
                }
            }
        }

        item {
            // آمار محصولات
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "پرفروش‌ترین محصولات",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    repeat(10) { index ->
                        ProductStatsRow(
                            productName = "محصول ${index + 1}",
                            soldCount = (50..200).random(),
                            revenue = "${(1000..5000).random()},000 ﷼"
                        )

                        if (index < 9) {
                            Spacer(modifier = Modifier.height(8.dp))
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun DetailedInvoiceCard(
    invoiceNumber: String,
    sellerName: String,
    customerName: String,
    items: List<String>,
    totalAmount: String,
    paidAmount: String,
    remainingAmount: String,
    date: String,
    paymentType: String
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = invoiceNumber,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Text(
                        text = "فروشنده: $sellerName",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )

                    Text(
                        text = "مشتری: $customerName",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }

                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    Text(
                        text = date,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )

                    Text(
                        text = paymentType,
                        style = MaterialTheme.typography.bodySmall,
                        color = if (paymentType == "نقدی") Color(0xFF4CAF50) else Color(0xFFFF9800),
                        modifier = Modifier
                            .background(
                                if (paymentType == "نقدی") Color(0xFF4CAF50).copy(alpha = 0.1f) else Color(0xFFFF9800).copy(alpha = 0.1f),
                                RoundedCornerShape(4.dp)
                            )
                            .padding(horizontal = 6.dp, vertical = 2.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // آیتم‌های فاکتور
            Text(
                text = "کالاها: ${items.joinToString(", ")}",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
            )

            Spacer(modifier = Modifier.height(8.dp))

            // اطلاعات مالی
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "کل مبلغ:",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = totalAmount,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                }

                Column {
                    Text(
                        text = "پرداخت شده:",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = paidAmount,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF4CAF50)
                    )
                }

                Column {
                    Text(
                        text = "مانده:",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = remainingAmount,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = if (remainingAmount.startsWith("0")) Color(0xFF4CAF50) else Color(0xFFFF9800)
                    )
                }
            }
        }
    }
}

@Composable
fun DetailedPaymentCard(
    paymentId: String,
    invoiceNumber: String,
    customerName: String,
    sellerName: String,
    amount: String,
    paymentMethod: String,
    date: String,
    status: String
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = paymentId,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )

                Text(
                    text = "فاکتور: $invoiceNumber",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )

                Text(
                    text = "$customerName → $sellerName",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )

                Text(
                    text = "$paymentMethod • $date",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )
            }

            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = amount,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )

                Text(
                    text = status,
                    style = MaterialTheme.typography.bodySmall,
                    color = when (status) {
                        "تایید شده" -> Color(0xFF4CAF50)
                        "در انتظار" -> Color(0xFFFF9800)
                        "رد شده" -> Color(0xFFF44336)
                        else -> MaterialTheme.colorScheme.onSurface
                    },
                    modifier = Modifier
                        .background(
                            when (status) {
                                "تایید شده" -> Color(0xFF4CAF50).copy(alpha = 0.1f)
                                "در انتظار" -> Color(0xFFFF9800).copy(alpha = 0.1f)
                                "رد شده" -> Color(0xFFF44336).copy(alpha = 0.1f)
                                else -> Color.Transparent
                            },
                            RoundedCornerShape(4.dp)
                        )
                        .padding(horizontal = 8.dp, vertical = 2.dp)
                )
            }
        }
    }
}

@Composable
fun FinancialSummaryItem(
    title: String,
    amount: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = amount,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = color
        )

        Text(
            text = title,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
        )
    }
}

@Composable
fun SellerPerformanceRow(
    sellerName: String,
    sales: String,
    customers: Int,
    performance: Int
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = sellerName,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.weight(1f)
        )

        Text(
            text = sales,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF4CAF50),
            modifier = Modifier.weight(1f),
            textAlign = TextAlign.Center
        )

        Text(
            text = "$customers مشتری",
            style = MaterialTheme.typography.bodySmall,
            modifier = Modifier.weight(1f),
            textAlign = TextAlign.Center
        )

        Text(
            text = "$performance%",
            style = MaterialTheme.typography.bodySmall,
            color = if (performance > 80) Color(0xFF4CAF50) else if (performance > 60) Color(0xFFFF9800) else Color(0xFFF44336),
            modifier = Modifier.weight(0.5f),
            textAlign = TextAlign.End
        )
    }
}

@Composable
fun TimeStatsRow(
    period: String,
    sales: String,
    invoices: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = period,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )

        Column(
            horizontalAlignment = Alignment.End
        ) {
            Text(
                text = sales,
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF4CAF50)
            )

            Text(
                text = invoices,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
fun ProductStatsRow(
    productName: String,
    soldCount: Int,
    revenue: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = productName,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.weight(1f)
        )

        Text(
            text = "$soldCount عدد",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            modifier = Modifier.weight(1f),
            textAlign = TextAlign.Center
        )

        Text(
            text = revenue,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF4CAF50),
            textAlign = TextAlign.End
        )
    }
}