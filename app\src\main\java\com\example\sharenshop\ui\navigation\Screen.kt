package com.example.sharenshop.ui.navigation

sealed class Screen(val route: String) {
    object Auth : Screen("auth_screen")
    object Main : Screen("main_screen")
    object Home : Screen("home_screen")
    object Invoice : Screen("invoice_screen")
    object Product : Screen("product_screen")
    object Seller : Screen("seller_screen")
    object Statistics : Screen("statistics_screen")
    object UserManagement : Screen("user_management_screen")
    object Payment : Screen("payment_screen")
    object Settlement : Screen("settlement_screen")
    object Accounting : Screen("accounting_screen")
    object PendingApproval : Screen("pending_approval_screen")
    // Add more screens as needed
} 