// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.domain.repository.SecurityRepository;
import com.example.sharenshop.domain.use_case.SecurityUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideSecurityUseCasesFactory implements Factory<SecurityUseCases> {
  private final Provider<SecurityRepository> repositoryProvider;

  public UseCaseModule_ProvideSecurityUseCasesFactory(
      Provider<SecurityRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public SecurityUseCases get() {
    return provideSecurityUseCases(repositoryProvider.get());
  }

  public static UseCaseModule_ProvideSecurityUseCasesFactory create(
      Provider<SecurityRepository> repositoryProvider) {
    return new UseCaseModule_ProvideSecurityUseCasesFactory(repositoryProvider);
  }

  public static SecurityUseCases provideSecurityUseCases(SecurityRepository repository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideSecurityUseCases(repository));
  }
}
