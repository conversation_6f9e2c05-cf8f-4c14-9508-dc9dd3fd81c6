package com.example.sharenshop.data.model;

/**
 * گزارش مالی
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\t\n\u0002\b\u001a\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B=\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\u0006\u0010\b\u001a\u00020\u0003\u0012\u0006\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010 \u001a\u00020\u0003H\u00c6\u0003J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\nH\u00c6\u0003JO\u0010#\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\nH\u00c6\u0001J\u0013\u0010$\u001a\u00020%2\b\u0010&\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\'\u001a\u00020(H\u00d6\u0001J\t\u0010)\u001a\u00020*H\u00d6\u0001R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001c\u0010\u0004\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u000e\u0010\u000f\u001a\u0004\b\u0010\u0010\u0011R\u001c\u0010\b\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0012\u0010\u000f\u001a\u0004\b\u0013\u0010\u0011R\u001c\u0010\u0005\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0014\u0010\u000f\u001a\u0004\b\u0015\u0010\u0011R\u001c\u0010\u0006\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0016\u0010\u000f\u001a\u0004\b\u0017\u0010\u0011R\u001c\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0018\u0010\u000f\u001a\u0004\b\u0019\u0010\u0011R\u001c\u0010\u0007\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001a\u0010\u000f\u001a\u0004\b\u001b\u0010\u0011\u00a8\u0006+"}, d2 = {"Lcom/example/sharenshop/data/model/FinancialReport;", "", "totalSales", "Ljava/math/BigDecimal;", "totalCashReceived", "totalCreditReceived", "totalOutstandingDebt", "totalSettlementPending", "totalCommissionOwed", "reportDate", "", "(Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;J)V", "getReportDate", "()J", "getTotalCashReceived$annotations", "()V", "getTotalCashReceived", "()Ljava/math/BigDecimal;", "getTotalCommissionOwed$annotations", "getTotalCommissionOwed", "getTotalCreditReceived$annotations", "getTotalCreditReceived", "getTotalOutstandingDebt$annotations", "getTotalOutstandingDebt", "getTotalSales$annotations", "getTotalSales", "getTotalSettlementPending$annotations", "getTotalSettlementPending", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class FinancialReport {
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal totalSales = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal totalCashReceived = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal totalCreditReceived = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal totalOutstandingDebt = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal totalSettlementPending = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal totalCommissionOwed = null;
    private final long reportDate = 0L;
    
    public FinancialReport(@org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalSales, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalCashReceived, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalCreditReceived, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalOutstandingDebt, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalSettlementPending, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalCommissionOwed, long reportDate) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getTotalSales() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getTotalSales$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getTotalCashReceived() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getTotalCashReceived$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getTotalCreditReceived() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getTotalCreditReceived$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getTotalOutstandingDebt() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getTotalOutstandingDebt$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getTotalSettlementPending() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getTotalSettlementPending$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getTotalCommissionOwed() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getTotalCommissionOwed$annotations() {
    }
    
    public final long getReportDate() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component6() {
        return null;
    }
    
    public final long component7() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.model.FinancialReport copy(@org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalSales, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalCashReceived, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalCreditReceived, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalOutstandingDebt, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalSettlementPending, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalCommissionOwed, long reportDate) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}