// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.domain.repository.CustomerRepository;
import com.example.sharenshop.domain.use_case.CustomerUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideCustomerUseCasesFactory implements Factory<CustomerUseCases> {
  private final Provider<CustomerRepository> repositoryProvider;

  public UseCaseModule_ProvideCustomerUseCasesFactory(
      Provider<CustomerRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public CustomerUseCases get() {
    return provideCustomerUseCases(repositoryProvider.get());
  }

  public static UseCaseModule_ProvideCustomerUseCasesFactory create(
      Provider<CustomerRepository> repositoryProvider) {
    return new UseCaseModule_ProvideCustomerUseCasesFactory(repositoryProvider);
  }

  public static CustomerUseCases provideCustomerUseCases(CustomerRepository repository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideCustomerUseCases(repository));
  }
}
