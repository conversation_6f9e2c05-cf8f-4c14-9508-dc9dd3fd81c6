package com.example.sharenshop.ui.screens

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.sharenshop.ui.theme.SharenShopColors
import kotlinx.coroutines.delay
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.PI

@Composable
fun PendingApprovalScreen(
    onRetryLogin: () -> Unit = {},
    onContactSupport: () -> Unit = {}
) {
    // انیمیشن‌های مختلف
    val infiniteTransition = rememberInfiniteTransition(label = "infinite")
    
    val rotationAngle by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )
    
    val pulseScale by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse"
    )
    
    val shimmerAlpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "shimmer"
    )

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        SharenShopColors.FutureDusk,
                        SharenShopColors.DeepBlue.copy(alpha = 0.1f),
                        SharenShopColors.FutureDusk
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            
            // Header Section
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.padding(top = 40.dp)
            ) {
                Text(
                    text = "پوشاک شارن",
                    fontSize = 32.sp,
                    fontWeight = FontWeight.Bold,
                    color = SharenShopColors.DeepBlue,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "تجربه‌ای جدید",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = SharenShopColors.SoftGray,
                    textAlign = TextAlign.Center
                )
            }

            // Main Animation Section
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.Center
            ) {
                
                // Fashion Animation
                Box(
                    modifier = Modifier.size(200.dp),
                    contentAlignment = Alignment.Center
                ) {
                    // Outer rotating ring
                    Canvas(
                        modifier = Modifier
                            .size(180.dp)
                            .rotate(rotationAngle)
                    ) {
                        drawFashionRing(this, SharenShopColors.DeepBlue.copy(alpha = 0.3f))
                    }
                    
                    // Middle pulsing circle
                    Box(
                        modifier = Modifier
                            .size(120.dp)
                            .scale(pulseScale)
                            .clip(CircleShape)
                            .background(
                                Brush.radialGradient(
                                    colors = listOf(
                                        SharenShopColors.DeepBlue.copy(alpha = 0.2f),
                                        SharenShopColors.DeepBlue.copy(alpha = 0.1f),
                                        Color.Transparent
                                    )
                                )
                            )
                    )
                    
                    // Center fashion icon
                    FashionIcon(
                        modifier = Modifier.size(60.dp),
                        alpha = shimmerAlpha
                    )
                }
                
                Spacer(modifier = Modifier.height(40.dp))
                
                // Status Text
                Text(
                    text = "در حال بررسی حساب شما...",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Medium,
                    color = SharenShopColors.DeepBlue,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Animated dots
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    repeat(3) { index ->
                        AnimatedDot(
                            delay = index * 200,
                            color = SharenShopColors.DeepBlue
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(32.dp))
                
                // Encouraging message
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White.copy(alpha = 0.9f)
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
                    shape = RoundedCornerShape(20.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = null,
                            tint = SharenShopColors.DeepBlue,
                            modifier = Modifier.size(32.dp)
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        Text(
                            text = "نگران نباشید",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = SharenShopColors.DeepBlue,
                            textAlign = TextAlign.Center
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "استایل شما در حال ارتقاست",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = SharenShopColors.SoftGray,
                            textAlign = TextAlign.Center
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        Text(
                            text = "فروشنده ما در حال بررسی درخواست شما است و به زودی دسترسی کامل به مجموعه‌های فشن خواهید داشت.",
                            fontSize = 14.sp,
                            color = SharenShopColors.SoftGray,
                            textAlign = TextAlign.Center,
                            lineHeight = 20.sp
                        )
                    }
                }
            }

            // Bottom Actions
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.padding(bottom = 20.dp)
            ) {
                OutlinedButton(
                    onClick = onRetryLogin,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(50.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = SharenShopColors.DeepBlue
                    ),
                    shape = RoundedCornerShape(25.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "تلاش مجدد برای ورود",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                TextButton(
                    onClick = onContactSupport,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = Icons.Default.Phone,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp),
                        tint = SharenShopColors.SoftGray
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "تماس با پشتیبانی",
                        fontSize = 14.sp,
                        color = SharenShopColors.SoftGray
                    )
                }
            }
        }
    }
}

@Composable
fun AnimatedDot(
    delay: Int,
    color: Color
) {
    val infiniteTransition = rememberInfiniteTransition(label = "dot")
    
    val alpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(600, delayMillis = delay, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "dot_alpha"
    )
    
    Box(
        modifier = Modifier
            .size(8.dp)
            .clip(CircleShape)
            .background(color.copy(alpha = alpha))
    )
}

@Composable
fun FashionIcon(
    modifier: Modifier = Modifier,
    alpha: Float = 1f
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        // Fashion icons arrangement
        val icons = listOf(
            Icons.Default.Star,
            Icons.Default.Star,
            Icons.Default.Star,
            Icons.Default.Favorite
        )
        
        icons.forEachIndexed { index, icon ->
            val angle = (index * 90f) * (PI / 180f)
            val radius = 20.dp
            
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = SharenShopColors.DeepBlue.copy(alpha = alpha * 0.7f),
                modifier = Modifier
                    .size(16.dp)
                    .offset(
                        x = (radius.value * cos(angle)).dp,
                        y = (radius.value * sin(angle)).dp
                    )
            )
        }
        
        // Center main icon
        Icon(
            imageVector = Icons.Default.Star,
            contentDescription = null,
            tint = SharenShopColors.DeepBlue.copy(alpha = alpha),
            modifier = Modifier.size(32.dp)
        )
    }
}

fun drawFashionRing(drawScope: DrawScope, color: Color) {
    with(drawScope) {
        val centerX = size.width / 2
        val centerY = size.height / 2
        val radius = size.minDimension / 2
        
        // Draw fashion-themed ring segments
        repeat(8) { index ->
            val startAngle = index * 45f
            val sweepAngle = 30f
            
            drawArc(
                color = color,
                startAngle = startAngle,
                sweepAngle = sweepAngle,
                useCenter = false,
                style = androidx.compose.ui.graphics.drawscope.Stroke(width = 4.dp.toPx())
            )
        }
    }
}
