# تنظیم Authentication در Supabase

## 1. تنظیمات کلی Authentication

### در پنل Supabase > Authentication > Settings:

1. **Site URL**: `http://localhost:3000` (برای development)
2. **Redirect URLs**: 
   - `http://localhost:3000/**`
   - URL اپلیکیشن اندروید شما
3. **Email Templates**: سفارشی‌سازی ایمیل‌های تایید
4. **Password Policy**: 
   - حداقل 8 کاراکتر
   - شامل حروف و اعداد

## 2. ایجاد کاربران پیش‌فرض

### در پنل Supabase > Authentication > Users:

#### مدیران کل:
1. **مدیر اصلی**:
   - **Email**: `<EMAIL>`
   - **Password**: `Admin123!@#`
   - **Username**: `admin`
   - **Email Confirmed**: ✅
   - **Phone Confirmed**: ✅

2. **مدیر دوم**:
   - **Email**: `<EMAIL>`
   - **Password**: `Manager123!@#`
   - **Username**: `manager`
   - **Email Confirmed**: ✅
   - **Phone Confirmed**: ✅

#### فروشندگان:
1. **احمد محمدی**:
   - Email: `<EMAIL>`
   - Password: `Ahmad123!@#`
   - Username: `ahmad_seller`

2. **علی رضایی**:
   - Email: `<EMAIL>`
   - Password: `Ali123!@#`
   - Username: `ali_seller`

3. **فاطمه احمدی**:
   - Email: `<EMAIL>`
   - Password: `Fateme123!@#`
   - Username: `fateme_seller`

## 3. تنظیم Custom Claims

### ایجاد Function برای تنظیم نقش کاربر:

```sql
-- در SQL Editor اجرا کنید
CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
RETURNS TEXT AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT user_type INTO user_role
    FROM public.users
    WHERE id = user_id;
    
    RETURN COALESCE(user_role, 'customer');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تنظیم دسترسی
GRANT EXECUTE ON FUNCTION public.get_user_role(UUID) TO authenticated;
```

## 4. تنظیم Realtime

### فعال کردن Realtime برای جداول مورد نیاز:

در پنل Supabase > Database > Replication:

✅ `notifications` - برای نوتیفیکیشن‌های لحظه‌ای
✅ `customer_approval_requests` - برای درخواست‌های تایید
✅ `invoices` - برای بروزرسانی فاکتورها
✅ `products` - برای بروزرسانی موجودی

## 5. تنظیم Storage (اختیاری)

### برای ذخیره تصاویر محصولات:

1. ایجاد Bucket جدید: `product-images`
2. تنظیم Policy:
   ```sql
   -- مشاهده عمومی
   CREATE POLICY "Public read access" ON storage.objects
   FOR SELECT USING (bucket_id = 'product-images');
   
   -- آپلود فقط برای مدیر و فروشندگان
   CREATE POLICY "Admin and cashier upload" ON storage.objects
   FOR INSERT WITH CHECK (
       bucket_id = 'product-images' AND
       EXISTS (
           SELECT 1 FROM users 
           WHERE id = auth.uid() AND user_type IN ('super_admin', 'cashier')
       )
   );
   ```

## 6. تنظیم Environment Variables

### در فایل `local.properties`:

```properties
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

## 7. تست کاربران

### برای تست سیستم:

1. **ورود مدیر اصلی**: `<EMAIL>` / `Admin123!@#`
2. **ورود مدیر دوم**: `<EMAIL>` / `Manager123!@#`
3. **ورود فروشنده**: `<EMAIL>` / `Ahmad123!@#`
4. **ثبت نام مشتری**: با کد معرف `ahmad_seller`

## 8. نکات مهم

- ✅ همه رمزهای عبور باید قوی باشند
- ✅ Email Confirmation را فعال کنید
- ✅ Rate Limiting را تنظیم کنید
- ✅ CORS را برای دامنه اپلیکیشن تنظیم کنید
- ✅ API Keys را امن نگه دارید

## 9. مراحل اجرا

1. اسکریپت `supabase_setup.sql` را اجرا کنید
2. کاربران را در Authentication > Users ایجاد کنید
3. اسکریپت `supabase_default_users.sql` را اجرا کنید
4. اسکریپت `supabase_rls_policies.sql` را اجرا کنید
5. Realtime را فعال کنید
6. Environment Variables را تنظیم کنید
7. اپلیکیشن را تست کنید
