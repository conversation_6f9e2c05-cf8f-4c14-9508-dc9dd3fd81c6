package com.example.sharenshop.data.remote;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0010\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\u00020\u0004X\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0014\u0010\u0011\u001a\u00020\u0004X\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010R\u000e\u0010\u0013\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/example/sharenshop/data/remote/SupabaseConstants;", "", "()V", "CUSTOMERS_TABLE", "", "INVOICES_TABLE", "INVOICE_ITEMS_TABLE", "NOTIFICATIONS_TABLE", "PAYMENTS_TABLE", "PRODUCTS_TABLE", "SELLERS_TABLE", "SETTINGS_TABLE", "SETTLEMENTS_TABLE", "SETTLEMENT_ITEMS_TABLE", "SUPABASE_PUBLIC_KEY", "getSUPABASE_PUBLIC_KEY", "()Ljava/lang/String;", "SUPABASE_URL", "getSUPABASE_URL", "USERS_TABLE", "app_debug"})
public final class SupabaseConstants {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String SUPABASE_URL = "https://ddpiwmgmdcfowfelbzno.supabase.co";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String SUPABASE_PUBLIC_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRkcGl3bWdtZGNmb3dmZWxiem5vIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyNTg2MDgsImV4cCI6MjA2MjgzNDYwOH0.hhn-5VQ6YU-U9IjPF7DD_K1cCyQllbjt8sF89x15B7E";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String USERS_TABLE = "users";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String PRODUCTS_TABLE = "products";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CUSTOMERS_TABLE = "customers";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SELLERS_TABLE = "sellers";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String INVOICES_TABLE = "invoices";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String INVOICE_ITEMS_TABLE = "invoice_items";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String PAYMENTS_TABLE = "payments";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String NOTIFICATIONS_TABLE = "notifications";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SETTLEMENTS_TABLE = "settlements";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SETTLEMENT_ITEMS_TABLE = "settlement_items";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SETTINGS_TABLE = "settings";
    @org.jetbrains.annotations.NotNull()
    public static final com.example.sharenshop.data.remote.SupabaseConstants INSTANCE = null;
    
    private SupabaseConstants() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSUPABASE_URL() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSUPABASE_PUBLIC_KEY() {
        return null;
    }
}