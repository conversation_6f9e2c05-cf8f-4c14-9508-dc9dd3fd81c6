// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.security.DatabaseEncryptionService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SecurityModule_Companion_ProvideDatabaseEncryptionServiceFactory implements Factory<DatabaseEncryptionService> {
  @Override
  public DatabaseEncryptionService get() {
    return provideDatabaseEncryptionService();
  }

  public static SecurityModule_Companion_ProvideDatabaseEncryptionServiceFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static DatabaseEncryptionService provideDatabaseEncryptionService() {
    return Preconditions.checkNotNullFromProvides(SecurityModule.Companion.provideDatabaseEncryptionService());
  }

  private static final class InstanceHolder {
    private static final SecurityModule_Companion_ProvideDatabaseEncryptionServiceFactory INSTANCE = new SecurityModule_Companion_ProvideDatabaseEncryptionServiceFactory();
  }
}
