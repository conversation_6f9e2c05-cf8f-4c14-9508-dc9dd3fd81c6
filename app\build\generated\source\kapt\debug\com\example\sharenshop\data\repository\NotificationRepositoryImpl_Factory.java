// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.repository;

import com.example.sharenshop.data.local.dao.NotificationDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NotificationRepositoryImpl_Factory implements Factory<NotificationRepositoryImpl> {
  private final Provider<NotificationDao> notificationDaoProvider;

  public NotificationRepositoryImpl_Factory(Provider<NotificationDao> notificationDaoProvider) {
    this.notificationDaoProvider = notificationDaoProvider;
  }

  @Override
  public NotificationRepositoryImpl get() {
    return newInstance(notificationDaoProvider.get());
  }

  public static NotificationRepositoryImpl_Factory create(
      Provider<NotificationDao> notificationDaoProvider) {
    return new NotificationRepositoryImpl_Factory(notificationDaoProvider);
  }

  public static NotificationRepositoryImpl newInstance(NotificationDao notificationDao) {
    return new NotificationRepositoryImpl(notificationDao);
  }
}
