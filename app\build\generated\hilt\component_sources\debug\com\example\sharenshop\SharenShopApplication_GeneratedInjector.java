package com.example.sharenshop;

import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = SharenShopApplication.class
)
@GeneratedEntryPoint
@InstallIn(SingletonComponent.class)
public interface SharenShopApplication_GeneratedInjector {
  void injectSharenShopApplication(SharenShopApplication sharenShopApplication);
}
