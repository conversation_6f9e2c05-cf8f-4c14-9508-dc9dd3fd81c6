package com.example.sharenshop.data.model;

/**
 * جزئیات کامل تصویه برای نمایش
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B1\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b\u00a2\u0006\u0002\u0010\nJ\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u0014\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u00c6\u0003J9\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00052\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011\u00a8\u0006\u001d"}, d2 = {"Lcom/example/sharenshop/data/model/SettlementDetails;", "", "settlement", "Lcom/example/sharenshop/data/model/Settlement;", "sellerName", "", "managerName", "relatedPayments", "", "Lcom/example/sharenshop/data/model/Payment;", "(Lcom/example/sharenshop/data/model/Settlement;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)V", "getManagerName", "()Ljava/lang/String;", "getRelatedPayments", "()Ljava/util/List;", "getSellerName", "getSettlement", "()Lcom/example/sharenshop/data/model/Settlement;", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class SettlementDetails {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.data.model.Settlement settlement = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String sellerName = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String managerName = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.sharenshop.data.model.Payment> relatedPayments = null;
    
    public SettlementDetails(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Settlement settlement, @org.jetbrains.annotations.NotNull()
    java.lang.String sellerName, @org.jetbrains.annotations.Nullable()
    java.lang.String managerName, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.sharenshop.data.model.Payment> relatedPayments) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.model.Settlement getSettlement() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSellerName() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getManagerName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.sharenshop.data.model.Payment> getRelatedPayments() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.model.Settlement component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.sharenshop.data.model.Payment> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.model.SettlementDetails copy(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Settlement settlement, @org.jetbrains.annotations.NotNull()
    java.lang.String sellerName, @org.jetbrains.annotations.Nullable()
    java.lang.String managerName, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.sharenshop.data.model.Payment> relatedPayments) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}