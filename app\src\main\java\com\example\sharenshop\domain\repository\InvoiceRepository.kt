package com.example.sharenshop.domain.repository

import com.example.sharenshop.data.model.Invoice
import kotlinx.coroutines.flow.Flow

interface InvoiceRepository {
    fun getInvoiceById(invoiceId: String): Flow<Invoice?>
    suspend fun insertInvoice(invoice: Invoice)
    suspend fun updateInvoice(invoice: Invoice)
    suspend fun deleteInvoiceById(invoiceId: String)
    fun getAllInvoices(): Flow<List<Invoice>>
    fun getInvoicesByCustomerId(customerId: String): Flow<List<Invoice>>
    fun getInvoicesByUserId(userId: String): Flow<List<Invoice>>
} 