package com.example.sharenshop.domain.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\b\n\u0002\b\u0004\bf\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0006H&J\u0016\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\b\u001a\u00020\u0006H&J\u001e\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u000bH&J\"\u0010\r\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u000f0\u000e0\u00032\u0006\u0010\u0010\u001a\u00020\u000fH&J\u000e\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0003H&J\u000e\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H&\u00a8\u0006\u0013"}, d2 = {"Lcom/example/sharenshop/domain/repository/StatisticsRepository;", "", "getCustomerSpending", "Lkotlinx/coroutines/flow/Flow;", "Ljava/math/BigDecimal;", "customerId", "", "getSalesByPaymentStatus", "status", "getSalesByTimePeriod", "startTime", "", "endTime", "getTopSellingProducts", "", "", "limit", "getTotalProductsSold", "getTotalSalesAmount", "app_debug"})
public abstract interface StatisticsRepository {
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.math.BigDecimal> getTotalSalesAmount();
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.lang.Integer> getTotalProductsSold();
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.Map<java.lang.String, java.lang.Integer>> getTopSellingProducts(int limit);
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.math.BigDecimal> getCustomerSpending(@org.jetbrains.annotations.NotNull()
    java.lang.String customerId);
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.math.BigDecimal> getSalesByPaymentStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String status);
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.math.BigDecimal> getSalesByTimePeriod(long startTime, long endTime);
}