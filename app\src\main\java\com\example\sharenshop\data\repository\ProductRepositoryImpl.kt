package com.example.sharenshop.data.repository

import com.example.sharenshop.data.local.dao.ProductDao
// import com.example.sharenshop.data.remote.SupabaseConstants
// import com.example.sharenshop.data.remote.SupabaseManager
import com.example.sharenshop.domain.repository.ProductRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import com.example.sharenshop.data.model.Product

class ProductRepositoryImpl @Inject constructor(
    private val productDao: ProductDao,
//    private val supabaseManager: SupabaseManager
) : ProductRepository {

    override fun getProductById(productId: String): Flow<Product?> = flow {
        productDao.getProductById(productId).collect { localProduct ->
            if (localProduct != null) {
                emit(localProduct)
            }
            try {
                // val response = supabaseManager.postgrest
                //     .from(SupabaseConstants.PRODUCTS_TABLE)
                //     .select()
                //     .eq("id", productId)
                //     .limit(1)
                //     .single()
                //     .execute()
                // TODO: Deserialize response.data to Product object
                val remoteProduct: Product? = null // Placeholder for deserialized product

                if (remoteProduct != null) {
                    productDao.insertProduct(remoteProduct)
                    emit(remoteProduct)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override suspend fun insertProduct(product: Product) {
        productDao.insertProduct(product)
        try {
            // val supabaseManager = SupabaseManager()
            // supabaseManager.postgrest
            //     .from(SupabaseConstants.PRODUCTS_TABLE)
            //     .insert(product)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override suspend fun updateProduct(product: Product) {
        productDao.updateProduct(product)
        try {
            // val supabaseManager = SupabaseManager()
            // supabaseManager.postgrest
            //     .from(SupabaseConstants.PRODUCTS_TABLE)
            //     .update(product)
            //     .eq("id", product.id)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override suspend fun deleteProductById(productId: String) {
        productDao.deleteProductById(productId)
        try {
            // val supabaseManager = SupabaseManager()
            // supabaseManager.postgrest
            //     .from(SupabaseConstants.PRODUCTS_TABLE)
            //     .delete()
            //     .eq("id", productId)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun getAllProducts(): Flow<List<Product>> = flow {
        productDao.getAllProducts().collect { emit(it) }
    }

    override fun searchProducts(query: String): Flow<List<Product>> = flow {
        productDao.searchProducts(query).collect { emit(it) }
    }
} 