# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users can modify this file to configure Gradle project settings.

# IDE (e.g. Android Studio) users can modify this file to configure Gradle project settings.
# In most cases you probably don't need to modify this file.

# Increase the maximum heap size for the Gradle daemon to 2GB.
# This is crucial for large Android projects and projects using KSP/annotation processors.
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
org.gradle.java.home=C:\\Users\\<USER>\\.jdks\\ms-17.0.15
# When set to true, Grad<PERSON> will run in daemon mode.
# The Gradle daemon is a long-lived background process that improves the performance of builds.
org.gradle.daemon=true

# Prevents Gradle from using offline mode
org.gradle.offline=false

# Enables the configuration cache, which speeds up build times.
# It caches the result of the configuration phase and reuses it for subsequent builds.
org.gradle.configuration.cache=true

# Enables the Build Cache.
# The build cache stores reusable outputs of build tasks (e.g. compiled classes) and reuses them in subsequent builds.
org.gradle.caching=true

# Android X - Enable Jetpack Compose
android.useAndroidX=true
android.nonTransitiveRClass=true

# Enable Kotlin Coroutines stable API
kotlin.coroutines.enable.jvm.target.validation=false

# حذف وابستگی‌های قدیمی supabase و جایگزینی با نسخه جدید از Maven Central
# نیازی به تغییر در این فایل نیست، فقط build.gradle.kts را به‌روزرسانی کردیم.

org.gradle.user.home=C:\Users\<USER>\.gradle
