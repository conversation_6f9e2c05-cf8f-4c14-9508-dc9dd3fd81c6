// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.local.dao.UserDao;
import com.example.sharenshop.data.local.database.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaoModule_ProvideUserDaoFactory implements Factory<UserDao> {
  private final Provider<AppDatabase> appDatabaseProvider;

  public DaoModule_ProvideUserDaoFactory(Provider<AppDatabase> appDatabaseProvider) {
    this.appDatabaseProvider = appDatabaseProvider;
  }

  @Override
  public UserDao get() {
    return provideUserDao(appDatabaseProvider.get());
  }

  public static DaoModule_ProvideUserDaoFactory create(Provider<AppDatabase> appDatabaseProvider) {
    return new DaoModule_ProvideUserDaoFactory(appDatabaseProvider);
  }

  public static UserDao provideUserDao(AppDatabase appDatabase) {
    return Preconditions.checkNotNullFromProvides(DaoModule.INSTANCE.provideUserDao(appDatabase));
  }
}
