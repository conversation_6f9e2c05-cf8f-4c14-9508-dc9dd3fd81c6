package com.example.sharenshop.data.security;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004H\u0016J\u0010\u0010\u0006\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u0004H\u0016\u00a8\u0006\b"}, d2 = {"Lcom/example/sharenshop/data/security/SharedPreferencesEncryptionService;", "Lcom/example/sharenshop/data/security/EncryptionService;", "()V", "decrypt", "", "encryptedData", "encrypt", "data", "app_debug"})
public final class SharedPreferencesEncryptionService implements com.example.sharenshop.data.security.EncryptionService {
    
    @javax.inject.Inject()
    public SharedPreferencesEncryptionService() {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String encrypt(@org.jetbrains.annotations.NotNull()
    java.lang.String data) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String decrypt(@org.jetbrains.annotations.NotNull()
    java.lang.String encryptedData) {
        return null;
    }
}