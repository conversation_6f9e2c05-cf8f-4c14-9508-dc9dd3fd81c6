package com.example.sharenshop.data.security

import javax.inject.Inject
import javax.inject.<PERSON>ton

@Singleton
class SharedPreferencesEncryptionService @Inject constructor() : EncryptionService {
    override fun encrypt(data: String): String {
        // TODO: Implement actual shared preferences encryption logic
        return "ENCRYPTED_SP:" + data
    }

    override fun decrypt(encryptedData: String): String {
        // TODO: Implement actual shared preferences decryption logic
        return encryptedData.replace("ENCRYPTED_SP:", "")
    }
} 