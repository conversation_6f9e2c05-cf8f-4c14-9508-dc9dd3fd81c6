# 🔧 راهنمای بروزرسانی تنظیمات Supabase

## 📋 وضعیت فعلی:

✅ **تنظیمات موجود در پروژه:**
- `local.properties` ✅
- `build.gradle.kts` ✅ 
- `NetworkModule.kt` ✅
- کتابخانه‌های Supabase ✅

## 🎯 **مراحل بروزرسانی:**

### **مرحله 1: بروزرسانی local.properties**

فایل `local.properties` را باز کنید و مقادیر زیر را با اطلاعات واقعی Supabase جایگزین کنید:

```properties
## This file must *NOT* be checked into Version Control Systems,
# as it contains information specific to your local configuration.
#
# Location of the SDK. This is only used by Gradle.
# For customization when using a Version Control System, please read the
# header note.
#Tue Jun 03 02:23:19 IRST 2025
sdk.dir=C\\:\\Users\\alika\\AppData\\Local\\Android\\Sdk

# Supabase Configuration
SUPABASE_URL=https://YOUR_PROJECT_ID.supabase.co
SUPABASE_PUBLIC_KEY=YOUR_ANON_PUBLIC_KEY_HERE
```

### **مرحله 2: دریافت اطلاعات از پنل Supabase**

1. **وارد پنل Supabase شوید:** https://supabase.com/dashboard
2. **پروژه خود را انتخاب کنید**
3. **به Settings > API بروید**
4. **اطلاعات زیر را کپی کنید:**

#### **Project URL:**
```
https://YOUR_PROJECT_ID.supabase.co
```

#### **Anon Public Key:**
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### **مرحله 3: جایگزینی در local.properties**

```properties
# جایگزین کنید:
SUPABASE_URL=https://xyzabc123.supabase.co
SUPABASE_PUBLIC_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh5emFiYzEyMyIsInJvbGUiOiJhbm9uIiwiaWF0IjoxNjc4ODg4ODg4LCJleHAiOjE5OTQ0NjQ4ODh9.EXAMPLE_KEY_HERE
```

### **مرحله 4: Gradle Sync**

1. **Android Studio** را باز کنید
2. **Sync Now** را کلیک کنید یا `Ctrl+Shift+O`
3. **Build > Clean Project**
4. **Build > Rebuild Project**

### **مرحله 5: تست اتصال**

کد زیر را در یکی از فایل‌ها اضافه کنید تا اتصال را تست کنید:

```kotlin
// در AuthViewModel یا هر جای مناسب
private fun testSupabaseConnection() {
    android.util.Log.d("Supabase", "URL: ${BuildConfig.SUPABASE_URL}")
    android.util.Log.d("Supabase", "Key: ${BuildConfig.SUPABASE_PUBLIC_KEY.take(20)}...")
    
    if (BuildConfig.SUPABASE_URL.contains("your-project")) {
        android.util.Log.e("Supabase", "❌ URL هنوز بروزرسانی نشده!")
    } else {
        android.util.Log.i("Supabase", "✅ URL بروزرسانی شده")
    }
}
```

## 🚨 **نکات مهم:**

### **امنیت:**
- ❌ **هرگز** `local.properties` را commit نکنید
- ✅ فایل در `.gitignore` قرار دارد
- ✅ فقط `ANON PUBLIC KEY` استفاده کنید، نه `SERVICE ROLE KEY`

### **خطایابی:**
اگر خطا دریافت کردید:

1. **بررسی Logcat:**
```
E/NetworkModule: Supabase URL or Key is blank. Check local.properties and Gradle sync.
```

2. **بررسی BuildConfig:**
```kotlin
Log.d("Debug", "URL: ${BuildConfig.SUPABASE_URL}")
Log.d("Debug", "Key: ${BuildConfig.SUPABASE_PUBLIC_KEY}")
```

3. **Clean & Rebuild:**
```
Build > Clean Project
Build > Rebuild Project
```

## ✅ **چک لیست:**

- [ ] URL از پنل Supabase کپی شد
- [ ] ANON KEY از پنل Supabase کپی شد  
- [ ] local.properties بروزرسانی شد
- [ ] Gradle Sync انجام شد
- [ ] Clean & Rebuild انجام شد
- [ ] اپلیکیشن بدون خطا اجرا می‌شود
- [ ] لاگ‌ها نشان‌دهنده اتصال موفق هستند

## 🔄 **مرحله بعدی:**

پس از بروزرسانی موفق، آماده اجرای اسکریپت‌های SQL در پنل Supabase هستید:

1. `supabase_setup.sql` - ایجاد جداول
2. `supabase_default_users.sql` - کاربران پیش‌فرض  
3. `supabase_rls_policies.sql` - تنظیم امنیت

آیا نیاز به کمک در هر مرحله دارید؟
