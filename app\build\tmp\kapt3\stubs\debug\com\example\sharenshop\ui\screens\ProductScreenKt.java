package com.example.sharenshop.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000<\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\u001a4\u0010\u0000\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0007H\u0007\u001a@\u0010\b\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\n2\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00072\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0007H\u0007\u001a\u001c\u0010\r\u001a\u00020\u00012\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\t\u001a\u00020\nH\u0007\u001a\u001e\u0010\u0010\u001a\u00020\u00012\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00030\u00122\u0006\u0010\t\u001a\u00020\nH\u0007\u001a,\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u0014\u001a\u00020\u00152\u0012\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u00010\u00072\u0006\u0010\u0017\u001a\u00020\u0015H\u0007\u00a8\u0006\u0018"}, d2 = {"AddEditProductDialog", "", "product", "Lcom/example/sharenshop/data/model/Product;", "onDismiss", "Lkotlin/Function0;", "onSave", "Lkotlin/Function1;", "ProductCard", "userRole", "Lcom/example/sharenshop/data/model/UserRole;", "onEditClick", "onDeleteClick", "ProductScreen", "viewModel", "Lcom/example/sharenshop/presentation/product/ProductViewModel;", "ProductStatsRow", "products", "", "SearchBar", "query", "", "onQueryChange", "placeholder", "app_debug"})
public final class ProductScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ProductScreen(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.product.ProductViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SearchBar(@org.jetbrains.annotations.NotNull()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onQueryChange, @org.jetbrains.annotations.NotNull()
    java.lang.String placeholder) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ProductStatsRow(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.sharenshop.data.model.Product> products, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ProductCard(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Product product, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.sharenshop.data.model.Product, kotlin.Unit> onEditClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.sharenshop.data.model.Product, kotlin.Unit> onDeleteClick) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AddEditProductDialog(@org.jetbrains.annotations.Nullable()
    com.example.sharenshop.data.model.Product product, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.sharenshop.data.model.Product, kotlin.Unit> onSave) {
    }
}