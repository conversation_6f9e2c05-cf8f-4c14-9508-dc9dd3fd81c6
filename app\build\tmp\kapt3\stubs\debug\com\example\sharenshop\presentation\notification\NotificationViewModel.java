package com.example.sharenshop.presentation.notification;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u0012\u001a\u00020\u0013J\u000e\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\u0016J\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00160\u00182\u0006\u0010\u0019\u001a\u00020\u001aJ\u0010\u0010\u001b\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\tH\u0002J\u0010\u0010\u001d\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\tH\u0002J\u0006\u0010\u001e\u001a\u00020\u0013J\u000e\u0010\u001f\u001a\u00020\u00132\u0006\u0010 \u001a\u00020\tJ2\u0010!\u001a\u00020\u00132\u0006\u0010\"\u001a\u00020\t2\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010#\u001a\u00020\t2\u0006\u0010$\u001a\u00020\t2\n\b\u0002\u0010%\u001a\u0004\u0018\u00010\tJ\u000e\u0010&\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\tR\u0016\u0010\u0007\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\'"}, d2 = {"Lcom/example/sharenshop/presentation/notification/NotificationViewModel;", "Landroidx/lifecycle/ViewModel;", "notificationRepository", "Lcom/example/sharenshop/domain/repository/NotificationRepository;", "userRepository", "Lcom/example/sharenshop/domain/repository/UserRepository;", "(Lcom/example/sharenshop/domain/repository/NotificationRepository;Lcom/example/sharenshop/domain/repository/UserRepository;)V", "_currentUserId", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_uiState", "Lcom/example/sharenshop/presentation/notification/NotificationUiState;", "currentUserId", "Lkotlinx/coroutines/flow/StateFlow;", "getCurrentUserId", "()Lkotlinx/coroutines/flow/StateFlow;", "uiState", "getUiState", "clearError", "", "deleteNotification", "notification", "Lcom/example/sharenshop/data/model/Notification;", "getNotificationsByType", "", "type", "Lcom/example/sharenshop/data/model/NotificationType;", "loadNotifications", "userId", "loadUnreadCount", "markAllAsRead", "markAsRead", "notificationId", "sendNotification", "receiverUserId", "title", "message", "data", "setCurrentUser", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class NotificationViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.repository.NotificationRepository notificationRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.repository.UserRepository userRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.sharenshop.presentation.notification.NotificationUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.sharenshop.presentation.notification.NotificationUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _currentUserId = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> currentUserId = null;
    
    @javax.inject.Inject()
    public NotificationViewModel(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.NotificationRepository notificationRepository, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.UserRepository userRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.sharenshop.presentation.notification.NotificationUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getCurrentUserId() {
        return null;
    }
    
    public final void setCurrentUser(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
    }
    
    private final void loadNotifications(java.lang.String userId) {
    }
    
    private final void loadUnreadCount(java.lang.String userId) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.sharenshop.data.model.Notification> getNotificationsByType(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.NotificationType type) {
        return null;
    }
    
    public final void markAsRead(@org.jetbrains.annotations.NotNull()
    java.lang.String notificationId) {
    }
    
    public final void markAllAsRead() {
    }
    
    public final void sendNotification(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverUserId, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.NotificationType type, @org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String message, @org.jetbrains.annotations.Nullable()
    java.lang.String data) {
    }
    
    public final void clearError() {
    }
    
    public final void deleteNotification(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Notification notification) {
    }
}