package com.example.sharenshop.data.local.database

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.example.sharenshop.data.local.converter.BigDecimalConverter
import com.example.sharenshop.data.local.dao.CustomerDao
import com.example.sharenshop.data.local.dao.InvoiceDao
import com.example.sharenshop.data.local.dao.InvoiceItemDao
import com.example.sharenshop.data.local.dao.NotificationDao
import com.example.sharenshop.data.local.dao.ProductDao
import com.example.sharenshop.data.local.dao.SellerDao
import com.example.sharenshop.data.local.dao.UserDao
// Removed problematic DAO imports
import com.example.sharenshop.data.model.Customer
import com.example.sharenshop.data.model.Invoice
import com.example.sharenshop.data.model.InvoiceItem
import com.example.sharenshop.data.model.Notification
import com.example.sharenshop.data.model.Product
import com.example.sharenshop.data.model.Seller
import com.example.sharenshop.data.model.User
// Removed problematic imports

@Database(
    entities = [
        User::class,
        Product::class,
        Customer::class,
        Seller::class,
        Invoice::class,
        InvoiceItem::class,
        Notification::class
    ],
    version = 3, // نسخه جدید - Notification entity اضافه شد
    exportSchema = false
)
@TypeConverters(BigDecimalConverter::class)
abstract class AppDatabase : RoomDatabase() {
    abstract fun userDao(): UserDao
    abstract fun productDao(): ProductDao
    abstract fun customerDao(): CustomerDao
    abstract fun sellerDao(): SellerDao
    abstract fun invoiceDao(): InvoiceDao
    abstract fun invoiceItemDao(): InvoiceItemDao
    abstract fun notificationDao(): NotificationDao
    // TODO: Add payment-related DAOs when needed
}