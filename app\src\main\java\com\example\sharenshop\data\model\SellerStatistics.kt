package com.example.sharenshop.data.model

import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import java.math.BigDecimal

/**
 * آمار کامل فروشنده برای مدیر کل
 */
@Serializable
data class SellerStatistics(
    val sellerId: String,
    val sellerName: String,
    val sellerEmail: String,
    
    // آمار فروش
    @Contextual
    val totalSales: BigDecimal, // کل فروش
    @Contextual
    val cashSales: BigDecimal, // فروش نقدی
    @Contextual
    val creditSales: BigDecimal, // فروش نسیه
    
    // آمار فاکتورها
    val totalInvoices: Int, // تعداد کل فاکتورها
    val paidInvoices: Int, // فاکتورهای پرداخت شده
    val pendingInvoices: Int, // فاکتورهای در انتظار
    val overdueInvoices: Int, // فاکتورهای معوقه
    
    // آمار مشتریان
    val totalCustomers: Int, // تعداد کل مشتریان
    val newCustomersThisMonth: Int, // مشتریان جدید این ماه
    val activeCustomers: Int, // مشتریان فعال
    
    // آمار زمانی
    @Contextual
    val salesThisMonth: BigDecimal, // فروش این ماه
    @Contextual
    val salesLastMonth: BigDecimal, // فروش ماه گذشته
    @Contextual
    val salesThisYear: BigDecimal, // فروش امسال

    // آمار عملکرد
    @Contextual
    val averageInvoiceValue: BigDecimal, // میانگین ارزش فاکتور
    val bestSellingDay: String?, // بهترین روز فروش
    val performanceScore: Double, // امتیاز عملکرد (0-100)
    
    val lastActivityDate: Long, // آخرین فعالیت
    val createdAt: Long
)

/**
 * خلاصه آمار کلی برای داشبورد مدیر کل
 */
@Serializable
data class OverallStatistics(
    // آمار کلی فروش
    @Contextual
    val totalSales: BigDecimal,
    @Contextual
    val totalCashSales: BigDecimal,
    @Contextual
    val totalCreditSales: BigDecimal,
    
    // آمار کلی فاکتورها
    val totalInvoices: Int,
    val totalPaidInvoices: Int,
    val totalPendingInvoices: Int,
    val totalOverdueInvoices: Int,
    
    // آمار کلی مشتریان
    val totalCustomers: Int,
    val totalNewCustomersThisMonth: Int,
    val totalActiveCustomers: Int,
    
    // آمار فروشندگان
    val totalSellers: Int,
    val activeSellers: Int,
    val topPerformingSeller: String?,
    
    // آمار زمانی
    @Contextual
    val salesThisMonth: BigDecimal,
    @Contextual
    val salesLastMonth: BigDecimal,
    val salesGrowthPercentage: Double,
    
    val generatedAt: Long
)

/**
 * آمار مقایسه‌ای فروشندگان
 */
@Serializable
data class SellerComparison(
    val sellerId: String,
    val sellerName: String,
    @Contextual
    val totalSales: BigDecimal,
    val totalInvoices: Int,
    val totalCustomers: Int,
    val performanceRank: Int, // رتبه عملکرد
    val salesRank: Int, // رتبه فروش
    val customerRank: Int // رتبه تعداد مشتری
)

/**
 * آمار روزانه برای نمودار
 */
@Serializable
data class DailySalesStatistics(
    val date: String, // YYYY-MM-DD
    @Contextual
    val totalSales: BigDecimal,
    @Contextual
    val cashSales: BigDecimal,
    @Contextual
    val creditSales: BigDecimal,
    val invoiceCount: Int,
    val newCustomers: Int
)

/**
 * آمار ماهانه برای نمودار
 */
@Serializable
data class MonthlySalesStatistics(
    val month: String, // YYYY-MM
    @Contextual
    val totalSales: BigDecimal,
    @Contextual
    val cashSales: BigDecimal,
    @Contextual
    val creditSales: BigDecimal,
    val invoiceCount: Int,
    val newCustomers: Int,
    val activeSellers: Int
)

/**
 * آمار محصولات پرفروش
 */
@Serializable
data class TopSellingProduct(
    val productId: String,
    val productName: String,
    val totalQuantitySold: Int,
    @Contextual
    val totalRevenue: BigDecimal,
    @Contextual
    val averagePrice: BigDecimal,
    val soldBySellers: List<String> // لیست فروشندگانی که این محصول را فروخته‌اند
)

/**
 * آمار مشتریان برتر
 */
@Serializable
data class TopCustomer(
    val customerId: String,
    val customerName: String,
    @Contextual
    val totalPurchases: BigDecimal,
    val totalInvoices: Int,
    val lastPurchaseDate: Long,
    val referredBySeller: String // فروشنده معرف
)
