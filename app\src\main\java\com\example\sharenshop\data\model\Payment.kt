package com.example.sharenshop.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import java.math.BigDecimal

/**
 * مدل پرداخت - برای ثبت پرداخت‌های جزئی مشتریان
 */
@Serializable
@Entity(tableName = "payments")
data class Payment(
    @PrimaryKey(autoGenerate = false)
    val id: String,
    val customerId: String, // مشتری پرداخت کننده
    val receiverId: String, // فروشنده دریافت کننده
    @Contextual
    val amount: BigDecimal, // مبلغ پرداخت
    val paymentMethod: PaymentMethod, // نوع پرداخت
    val trackingNumber: String? = null, // شماره پیگیری (برای کارت به کارت)
    val description: String? = null, // توضیحات
    val status: PaymentStatus, // وضعیت پرداخت
    val paymentDate: Long, // تاریخ پرداخت
    val confirmationDate: Long? = null, // تاریخ تایید
    val rejectionReason: String? = null, // دلیل رد (در صورت رد)
    val createdAt: Long,
    val updatedAt: Long? = null
)

/**
 * نوع پرداخت
 */
@Serializable
enum class PaymentMethod(val value: String, val displayName: String) {
    CASH("cash", "نقدی حضوری"),
    CARD_TO_CARD("card_to_card", "کارت به کارت");
    
    companion object {
        fun fromString(value: String): PaymentMethod {
            return values().find { it.value == value } ?: CASH
        }
    }
}

/**
 * وضعیت پرداخت
 */
@Serializable
enum class PaymentStatus(val value: String, val displayName: String) {
    PENDING("pending", "در انتظار تایید"),
    CONFIRMED("confirmed", "تایید شده"),
    REJECTED("rejected", "رد شده");
    
    companion object {
        fun fromString(value: String): PaymentStatus {
            return values().find { it.value == value } ?: PENDING
        }
    }
}

/**
 * جزئیات کامل پرداخت برای نمایش
 */
data class PaymentDetails(
    val payment: Payment,
    val customerName: String,
    val receiverName: String,
    val remainingDebt: BigDecimal // مانده بدهی پس از این پرداخت
)
