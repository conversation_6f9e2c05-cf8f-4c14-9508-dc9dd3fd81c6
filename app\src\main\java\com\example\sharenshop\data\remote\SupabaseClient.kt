package com.example.sharenshop.data.remote

import com.example.sharenshop.BuildConfig
import io.github.jan.supabase.createSupabaseClient
import io.github.jan.supabase.gotrue.Auth
import io.github.jan.supabase.gotrue.auth
import io.github.jan.supabase.postgrest.Postgrest
import io.github.jan.supabase.postgrest.postgrest

/**
 * Supabase Client Configuration
 */
object SupabaseClient {
    
    val client = createSupabaseClient(
        supabaseUrl = BuildConfig.SUPABASE_URL,
        supabaseKey = BuildConfig.SUPABASE_PUBLIC_KEY
    ) {
        install(Auth)
        install(Postgrest)
    }
    
    val auth get() = client.auth
    val postgrest get() = client.postgrest
    
    fun isConfigured(): Boolean {
        return BuildConfig.SUPABASE_URL.isNotBlank() && 
               BuildConfig.SUPABASE_PUBLIC_KEY.isNotBlank()
    }
}
