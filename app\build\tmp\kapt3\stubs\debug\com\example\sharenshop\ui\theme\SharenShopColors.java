package com.example.sharenshop.ui.theme;

/**
 * SharenShop Custom Color Extensions
 * رنگ‌های سفارشی برای عناصر خاص اپلیکیشن
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b(\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0019\u0010\u0003\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0005\u0010\u0006R\u0019\u0010\b\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\t\u0010\u0006R\u0019\u0010\n\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u000b\u0010\u0006R\u0019\u0010\f\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\r\u0010\u0006R\u0019\u0010\u000e\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u000f\u0010\u0006R\u0019\u0010\u0010\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0011\u0010\u0006R\u0019\u0010\u0012\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0013\u0010\u0006R\u0019\u0010\u0014\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0015\u0010\u0006R\u0019\u0010\u0016\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0017\u0010\u0006R\u0019\u0010\u0018\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0019\u0010\u0006R\u0019\u0010\u001a\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u001b\u0010\u0006R\u0019\u0010\u001c\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u001d\u0010\u0006R\u0019\u0010\u001e\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u001f\u0010\u0006R\u0019\u0010 \u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b!\u0010\u0006R\u0019\u0010\"\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b#\u0010\u0006R\u0019\u0010$\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b%\u0010\u0006R\u0019\u0010&\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\'\u0010\u0006R\u0019\u0010(\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b)\u0010\u0006R\u0019\u0010*\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b+\u0010\u0006\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b!\u00a8\u0006,"}, d2 = {"Lcom/example/sharenshop/ui/theme/SharenShopColors;", "", "()V", "CashierColor", "Landroidx/compose/ui/graphics/Color;", "getCashierColor-0d7_KjU", "()J", "J", "CustomerColor", "getCustomerColor-0d7_KjU", "DarkBrown", "getDarkBrown-0d7_KjU", "DeepBlue", "getDeepBlue-0d7_KjU", "FutureDusk", "getFutureDusk-0d7_KjU", "InStock", "getInStock-0d7_KjU", "Info", "getInfo-0d7_KjU", "LossColor", "getLossColor-0d7_KjU", "LowStock", "getLowStock-0d7_KjU", "NeutralColor", "getNeutralColor-0d7_KjU", "OutOfStock", "getOutOfStock-0d7_KjU", "OverdueInvoice", "getOverdueInvoice-0d7_KjU", "PaidInvoice", "getPaidInvoice-0d7_KjU", "PendingInvoice", "getPendingInvoice-0d7_KjU", "ProfitColor", "getProfitColor-0d7_KjU", "SoftGray", "getSoftGray-0d7_KjU", "Success", "getSuccess-0d7_KjU", "SuperAdminColor", "getSuperAdminColor-0d7_KjU", "Warning", "getWarning-0d7_KjU", "app_debug"})
public final class SharenShopColors {
    private static final long FutureDusk = 0L;
    private static final long DeepBlue = 0L;
    private static final long SoftGray = 0L;
    private static final long DarkBrown = 0L;
    private static final long Success = 0L;
    private static final long Warning = 0L;
    private static final long Info = 0L;
    private static final long SuperAdminColor = 0L;
    private static final long CashierColor = 0L;
    private static final long CustomerColor = 0L;
    private static final long ProfitColor = 0L;
    private static final long LossColor = 0L;
    private static final long NeutralColor = 0L;
    private static final long PaidInvoice = 0L;
    private static final long PendingInvoice = 0L;
    private static final long OverdueInvoice = 0L;
    private static final long InStock = 0L;
    private static final long LowStock = 0L;
    private static final long OutOfStock = 0L;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.sharenshop.ui.theme.SharenShopColors INSTANCE = null;
    
    private SharenShopColors() {
        super();
    }
}