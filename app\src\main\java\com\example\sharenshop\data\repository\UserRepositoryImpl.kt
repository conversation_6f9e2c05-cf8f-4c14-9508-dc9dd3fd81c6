package com.example.sharenshop.data.repository

import com.example.sharenshop.data.local.dao.UserDao
// import com.example.sharenshop.data.remote.SupabaseManager
// import com.example.sharenshop.data.remote.SupabaseConstants
// import com.example.sharenshop.data.remote.postgrest
// import kotlinx.serialization.json.Json
import com.example.sharenshop.domain.repository.UserRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import com.example.sharenshop.data.model.User

class UserRepositoryImpl @Inject constructor(
    private val userDao: UserDao,
    // private val supabaseManager: SupabaseManager,
    // private val json: Json // Inject Json serializer
) : UserRepository {

    override fun getUserById(userId: String): Flow<User?> = flow {
        // Try to get from local database first
        userDao.getUserById(userId).collect { localUser ->
            if (localUser != null) {
                emit(localUser)
            } else {
                // Then try to fetch from remote (Supabase)
                // try {
                //     val response = supabaseManager.client.postgrest
                //         .from(SupabaseConstants.USERS_TABLE)
                //         .select()
                //         .eq("id", userId)
                //         .limit(1)
                //         .single()
                //         .execute()

                //     val remoteUser = response.data?.let { jsonString ->
                //         json.decodeFromString<User>(jsonString)
                //     }

                //     if (remoteUser != null) {
                //         userDao.insertUser(remoteUser) // Cache remote data locally
                //         emit(remoteUser)
                //     }
                // } catch (e: Exception) {
                //     // Handle network or Supabase errors
                //     e.printStackTrace()
                //     // Optionally emit an error state or null if no local data
                //     emit(null)
                // }
            }
        }
    }

    override suspend fun insertUser(user: User) {
        userDao.insertUser(user) // Insert locally first
        // try {
        //     val userJson = json.encodeToString(User.serializer(), user)
        //     supabaseManager.client.postgrest
        //         .from(SupabaseConstants.USERS_TABLE)
        //         .insert(userJson) // Send serialized user object
        //         .execute()
        // } catch (e: Exception) {
        //     e.printStackTrace()
        //     // Handle remote insert error - consider rolling back local insert or retrying
        // }
    }

    override suspend fun updateUser(user: User) {
        userDao.updateUser(user) // Update locally first
        // try {
        //     val userJson = json.encodeToString(User.serializer(), user)
        //     supabaseManager.client.postgrest
        //         .from(SupabaseConstants.USERS_TABLE)
        //         .update(userJson)
        //         .eq("id", user.id)
        //         .execute()
        // } catch (e: Exception) {
        //     e.printStackTrace()
        //     // Handle remote update error
        // }
    }

    override suspend fun deleteUserById(userId: String) {
        userDao.deleteUserById(userId) // Delete locally first
        // try {
        //     supabaseManager.client.postgrest
        //         .from(SupabaseConstants.USERS_TABLE)
        //         .delete()
        //         .eq("id", userId)
        //         .execute()
        // } catch (e: Exception) {
        //     e.printStackTrace()
        //     // Handle remote delete error
        // }
    }

    override fun getAllUsers(): Flow<List<User>> = flow {
        // For simplicity, fetching all from local. Real app might sync with remote.
        userDao.getAllUsers().collect { emit(it) }
        // TODO: Implement syncing all users from Supabase if needed, considering performance implications.
    }

    override fun getUsersByUserType(userType: String): Flow<List<User>> = flow {
        userDao.getUsersByUserType(userType).collect { emit(it) }
        // TODO: Implement syncing users by type from Supabase if needed.
    }

    override suspend fun getUserByUsername(username: String): User? {
        return userDao.getUserByUsername(username)
        // TODO: Implement remote fetch from Supabase if needed
    }

    override suspend fun getUserByEmail(email: String): User? {
        return userDao.getUserByEmail(email)
        // TODO: Implement remote fetch from Supabase if needed
    }
}