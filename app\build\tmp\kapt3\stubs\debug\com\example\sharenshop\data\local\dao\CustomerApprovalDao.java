package com.example.sharenshop.data.local.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0007\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0014\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\r0\fH\'J\u001c\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0016\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0018\u0010\u0015\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0016\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0018\u0010\u0017\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0018\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u001c\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\r0\f2\u0006\u0010\u0014\u001a\u00020\u0010H\'J\u001c\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\r0\f2\u0006\u0010\u001b\u001a\u00020\u001cH\'J\u0016\u0010\u001d\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u001e\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ&\u0010\u001f\u001a\u00020\u00032\u0006\u0010 \u001a\u00020\u00102\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010!\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\"\u00a8\u0006#"}, d2 = {"Lcom/example/sharenshop/data/local/dao/CustomerApprovalDao;", "", "deleteExpiredRequests", "", "expireDate", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteRequest", "request", "Lcom/example/sharenshop/data/model/CustomerApprovalRequest;", "(Lcom/example/sharenshop/data/model/CustomerApprovalRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllRequests", "Lkotlinx/coroutines/flow/Flow;", "", "getPendingRequestsByReferrerCode", "code", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPendingRequestsCount", "", "referrerId", "getRequestByCustomerEmail", "email", "getRequestById", "id", "getRequestsByReferrer", "getRequestsByStatus", "status", "Lcom/example/sharenshop/data/model/ApprovalStatus;", "insertRequest", "updateRequest", "updateRequestStatus", "requestId", "responseDate", "(Ljava/lang/String;Lcom/example/sharenshop/data/model/ApprovalStatus;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao()
public abstract interface CustomerApprovalDao {
    
    @androidx.room.Query(value = "SELECT * FROM customer_approval_requests WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRequestById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.sharenshop.data.model.CustomerApprovalRequest> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM customer_approval_requests ORDER BY requestDate DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharenshop.data.model.CustomerApprovalRequest>> getAllRequests();
    
    @androidx.room.Query(value = "SELECT * FROM customer_approval_requests WHERE referrerId = :referrerId ORDER BY requestDate DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharenshop.data.model.CustomerApprovalRequest>> getRequestsByReferrer(@org.jetbrains.annotations.NotNull()
    java.lang.String referrerId);
    
    @androidx.room.Query(value = "SELECT * FROM customer_approval_requests WHERE status = :status ORDER BY requestDate DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharenshop.data.model.CustomerApprovalRequest>> getRequestsByStatus(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.ApprovalStatus status);
    
    @androidx.room.Query(value = "SELECT * FROM customer_approval_requests WHERE customerEmail = :email")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRequestByCustomerEmail(@org.jetbrains.annotations.NotNull()
    java.lang.String email, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.sharenshop.data.model.CustomerApprovalRequest> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM customer_approval_requests WHERE referrerCode = :code AND status = \'pending\'")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPendingRequestsByReferrerCode(@org.jetbrains.annotations.NotNull()
    java.lang.String code, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.sharenshop.data.model.CustomerApprovalRequest>> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertRequest(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.CustomerApprovalRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateRequest(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.CustomerApprovalRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteRequest(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.CustomerApprovalRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE customer_approval_requests SET status = :status, responseDate = :responseDate WHERE id = :requestId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateRequestStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String requestId, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.ApprovalStatus status, long responseDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM customer_approval_requests WHERE status = \'expired\' OR (status = \'pending\' AND requestDate < :expireDate)")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteExpiredRequests(long expireDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM customer_approval_requests WHERE referrerId = :referrerId AND status = \'pending\'")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPendingRequestsCount(@org.jetbrains.annotations.NotNull()
    java.lang.String referrerId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
}