package com.example.sharenshop.presentation.statistics

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharenshop.domain.use_case.StatisticsUseCases
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import java.math.BigDecimal
import javax.inject.Inject

@HiltViewModel
class StatisticsViewModel @Inject constructor(
    private val statisticsUseCases: StatisticsUseCases
) : ViewModel() {

    private val _totalSalesAmount = MutableStateFlow(BigDecimal.ZERO)
    val totalSalesAmount: StateFlow<BigDecimal> = _totalSalesAmount.asStateFlow()

    private val _totalProductsSold = MutableStateFlow(0)
    val totalProductsSold: StateFlow<Int> = _totalProductsSold.asStateFlow()

    private val _topSellingProducts = MutableStateFlow<Map<String, Int>>(emptyMap())
    val topSellingProducts: StateFlow<Map<String, Int>> = _topSellingProducts.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    init {
        loadStatistics()
    }

    private fun loadStatistics() {
        _isLoading.value = true

        combine(
            statisticsUseCases.getTotalSalesAmount(),
            statisticsUseCases.getTotalProductsSold(),
            statisticsUseCases.getTopSellingProducts(5) // Top 5 products
        ) { sales, productsSold, topProducts ->
            _totalSalesAmount.value = sales
            _totalProductsSold.value = productsSold
            _topSellingProducts.value = topProducts
        }
            .catch { e ->
                _error.value = e.localizedMessage ?: "Unknown error loading statistics"
            }
            .onEach { _isLoading.value = false }
            .launchIn(viewModelScope)
    }

    // TODO: Add more functions to load specific statistics based on user interaction (e.g., filter by date)
} 