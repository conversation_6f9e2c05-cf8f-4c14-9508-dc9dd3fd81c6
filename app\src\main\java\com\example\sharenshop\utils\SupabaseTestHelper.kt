package com.example.sharenshop.utils

import android.util.Log
import com.example.sharenshop.BuildConfig
import com.example.sharenshop.data.remote.SupabaseClient
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Helper class برای تست اتصال Supabase
 */
object SupabaseTestHelper {

    private const val TAG = "SupabaseTest"

    /**
     * تست اتصال پایه Supabase
     */
    fun testConnection() {
        Log.d(TAG, "🔄 شروع تست اتصال Supabase...")

        // تست تنظیمات
        if (BuildConfig.SUPABASE_URL.isBlank()) {
            Log.e(TAG, "❌ SUPABASE_URL خالی است!")
            return
        }

        if (BuildConfig.SUPABASE_PUBLIC_KEY.isBlank()) {
            Log.e(TAG, "❌ SUPABASE_PUBLIC_KEY خالی است!")
            return
        }

        Log.i(TAG, "✅ تنظیمات Supabase موجود است")
        Log.d(TAG, "📍 URL: ${BuildConfig.SUPABASE_URL}")
        Log.d(TAG, "🔑 Key: ${BuildConfig.SUPABASE_PUBLIC_KEY.take(20)}...")

        // تست اتصال واقعی
        testRealConnection()
    }

    /**
     * تست اتصال واقعی به Supabase
     */
    private fun testRealConnection() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "🔄 تست اتصال واقعی...")

                // تست ساده: بررسی client
                if (SupabaseClient.isConfigured()) {
                    Log.i(TAG, "✅ SupabaseClient پیکربندی شده است")
                } else {
                    Log.w(TAG, "⚠️ SupabaseClient پیکربندی نشده است")
                }

                testDatabaseAccess()

            } catch (e: Exception) {
                Log.e(TAG, "❌ خطای اتصال: ${e.message}")
            }
        }
    }

    /**
     * تست دسترسی به پایگاه داده
     */
    private fun testDatabaseAccess() {
        try {
            Log.d(TAG, "🔄 تست دسترسی به پایگاه داده...")

            // تست ساده: بررسی تنظیمات
            if (SupabaseClient.isConfigured()) {
                Log.i(TAG, "✅ تنظیمات Supabase موجود است")
            } else {
                Log.w(TAG, "⚠️ تنظیمات Supabase ناقص است")
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ خطای تست: ${e.message}")
        }
    }

    /**
     * تست ورود با کاربر نمونه
     */
    fun testLogin(email: String, password: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "🔄 تست ورود با ایمیل: $email")

                // TODO: پیاده‌سازی تست ورود
                Log.i(TAG, "✅ تست ورود موفق")

            } catch (e: Exception) {
                Log.e(TAG, "❌ خطای ورود: ${e.message}")
            }
        }
    }

    /**
     * نمایش وضعیت کامل Supabase
     */
    fun showStatus() {
        Log.d(TAG, "📊 وضعیت Supabase:")
        Log.d(TAG, "   URL: ${if (BuildConfig.SUPABASE_URL.isNotBlank()) "✅ تنظیم شده" else "❌ خالی"}")
        Log.d(TAG, "   Key: ${if (BuildConfig.SUPABASE_PUBLIC_KEY.isNotBlank()) "✅ تنظیم شده" else "❌ خالی"}")
        Log.d(TAG, "   Client: ${if (SupabaseClient.isConfigured()) "✅ آماده" else "❌ ناآماده"}")
    }
}