package com.example.sharenshop.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.sharenshop.presentation.auth.AuthViewModel
import com.example.sharenshop.ui.screens.*

@Composable
fun NavGraph(
    navController: NavHostController = rememberNavController(),
    authViewModel: AuthViewModel = hiltViewModel()
) {
    // بررسی وضعیت احراز هویت
    val isAuthenticated by authViewModel.isAuthenticated.collectAsState()

    // تعیین صفحه شروع بر اساس وضعیت احراز هویت
    val startDestination = if (isAuthenticated) Screen.Main.route else Screen.Auth.route
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        composable(Screen.Auth.route) {
            AuthScreen(
                onNavigateToMain = {
                    navController.navigate(Screen.Main.route) {
                        popUpTo(Screen.Auth.route) { inclusive = true }
                    }
                },
                onNavigateToPendingApproval = {
                    navController.navigate(Screen.PendingApproval.route) {
                        popUpTo(Screen.Auth.route) { inclusive = true }
                    }
                }
            )
        }
        composable(Screen.Main.route) {
            MainScreen(
                currentUserRole = authViewModel.getCurrentUserRole() ?: com.example.sharenshop.data.model.UserRole.CUSTOMER,
                onNavigateToAuth = {
                    authViewModel.logout()
                    navController.navigate(Screen.Auth.route) {
                        popUpTo(Screen.Main.route) { inclusive = true }
                    }
                }
            )
        }
        composable(Screen.Home.route) {
            HomeScreen()
        }
        composable(Screen.Invoice.route) {
            InvoiceScreen()
        }
        composable(Screen.Product.route) {
            ProductScreen()
        }
        composable(Screen.Seller.route) {
            SellerScreen()
        }
        composable(Screen.Statistics.route) {
            StatisticsScreen()
        }
        composable(Screen.UserManagement.route) {
            UserManagementScreen()
        }
        composable(Screen.Payment.route) {
            com.example.sharenshop.ui.screens.PaymentScreen()
        }
        composable(Screen.Settlement.route) {
            com.example.sharenshop.ui.screens.SettlementScreen()
        }
        composable(Screen.Accounting.route) {
            com.example.sharenshop.ui.screens.AccountingScreen()
        }
        composable(Screen.PendingApproval.route) {
            PendingApprovalScreen(
                onRetryLogin = {
                    navController.navigate(Screen.Auth.route) {
                        popUpTo(Screen.PendingApproval.route) { inclusive = true }
                    }
                },
                onContactSupport = {
                    // TODO: Implement contact support
                }
            )
        }
        // Add more composable destinations here
    }
}