package com.example.sharenshop.data.repository

import com.example.sharenshop.data.local.dao.CustomerDao
// import com.example.sharenshop.data.remote.SupabaseConstants
// import com.example.sharenshop.data.remote.SupabaseManager
import com.example.sharenshop.domain.repository.CustomerRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import com.example.sharenshop.data.model.Customer

class CustomerRepositoryImpl @Inject constructor(
    private val customerDao: CustomerDao,
//    private val supabaseManager: SupabaseManager
) : CustomerRepository {

    override fun getCustomerById(customerId: String): Flow<Customer?> = flow {
        customerDao.getCustomerById(customerId).collect { localCustomer ->
            if (localCustomer != null) {
                emit(localCustomer)
            }
            try {
                // val response = supabaseManager.postgrest
                //     .from(SupabaseConstants.CUSTOMERS_TABLE)
                //     .select()
                //     .eq("id", customerId)
                //     .limit(1)
                //     .single()
                //     .execute()
                // TODO: Deserialize response.data to Customer object
                val remoteCustomer: Customer? = null // Placeholder for deserialized customer

                if (remoteCustomer != null) {
                    customerDao.insertCustomer(remoteCustomer)
                    emit(remoteCustomer)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override suspend fun insertCustomer(customer: Customer) {
        customerDao.insertCustomer(customer)
        try {
            // val supabaseManager = SupabaseManager()
            // supabaseManager.postgrest
            //     .from(SupabaseConstants.CUSTOMERS_TABLE)
            //     .insert(customer)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override suspend fun updateCustomer(customer: Customer) {
        customerDao.updateCustomer(customer)
        try {
            // val supabaseManager = SupabaseManager()
            // supabaseManager.postgrest
            //     .from(SupabaseConstants.CUSTOMERS_TABLE)
            //     .update(customer)
            //     .eq("id", customer.id)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override suspend fun deleteCustomerById(customerId: String) {
        customerDao.deleteCustomerById(customerId)
        try {
            // val supabaseManager = SupabaseManager()
            // supabaseManager.postgrest
            //     .from(SupabaseConstants.CUSTOMERS_TABLE)
            //     .delete()
            //     .eq("id", customerId)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun getAllCustomers(): Flow<List<Customer>> = flow {
        customerDao.getAllCustomers().collect { emit(it) }
    }

    override fun searchCustomers(query: String): Flow<List<Customer>> = flow {
        customerDao.searchCustomers(query).collect { emit(it) }
    }
} 