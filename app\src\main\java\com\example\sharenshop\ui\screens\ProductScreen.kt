package com.example.sharenshop.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.sharenshop.data.model.Product
import com.example.sharenshop.data.model.UserRole
import com.example.sharenshop.presentation.product.ProductViewModel
import com.example.sharenshop.ui.components.StatCard
import com.example.sharenshop.ui.components.formatCurrency
import com.example.sharenshop.ui.utils.*
import java.math.BigDecimal
import java.text.NumberFormat
import java.util.*
import java.util.UUID

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductScreen(
    viewModel: ProductViewModel = hiltViewModel(),
    userRole: UserRole = UserRole.SUPER_ADMIN
) {
    val products by viewModel.products.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    val searchQuery by viewModel.searchQuery.collectAsStateWithLifecycle()
    var showAddDialog by remember { mutableStateOf(false) }
    var selectedProduct by remember { mutableStateOf<Product?>(null) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("مدیریت محصولات") },
                actions = {
                    if (userRole != UserRole.CUSTOMER) {
                        IconButton(onClick = { showAddDialog = true }) {
                            Icon(Icons.Default.Add, contentDescription = "افزودن محصول")
                        }
                    }
                }
            )
        },
        floatingActionButton = {
            if (userRole != UserRole.CUSTOMER) {
                FloatingActionButton(
                    onClick = { showAddDialog = true }
                ) {
                    Icon(Icons.Default.Add, contentDescription = "افزودن محصول")
                }
            }
        }
    ) { paddingValues ->
        val adaptivePadding = getAdaptivePadding()
        val adaptiveSpacing = getAdaptiveSpacing()

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(adaptivePadding)
        ) {
            // جستجو
            SearchBar(
                query = searchQuery,
                onQueryChange = viewModel::updateSearchQuery,
                placeholder = "جستجوی محصولات..."
            )

            Spacer(modifier = Modifier.height(adaptiveSpacing))

            // آمار سریع
            ProductStatsRow(products, userRole)

            Spacer(modifier = Modifier.height(adaptiveSpacing))

            // لیست محصولات
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(products) { product ->
                        ProductCard(
                            product = product,
                            userRole = userRole,
                            onEditClick = { selectedProduct = it },
                            onDeleteClick = { viewModel.deleteProduct(it.id) }
                        )
                    }
                }
            }
        }
    }

    // دیالوگ افزودن/ویرایش محصول
    if (showAddDialog) {
        AddEditProductDialog(
            product = null,
            onDismiss = { showAddDialog = false },
            onSave = { product ->
                viewModel.addProduct(product)
                showAddDialog = false
            }
        )
    }

    selectedProduct?.let { product ->
        AddEditProductDialog(
            product = product,
            onDismiss = { selectedProduct = null },
            onSave = { updatedProduct ->
                viewModel.updateProduct(updatedProduct)
                selectedProduct = null
            }
        )
    }
}

@Composable
fun SearchBar(
    query: String,
    onQueryChange: (String) -> Unit,
    placeholder: String
) {
    OutlinedTextField(
        value = query,
        onValueChange = onQueryChange,
        modifier = Modifier.fillMaxWidth(),
        placeholder = { Text(placeholder) },
        leadingIcon = {
            Icon(Icons.Default.Search, contentDescription = "جستجو")
        },
        trailingIcon = {
            if (query.isNotEmpty()) {
                IconButton(onClick = { onQueryChange("") }) {
                    Icon(Icons.Default.Clear, contentDescription = "پاک کردن")
                }
            }
        },
        singleLine = true
    )
}

@Composable
fun ProductStatsRow(products: List<Product>, userRole: UserRole) {
    val adaptiveSpacing = getAdaptiveSpacing()
    val isLandscapeMode = isLandscape()

    if (isLandscapeMode) {
        // در حالت افقی از Column استفاده کن
        Column(
            verticalArrangement = Arrangement.spacedBy(adaptiveSpacing)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(adaptiveSpacing)
            ) {
                StatCard(
                    title = "کل محصولات",
                    value = products.size.toString(),
                    icon = Icons.Default.Build,
                    color = Color(0xFF4CAF50),
                    modifier = Modifier.weight(1f)
                )

                StatCard(
                    title = "موجودی کم",
                    value = products.count { it.stock < 10 }.toString(),
                    icon = Icons.Default.Warning,
                    color = Color(0xFFFF9800),
                    modifier = Modifier.weight(1f)
                )
            }

            if (userRole == UserRole.SUPER_ADMIN) {
                StatCard(
                    title = "ارزش کل",
                    value = formatCurrency(
                        products.sumOf { it.price.multiply(BigDecimal(it.stock)) }
                    ),
                    icon = Icons.Default.Star,
                    color = Color(0xFF2196F3),
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    } else {
        // در حالت عمودی از Row استفاده کن
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(adaptiveSpacing)
        ) {
            StatCard(
                title = "کل محصولات",
                value = products.size.toString(),
                icon = Icons.Default.Build,
                color = Color(0xFF4CAF50),
                modifier = Modifier.weight(1f)
            )

            StatCard(
                title = "موجودی کم",
                value = products.count { it.stock < 10 }.toString(),
                icon = Icons.Default.Warning,
                color = Color(0xFFFF9800),
                modifier = Modifier.weight(1f)
            )

            if (userRole == UserRole.SUPER_ADMIN) {
                StatCard(
                    title = "ارزش کل",
                    value = formatCurrency(
                        products.sumOf { it.price.multiply(BigDecimal(it.stock)) }
                    ),
                    icon = Icons.Default.Star,
                    color = Color(0xFF2196F3),
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}



@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductCard(
    product: Product,
    userRole: UserRole,
    onEditClick: (Product) -> Unit,
    onDeleteClick: (Product) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = product.name,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    if (!product.description.isNullOrBlank()) {
                        Text(
                            text = product.description,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }

                if (userRole != UserRole.CUSTOMER) {
                    Row {
                        IconButton(onClick = { onEditClick(product) }) {
                            Icon(
                                Icons.Default.Edit,
                                contentDescription = "ویرایش",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }

                        if (userRole == UserRole.SUPER_ADMIN) {
                            IconButton(onClick = { onDeleteClick(product) }) {
                                Icon(
                                    Icons.Default.Delete,
                                    contentDescription = "حذف",
                                    tint = MaterialTheme.colorScheme.error
                                )
                            }
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // قیمت
                Column {
                    Text(
                        text = "قیمت",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                    Text(
                        text = formatCurrency(product.price),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                }

                // موجودی
                Column(horizontalAlignment = Alignment.End) {
                    Text(
                        text = "موجودی",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )

                    val stockColor = when {
                        product.stock == 0 -> MaterialTheme.colorScheme.error
                        product.stock < 10 -> Color(0xFFFF9800)
                        else -> Color(0xFF4CAF50)
                    }

                    Text(
                        text = product.stock.toString(),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = stockColor
                    )
                }
            }

            // نشانگر موجودی کم
            if (product.stock < 10) {
                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Warning,
                        contentDescription = "موجودی کم",
                        tint = Color(0xFFFF9800),
                        modifier = Modifier.size(16.dp)
                    )

                    Spacer(modifier = Modifier.width(4.dp))

                    Text(
                        text = if (product.stock == 0) "ناموجود" else "موجودی کم",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFFFF9800)
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddEditProductDialog(
    product: Product?,
    onDismiss: () -> Unit,
    onSave: (Product) -> Unit
) {
    var name by remember { mutableStateOf(product?.name ?: "") }
    var description by remember { mutableStateOf(product?.description ?: "") }
    var price by remember { mutableStateOf(product?.price?.toString() ?: "") }
    var stock by remember { mutableStateOf(product?.stock?.toString() ?: "") }
    var imageUrl by remember { mutableStateOf(product?.imageUrl ?: "") }

    val isEditing = product != null

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(if (isEditing) "ویرایش محصول" else "افزودن محصول جدید")
        },
        text = {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    OutlinedTextField(
                        value = name,
                        onValueChange = { name = it },
                        label = { Text("نام محصول *") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )
                }

                item {
                    OutlinedTextField(
                        value = description,
                        onValueChange = { description = it },
                        label = { Text("توضیحات") },
                        modifier = Modifier.fillMaxWidth(),
                        maxLines = 3
                    )
                }

                item {
                    OutlinedTextField(
                        value = price,
                        onValueChange = { price = it },
                        label = { Text("قیمت (ریال) *") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        suffix = { Text("ریال") }
                    )
                }

                item {
                    OutlinedTextField(
                        value = stock,
                        onValueChange = { stock = it },
                        label = { Text("موجودی *") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        suffix = { Text("عدد") }
                    )
                }

                item {
                    OutlinedTextField(
                        value = imageUrl,
                        onValueChange = { imageUrl = it },
                        label = { Text("آدرس تصویر") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        placeholder = { Text("https://...") }
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (name.isNotBlank() && price.isNotBlank() && stock.isNotBlank()) {
                        try {
                            val newProduct = Product(
                                id = product?.id ?: UUID.randomUUID().toString(),
                                name = name.trim(),
                                description = description.trim().takeIf { it.isNotBlank() },
                                price = BigDecimal(price),
                                stock = stock.toInt(),
                                imageUrl = imageUrl.trim().takeIf { it.isNotBlank() },
                                createdAt = product?.createdAt ?: System.currentTimeMillis(),
                                updatedAt = System.currentTimeMillis()
                            )
                            onSave(newProduct)
                        } catch (e: Exception) {
                            // TODO: Show error message
                        }
                    }
                }
            ) {
                Text(if (isEditing) "ذخیره" else "افزودن")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("لغو")
            }
        }
    )
}

