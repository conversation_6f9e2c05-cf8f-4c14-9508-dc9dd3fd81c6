// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.CustomerApprovalRepository;
import com.example.sharenshop.domain.repository.NotificationRepository;
import com.example.sharenshop.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SubmitApprovalRequest_Factory implements Factory<SubmitApprovalRequest> {
  private final Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  private final Provider<NotificationRepository> notificationRepositoryProvider;

  public SubmitApprovalRequest_Factory(
      Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider,
      Provider<NotificationRepository> notificationRepositoryProvider) {
    this.customerApprovalRepositoryProvider = customerApprovalRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
    this.notificationRepositoryProvider = notificationRepositoryProvider;
  }

  @Override
  public SubmitApprovalRequest get() {
    return newInstance(customerApprovalRepositoryProvider.get(), userRepositoryProvider.get(), notificationRepositoryProvider.get());
  }

  public static SubmitApprovalRequest_Factory create(
      Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider,
      Provider<NotificationRepository> notificationRepositoryProvider) {
    return new SubmitApprovalRequest_Factory(customerApprovalRepositoryProvider, userRepositoryProvider, notificationRepositoryProvider);
  }

  public static SubmitApprovalRequest newInstance(
      CustomerApprovalRepository customerApprovalRepository, UserRepository userRepository,
      NotificationRepository notificationRepository) {
    return new SubmitApprovalRequest(customerApprovalRepository, userRepository, notificationRepository);
  }
}
