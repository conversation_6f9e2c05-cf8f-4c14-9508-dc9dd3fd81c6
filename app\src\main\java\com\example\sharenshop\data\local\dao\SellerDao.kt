package com.example.sharenshop.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.sharenshop.data.model.Seller
import kotlinx.coroutines.flow.Flow

@Dao
interface SellerDao {
    @Query("SELECT * FROM sellers WHERE id = :sellerId")
    fun getSellerById(sellerId: String): Flow<Seller?>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSeller(seller: Seller)

    @Update
    suspend fun updateSeller(seller: Seller)

    @Query("DELETE FROM sellers WHERE id = :sellerId")
    suspend fun deleteSellerById(sellerId: String)

    @Query("SELECT * FROM sellers")
    fun getAllSellers(): Flow<List<Seller>>

    @Query("SELECT * FROM sellers WHERE name LIKE '%' || :query || '%' OR contactPhone LIKE '%' || :query || '%' OR contactEmail LIKE '%' || :query || '%'")
    fun searchSellers(query: String): Flow<List<Seller>>
} 