package com.example.sharenshop.presentation.invoice

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharenshop.data.model.Invoice
import com.example.sharenshop.domain.use_case.InvoiceUseCases
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class InvoiceViewModel @Inject constructor(
    private val invoiceUseCases: InvoiceUseCases
) : ViewModel() {

    private val _invoices = MutableStateFlow<List<Invoice>>(emptyList())
    val invoices: StateFlow<List<Invoice>> = _invoices.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    init {
        loadInvoices()
    }

    fun loadInvoices() {
        _isLoading.value = true
        invoiceUseCases.getAllInvoices()
            .onEach { invoices ->
                _invoices.value = invoices
            }
            .catch { e ->
                _error.value = e.localizedMessage ?: "Unknown error loading invoices"
            }
            .onEach { _isLoading.value = false }
            .launchIn(viewModelScope)
    }

    fun createInvoice(invoice: Invoice) {
        insertInvoice(invoice)
    }

    fun insertInvoice(invoice: Invoice) {
        viewModelScope.launch {
            try {
                invoiceUseCases.insertInvoice(invoice)
                loadInvoices() // Refresh the list after insertion
            } catch (e: Exception) {
                _error.value = e.localizedMessage ?: "Error inserting invoice"
            }
        }
    }

    fun updateInvoice(invoice: Invoice) {
        viewModelScope.launch {
            try {
                invoiceUseCases.updateInvoice(invoice)
                loadInvoices() // Refresh the list after update
            } catch (e: Exception) {
                _error.value = e.localizedMessage ?: "Error updating invoice"
            }
        }
    }

    fun deleteInvoice(invoice: Invoice) {
        viewModelScope.launch {
            try {
                invoiceUseCases.deleteInvoice(invoice.id)
                loadInvoices() // Refresh the list after deletion
            } catch (e: Exception) {
                _error.value = e.localizedMessage ?: "Error deleting invoice"
            }
        }
    }

    fun getInvoicesByCustomer(customerId: String) {
        viewModelScope.launch {
            _isLoading.value = true
            invoiceUseCases.getInvoicesByCustomer(customerId)
                .onEach { invoices ->
                    _invoices.value = invoices
                }
                .catch { e ->
                    _error.value = e.localizedMessage ?: "Unknown error loading invoices by customer"
                }
                .onEach { _isLoading.value = false }
                .launchIn(viewModelScope)
        }
    }

    fun getInvoicesByUser(userId: String) {
        viewModelScope.launch {
            _isLoading.value = true
            invoiceUseCases.getInvoicesByUser(userId)
                .onEach { invoices ->
                    _invoices.value = invoices
                }
                .catch { e ->
                    _error.value = e.localizedMessage ?: "Unknown error loading invoices by user"
                }
                .onEach { _isLoading.value = false }
                .launchIn(viewModelScope)
        }
    }
} 