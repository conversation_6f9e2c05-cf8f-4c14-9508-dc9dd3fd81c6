{"logs": [{"outputFile": "com.example.sharenshop.app-mergeDebugResources-2:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88c123eabfc6d4b207c77cc8294f818f\\transformed\\material3-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,183,305,418,543,645,747,866,1002,1123,1269,1354,1455,1546,1644,1756,1878,1984,2123,2260,2390,2549,2674,2789,2907,3023,3115,3214,3331,3463,3568,3673,3779,3917,4060,4170,4271,4347,4450,4550,4638,4727,4832,4912,4996,5096,5196,5293,5391,5479,5583,5683,5785,5903,5983,6092", "endColumns": "127,121,112,124,101,101,118,135,120,145,84,100,90,97,111,121,105,138,136,129,158,124,114,117,115,91,98,116,131,104,104,105,137,142,109,100,75,102,99,87,88,104,79,83,99,99,96,97,87,103,99,101,117,79,108,97", "endOffsets": "178,300,413,538,640,742,861,997,1118,1264,1349,1450,1541,1639,1751,1873,1979,2118,2255,2385,2544,2669,2784,2902,3018,3110,3209,3326,3458,3563,3668,3774,3912,4055,4165,4266,4342,4445,4545,4633,4722,4827,4907,4991,5091,5191,5288,5386,5474,5578,5678,5780,5898,5978,6087,6185"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1889,2017,2139,2252,2377,2479,2581,2700,2836,2957,3103,3188,3289,3380,3478,3590,3712,3818,3957,4094,4224,4383,4508,4623,4741,4857,4949,5048,5165,5297,5402,5507,5613,5751,5894,6004,6105,6181,6284,6384,6472,6561,6666,6746,6830,6930,7030,7127,7225,7313,7417,7517,7619,7737,7817,7926", "endColumns": "127,121,112,124,101,101,118,135,120,145,84,100,90,97,111,121,105,138,136,129,158,124,114,117,115,91,98,116,131,104,104,105,137,142,109,100,75,102,99,87,88,104,79,83,99,99,96,97,87,103,99,101,117,79,108,97", "endOffsets": "2012,2134,2247,2372,2474,2576,2695,2831,2952,3098,3183,3284,3375,3473,3585,3707,3813,3952,4089,4219,4378,4503,4618,4736,4852,4944,5043,5160,5292,5397,5502,5608,5746,5889,5999,6100,6176,6279,6379,6467,6556,6661,6741,6825,6925,7025,7122,7220,7308,7412,7512,7614,7732,7812,7921,8019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\beb880474af4802d615c3712a7fe5cf4\\transformed\\browser-1.8.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,255,363", "endColumns": "95,103,107,102", "endOffsets": "146,250,358,461"}, "to": {"startLines": "11,15,16,17", "startColumns": "4,4,4,4", "startOffsets": "1020,1405,1509,1617", "endColumns": "95,103,107,102", "endOffsets": "1111,1504,1612,1715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\61acd70f2f91261ed7f816e4fc4161c1\\transformed\\ui-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,293,390,491,582,663,751,843,925,994,1061,1142,1231,1303,1384,1450", "endColumns": "99,87,96,100,90,80,87,91,81,68,66,80,88,71,80,65,116", "endOffsets": "200,288,385,486,577,658,746,838,920,989,1056,1137,1226,1298,1379,1445,1562"}, "to": {"startLines": "9,10,12,13,14,18,19,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,932,1116,1213,1314,1720,1801,8024,8116,8198,8267,8334,8415,8504,8677,8758,8824", "endColumns": "99,87,96,100,90,80,87,91,81,68,66,80,88,71,80,65,116", "endOffsets": "927,1015,1208,1309,1400,1796,1884,8111,8193,8262,8329,8410,8499,8571,8753,8819,8936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8bf4b3fb7fecc46740c95cb34de6afa1\\transformed\\core-1.12.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,206,308,411,515,616,721,8576", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "201,303,406,510,611,716,827,8672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88b5b0acc1ef090d93ba86ccb1fae5cf\\transformed\\foundation-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,93", "endOffsets": "139,233"}, "to": {"startLines": "87,88", "startColumns": "4,4", "startOffsets": "8941,9030", "endColumns": "88,93", "endOffsets": "9025,9119"}}]}]}