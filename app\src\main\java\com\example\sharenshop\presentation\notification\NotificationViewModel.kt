package com.example.sharenshop.presentation.notification

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharenshop.data.model.Notification
import com.example.sharenshop.data.model.NotificationType
import com.example.sharenshop.data.model.UserRole
import com.example.sharenshop.domain.repository.NotificationRepository
import com.example.sharenshop.domain.repository.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

@HiltViewModel
class NotificationViewModel @Inject constructor(
    private val notificationRepository: NotificationRepository,
    private val userRepository: UserRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(NotificationUiState())
    val uiState: StateFlow<NotificationUiState> = _uiState.asStateFlow()

    private val _currentUserId = MutableStateFlow<String?>(null)
    val currentUserId: StateFlow<String?> = _currentUserId.asStateFlow()

    fun setCurrentUser(userId: String) {
        _currentUserId.value = userId
        loadNotifications(userId)
        loadUnreadCount(userId)
    }

    private fun loadNotifications(userId: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                notificationRepository.getNotificationsByUser(userId)
                    .catch { e ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = e.localizedMessage ?: "خطا در بارگذاری پیام‌ها"
                        )
                    }
                    .collect { notifications ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            notifications = notifications,
                            error = null
                        )
                    }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.localizedMessage ?: "خطا در بارگذاری پیام‌ها"
                )
            }
        }
    }

    private fun loadUnreadCount(userId: String) {
        viewModelScope.launch {
            try {
                val count = notificationRepository.getUnreadNotificationsCount(userId)
                _uiState.value = _uiState.value.copy(unreadCount = count)
            } catch (e: Exception) {
                // خطا در بارگذاری تعداد پیام‌های خوانده نشده
            }
        }
    }

    fun getNotificationsByType(type: NotificationType): List<Notification> {
        return _uiState.value.notifications.filter { it.type == type }
    }

    fun markAsRead(notificationId: String) {
        viewModelScope.launch {
            try {
                notificationRepository.markAsRead(notificationId)
                // بروزرسانی لیست محلی
                val updatedNotifications = _uiState.value.notifications.map { notification ->
                    if (notification.id == notificationId) {
                        notification.copy(isRead = true)
                    } else {
                        notification
                    }
                }
                _uiState.value = _uiState.value.copy(notifications = updatedNotifications)
                
                // بروزرسانی تعداد خوانده نشده
                _currentUserId.value?.let { loadUnreadCount(it) }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "خطا در علامت‌گذاری پیام"
                )
            }
        }
    }

    fun markAllAsRead() {
        viewModelScope.launch {
            _currentUserId.value?.let { userId ->
                try {
                    notificationRepository.markAllAsReadForUser(userId)
                    // بروزرسانی لیست محلی
                    val updatedNotifications = _uiState.value.notifications.map { 
                        it.copy(isRead = true) 
                    }
                    _uiState.value = _uiState.value.copy(
                        notifications = updatedNotifications,
                        unreadCount = 0
                    )
                } catch (e: Exception) {
                    _uiState.value = _uiState.value.copy(
                        error = "خطا در علامت‌گذاری همه پیام‌ها"
                    )
                }
            }
        }
    }

    fun sendNotification(
        receiverUserId: String,
        type: NotificationType,
        title: String,
        message: String,
        data: String? = null
    ) {
        viewModelScope.launch {
            try {
                val notification = Notification(
                    id = UUID.randomUUID().toString(),
                    userId = receiverUserId,
                    type = type,
                    title = title,
                    message = message,
                    data = data,
                    isRead = false,
                    createdAt = System.currentTimeMillis()
                )
                
                notificationRepository.insertNotification(notification)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "خطا در ارسال پیام"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun deleteNotification(notification: Notification) {
        viewModelScope.launch {
            try {
                notificationRepository.deleteNotification(notification)
                val updatedNotifications = _uiState.value.notifications.filter { 
                    it.id != notification.id 
                }
                _uiState.value = _uiState.value.copy(notifications = updatedNotifications)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "خطا در حذف پیام"
                )
            }
        }
    }
}

data class NotificationUiState(
    val isLoading: Boolean = false,
    val notifications: List<Notification> = emptyList(),
    val unreadCount: Int = 0,
    val error: String? = null
)

// Helper functions برای تولید پیام‌های مختلف
object NotificationHelper {
    
    fun createCustomerApprovalRequest(
        customerName: String,
        customerEmail: String,
        referrerUserId: String
    ): Notification {
        return Notification(
            id = UUID.randomUUID().toString(),
            userId = referrerUserId,
            type = NotificationType.CUSTOMER_APPROVAL_REQUEST,
            title = "درخواست تایید مشتری جدید",
            message = "مشتری $customerName با ایمیل $customerEmail درخواست تایید کرده است",
            data = customerEmail,
            isRead = false,
            createdAt = System.currentTimeMillis()
        )
    }
    
    fun createCustomerApproved(
        customerName: String,
        customerUserId: String
    ): Notification {
        return Notification(
            id = UUID.randomUUID().toString(),
            userId = customerUserId,
            type = NotificationType.CUSTOMER_APPROVED,
            title = "حساب شما تایید شد",
            message = "تبریک! حساب کاربری شما با موفقیت تایید شد و می‌توانید از خدمات استفاده کنید",
            data = null,
            isRead = false,
            createdAt = System.currentTimeMillis()
        )
    }
    
    fun createPaymentNotification(
        amount: String,
        invoiceId: String,
        receiverUserId: String
    ): Notification {
        return Notification(
            id = UUID.randomUUID().toString(),
            userId = receiverUserId,
            type = NotificationType.SYSTEM_MESSAGE,
            title = "پرداخت جدید",
            message = "پرداخت $amount تومان برای فاکتور $invoiceId دریافت شد",
            data = invoiceId,
            isRead = false,
            createdAt = System.currentTimeMillis()
        )
    }
}
