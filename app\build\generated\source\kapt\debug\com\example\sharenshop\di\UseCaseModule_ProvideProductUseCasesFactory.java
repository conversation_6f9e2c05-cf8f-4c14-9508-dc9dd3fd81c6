// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.domain.repository.ProductRepository;
import com.example.sharenshop.domain.use_case.ProductUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideProductUseCasesFactory implements Factory<ProductUseCases> {
  private final Provider<ProductRepository> repositoryProvider;

  public UseCaseModule_ProvideProductUseCasesFactory(
      Provider<ProductRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public ProductUseCases get() {
    return provideProductUseCases(repositoryProvider.get());
  }

  public static UseCaseModule_ProvideProductUseCasesFactory create(
      Provider<ProductRepository> repositoryProvider) {
    return new UseCaseModule_ProvideProductUseCasesFactory(repositoryProvider);
  }

  public static ProductUseCases provideProductUseCases(ProductRepository repository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideProductUseCases(repository));
  }
}
