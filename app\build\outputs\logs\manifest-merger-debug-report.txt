-- Merging decision tree log ---
manifest
ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:2:1-31:12
INJECTED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:2:1-31:12
INJECTED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:2:1-31:12
INJECTED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:2:1-31:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a51658cbd4331bc973187dd84e4375f3\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b9c0e2dd747a5eaeb94ddd766ded1e5\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb5659120cd5b84eb209142f16865734\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\859f969b136d5ecad99a6025b175074b\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\d11c1f8ebd3bf0f19f2442da2e2cc203\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\08462122e8724fa94c0f6cab13d462ca\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\db44767170acab7c1cc405a7ee739c77\transformed\navigation-compose-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea8989101d9e3e34f6587265602ab618\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\88c123eabfc6d4b207c77cc8294f818f\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\407af4572c3b9391ef279572526756f2\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9137815684fd370958151756a35a2d65\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b4c6ce8bf42d40336ea811b38e5b7114\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae7fbea303a01badff3c3186e14dbc9f\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\88b5b0acc1ef090d93ba86ccb1fae5cf\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\721308b852862b787124cc6167c6573c\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\021458cff0588b86cac4a22c4bcc53c7\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f0d112e19342641a7a3e323f9dad3f1\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\00718ffe15e1660b73f2eab7e2770898\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\93baad69a6ab3646116efeb2564be62b\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e4fa5ab42215896eebe5050f731c513\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\20741aa415142376b396804bc37b66d4\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0876ed1ef2e0972e3dfc3894f35e3b16\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4d24b05f88563a445ee545c800018ea\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b60378e74e7f814f0632b5472797c6e6\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:functions-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6368567665478923c9cd638b185a31b\transformed\functions-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c88a859ab00e695fb07f7317e024a158\transformed\postgrest-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:2:1-20:12
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed26a02cc970d741cda9fadc1fbb2806\transformed\supabase-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376240fbcd90d22d84174e96a1cb38d\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.12\transforms\279c28a34c1142bfa9a0948e95ad5c28\transformed\hilt-android-2.49\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\872be39328562689a0cb04e5ae73da72\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77b826946bb21d692506a62a6dd7d20e\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a516a17367ca1e61b2b392a70ae9e1af\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a7ef59679f6cb291960d80ac8238d7d\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc4f7d79ecbd4e18ddb1ae1f09abdcb7\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f3cdab62cfcc338805420ad3e7c8126\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\482266ccf1f033fbba6d55ec433720e5\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4535643242f3cc62559757ae235a09e\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\84bc0d47a5107c1019597179d5215680\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a1a0651a6f7e40fd9a97078d1359fac\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\79c4df97b2dea687da7d5ff88faae00d\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba289e3c4508e5b804b39e13f451009b\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\46e889c8dec1aaf8a4b4a1f3f1be347f\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\5c9ebac011c455ede73668697ae3f70a\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abe55d1e7e0cce7ec0aad498f97406f2\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac561be4cad084f4e05a3cce70841ded\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61acd70f2f91261ed7f816e4fc4161c1\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b1c9864de71495f04abaf0a87c4145f\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\058d441810fd34f0246fdef2e8a1c5d4\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\07904e3b96b1871d1573b4ff52afdf7d\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c2d3f1d7b86d44aa696673bb92778ba\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\beb880474af4802d615c3712a7fe5cf4\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f89f849fa82c9916cc2908c6c8847f9e\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd185e56c1d31ab234d4390114907f74\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b4a2163848cd7c6eb1b4d89b537b4df1\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a9150cd5bde53e8b2c71411643e96c\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab1ead17130749a790fd159a99372a4c\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ded39a8ee1926897eddd55e5b96d4b43\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f80c9922d2074803f180cadd3dd10ed\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\76c96b4f8cf355beb60f301cf0df3b92\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8a2d672761d6c6b5443dd1bebad62ffa\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\121fce85451ade97b06c9b4cdbf64bdb\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ec73c4de3870badaa1f7d121fdfd740\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae15fde4828ec26275e7434f4bdc02b5\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e9367cc0e15ffdd165722af3a4479d84\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\1017fb3268f8fcc656f97e6927277a68\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\fea53b4ef81919d37e07ae6123e6325b\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd68e0396aa6771b95db1eb7c16d35d0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:17:1-35:12
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8f956037cad53b33b3f87c0e5f41674\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d5913c276c0a9f955d88f7a1992264c\transformed\krypto-debug\AndroidManifest.xml:2:1-9:12
MERGED from [co.touchlab:kermit-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\40908fc9bb975e8912ae66f7306aa7bf\transformed\kermit-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f65fd8b087efeddee5cbd9cf67ce5357\transformed\multiplatform-settings-debug\AndroidManifest.xml:2:1-7:12
MERGED from [co.touchlab:kermit-core-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\68748b4a3845fc0c2aad20e2e03910ee\transformed\kermit-core-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2de81474b5804d098345c2b2f29f7d59\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c980176cb44fb028b42fd4b75e1700a0\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3798808f819c35dff1b7afc99e522a63\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d5b3bcc6e9da875439380d5ef340d2e\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.12\transforms\d7e66152f0833fb48fb195ebc2e1c70d\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:6:5-79
	android:name
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:6:22-76
application
ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:8:5-29:19
INJECTED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:8:5-29:19
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376240fbcd90d22d84174e96a1cb38d\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376240fbcd90d22d84174e96a1cb38d\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abe55d1e7e0cce7ec0aad498f97406f2\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abe55d1e7e0cce7ec0aad498f97406f2\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b1c9864de71495f04abaf0a87c4145f\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b1c9864de71495f04abaf0a87c4145f\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\058d441810fd34f0246fdef2e8a1c5d4\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\058d441810fd34f0246fdef2e8a1c5d4\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab1ead17130749a790fd159a99372a4c\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab1ead17130749a790fd159a99372a4c\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e9367cc0e15ffdd165722af3a4479d84\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e9367cc0e15ffdd165722af3a4479d84\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd68e0396aa6771b95db1eb7c16d35d0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd68e0396aa6771b95db1eb7c16d35d0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3798808f819c35dff1b7afc99e522a63\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3798808f819c35dff1b7afc99e522a63\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:16:9-35
	android:label
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:14:9-41
	android:fullBackupContent
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:12:9-54
	android:roundIcon
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:15:9-54
	tools:targetApi
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:18:9-29
	android:icon
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:13:9-43
	android:allowBackup
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:10:9-35
	android:theme
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:17:9-48
	android:dataExtractionRules
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:11:9-65
	android:name
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:9:9-46
activity#com.example.sharenshop.MainActivity
ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:19:9-28:20
	android:exported
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:21:13-36
	android:theme
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:22:13-52
	android:name
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:20:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:23:13-27:29
action#android.intent.action.MAIN
ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:24:17-69
	android:name
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:24:25-66
category#android.intent.category.LAUNCHER
ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:26:17-77
	android:name
		ADDED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:26:27-74
uses-sdk
INJECTED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml
INJECTED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a51658cbd4331bc973187dd84e4375f3\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a51658cbd4331bc973187dd84e4375f3\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b9c0e2dd747a5eaeb94ddd766ded1e5\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b9c0e2dd747a5eaeb94ddd766ded1e5\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb5659120cd5b84eb209142f16865734\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb5659120cd5b84eb209142f16865734\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\859f969b136d5ecad99a6025b175074b\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\859f969b136d5ecad99a6025b175074b\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\d11c1f8ebd3bf0f19f2442da2e2cc203\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\d11c1f8ebd3bf0f19f2442da2e2cc203\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\08462122e8724fa94c0f6cab13d462ca\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\08462122e8724fa94c0f6cab13d462ca\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\db44767170acab7c1cc405a7ee739c77\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\db44767170acab7c1cc405a7ee739c77\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea8989101d9e3e34f6587265602ab618\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea8989101d9e3e34f6587265602ab618\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\88c123eabfc6d4b207c77cc8294f818f\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\88c123eabfc6d4b207c77cc8294f818f\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\407af4572c3b9391ef279572526756f2\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\407af4572c3b9391ef279572526756f2\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9137815684fd370958151756a35a2d65\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9137815684fd370958151756a35a2d65\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b4c6ce8bf42d40336ea811b38e5b7114\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b4c6ce8bf42d40336ea811b38e5b7114\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae7fbea303a01badff3c3186e14dbc9f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae7fbea303a01badff3c3186e14dbc9f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\88b5b0acc1ef090d93ba86ccb1fae5cf\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\88b5b0acc1ef090d93ba86ccb1fae5cf\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\721308b852862b787124cc6167c6573c\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\721308b852862b787124cc6167c6573c\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\021458cff0588b86cac4a22c4bcc53c7\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\021458cff0588b86cac4a22c4bcc53c7\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f0d112e19342641a7a3e323f9dad3f1\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f0d112e19342641a7a3e323f9dad3f1\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\00718ffe15e1660b73f2eab7e2770898\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\00718ffe15e1660b73f2eab7e2770898\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\93baad69a6ab3646116efeb2564be62b\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\93baad69a6ab3646116efeb2564be62b\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e4fa5ab42215896eebe5050f731c513\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e4fa5ab42215896eebe5050f731c513\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\20741aa415142376b396804bc37b66d4\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\20741aa415142376b396804bc37b66d4\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0876ed1ef2e0972e3dfc3894f35e3b16\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0876ed1ef2e0972e3dfc3894f35e3b16\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4d24b05f88563a445ee545c800018ea\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4d24b05f88563a445ee545c800018ea\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b60378e74e7f814f0632b5472797c6e6\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b60378e74e7f814f0632b5472797c6e6\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:functions-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6368567665478923c9cd638b185a31b\transformed\functions-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:functions-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6368567665478923c9cd638b185a31b\transformed\functions-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c88a859ab00e695fb07f7317e024a158\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c88a859ab00e695fb07f7317e024a158\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed26a02cc970d741cda9fadc1fbb2806\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed26a02cc970d741cda9fadc1fbb2806\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376240fbcd90d22d84174e96a1cb38d\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376240fbcd90d22d84174e96a1cb38d\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.12\transforms\279c28a34c1142bfa9a0948e95ad5c28\transformed\hilt-android-2.49\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.12\transforms\279c28a34c1142bfa9a0948e95ad5c28\transformed\hilt-android-2.49\AndroidManifest.xml:18:3-42
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\872be39328562689a0cb04e5ae73da72\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\872be39328562689a0cb04e5ae73da72\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77b826946bb21d692506a62a6dd7d20e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77b826946bb21d692506a62a6dd7d20e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a516a17367ca1e61b2b392a70ae9e1af\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a516a17367ca1e61b2b392a70ae9e1af\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a7ef59679f6cb291960d80ac8238d7d\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a7ef59679f6cb291960d80ac8238d7d\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc4f7d79ecbd4e18ddb1ae1f09abdcb7\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc4f7d79ecbd4e18ddb1ae1f09abdcb7\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f3cdab62cfcc338805420ad3e7c8126\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f3cdab62cfcc338805420ad3e7c8126\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\482266ccf1f033fbba6d55ec433720e5\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\482266ccf1f033fbba6d55ec433720e5\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4535643242f3cc62559757ae235a09e\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4535643242f3cc62559757ae235a09e\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\84bc0d47a5107c1019597179d5215680\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\84bc0d47a5107c1019597179d5215680\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a1a0651a6f7e40fd9a97078d1359fac\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a1a0651a6f7e40fd9a97078d1359fac\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\79c4df97b2dea687da7d5ff88faae00d\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\79c4df97b2dea687da7d5ff88faae00d\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba289e3c4508e5b804b39e13f451009b\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba289e3c4508e5b804b39e13f451009b\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\46e889c8dec1aaf8a4b4a1f3f1be347f\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\46e889c8dec1aaf8a4b4a1f3f1be347f\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\5c9ebac011c455ede73668697ae3f70a\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\5c9ebac011c455ede73668697ae3f70a\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abe55d1e7e0cce7ec0aad498f97406f2\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abe55d1e7e0cce7ec0aad498f97406f2\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac561be4cad084f4e05a3cce70841ded\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac561be4cad084f4e05a3cce70841ded\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61acd70f2f91261ed7f816e4fc4161c1\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61acd70f2f91261ed7f816e4fc4161c1\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b1c9864de71495f04abaf0a87c4145f\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b1c9864de71495f04abaf0a87c4145f\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\058d441810fd34f0246fdef2e8a1c5d4\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\058d441810fd34f0246fdef2e8a1c5d4\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\07904e3b96b1871d1573b4ff52afdf7d\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\07904e3b96b1871d1573b4ff52afdf7d\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c2d3f1d7b86d44aa696673bb92778ba\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c2d3f1d7b86d44aa696673bb92778ba\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\beb880474af4802d615c3712a7fe5cf4\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\beb880474af4802d615c3712a7fe5cf4\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f89f849fa82c9916cc2908c6c8847f9e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f89f849fa82c9916cc2908c6c8847f9e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd185e56c1d31ab234d4390114907f74\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd185e56c1d31ab234d4390114907f74\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b4a2163848cd7c6eb1b4d89b537b4df1\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b4a2163848cd7c6eb1b4d89b537b4df1\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a9150cd5bde53e8b2c71411643e96c\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a9150cd5bde53e8b2c71411643e96c\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab1ead17130749a790fd159a99372a4c\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab1ead17130749a790fd159a99372a4c\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ded39a8ee1926897eddd55e5b96d4b43\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ded39a8ee1926897eddd55e5b96d4b43\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f80c9922d2074803f180cadd3dd10ed\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f80c9922d2074803f180cadd3dd10ed\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\76c96b4f8cf355beb60f301cf0df3b92\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\76c96b4f8cf355beb60f301cf0df3b92\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8a2d672761d6c6b5443dd1bebad62ffa\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8a2d672761d6c6b5443dd1bebad62ffa\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\121fce85451ade97b06c9b4cdbf64bdb\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\121fce85451ade97b06c9b4cdbf64bdb\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ec73c4de3870badaa1f7d121fdfd740\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ec73c4de3870badaa1f7d121fdfd740\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae15fde4828ec26275e7434f4bdc02b5\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae15fde4828ec26275e7434f4bdc02b5\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e9367cc0e15ffdd165722af3a4479d84\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e9367cc0e15ffdd165722af3a4479d84\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\1017fb3268f8fcc656f97e6927277a68\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\1017fb3268f8fcc656f97e6927277a68\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\fea53b4ef81919d37e07ae6123e6325b\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\fea53b4ef81919d37e07ae6123e6325b\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd68e0396aa6771b95db1eb7c16d35d0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd68e0396aa6771b95db1eb7c16d35d0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8f956037cad53b33b3f87c0e5f41674\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8f956037cad53b33b3f87c0e5f41674\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d5913c276c0a9f955d88f7a1992264c\transformed\krypto-debug\AndroidManifest.xml:5:5-7:41
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d5913c276c0a9f955d88f7a1992264c\transformed\krypto-debug\AndroidManifest.xml:5:5-7:41
MERGED from [co.touchlab:kermit-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\40908fc9bb975e8912ae66f7306aa7bf\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\40908fc9bb975e8912ae66f7306aa7bf\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f65fd8b087efeddee5cbd9cf67ce5357\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f65fd8b087efeddee5cbd9cf67ce5357\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-core-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\68748b4a3845fc0c2aad20e2e03910ee\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-core-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\68748b4a3845fc0c2aad20e2e03910ee\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2de81474b5804d098345c2b2f29f7d59\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2de81474b5804d098345c2b2f29f7d59\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c980176cb44fb028b42fd4b75e1700a0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c980176cb44fb028b42fd4b75e1700a0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3798808f819c35dff1b7afc99e522a63\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3798808f819c35dff1b7afc99e522a63\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d5b3bcc6e9da875439380d5ef340d2e\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d5b3bcc6e9da875439380d5ef340d2e\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.12\transforms\d7e66152f0833fb48fb195ebc2e1c70d\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.12\transforms\d7e66152f0833fb48fb195ebc2e1c70d\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376240fbcd90d22d84174e96a1cb38d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376240fbcd90d22d84174e96a1cb38d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abe55d1e7e0cce7ec0aad498f97406f2\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abe55d1e7e0cce7ec0aad498f97406f2\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd68e0396aa6771b95db1eb7c16d35d0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd68e0396aa6771b95db1eb7c16d35d0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3798808f819c35dff1b7afc99e522a63\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3798808f819c35dff1b7afc99e522a63\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:13:13-31
	android:authorities
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:11:13-68
	android:exported
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:10:13-67
meta-data#io.github.jan.supabase.gotrue.SupabaseInitializer
ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:14:13-16:52
	android:value
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:16:17-49
	android:name
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:15:17-81
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376240fbcd90d22d84174e96a1cb38d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376240fbcd90d22d84174e96a1cb38d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376240fbcd90d22d84174e96a1cb38d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abe55d1e7e0cce7ec0aad498f97406f2\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abe55d1e7e0cce7ec0aad498f97406f2\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abe55d1e7e0cce7ec0aad498f97406f2\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b1c9864de71495f04abaf0a87c4145f\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b1c9864de71495f04abaf0a87c4145f\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b1c9864de71495f04abaf0a87c4145f\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\058d441810fd34f0246fdef2e8a1c5d4\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\058d441810fd34f0246fdef2e8a1c5d4\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\058d441810fd34f0246fdef2e8a1c5d4\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:24:13-63
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.example.sharenshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.sharenshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab1ead17130749a790fd159a99372a4c\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab1ead17130749a790fd159a99372a4c\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab1ead17130749a790fd159a99372a4c\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab1ead17130749a790fd159a99372a4c\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab1ead17130749a790fd159a99372a4c\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#com.russhwolf.settings.SettingsInitializer
ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd68e0396aa6771b95db1eb7c16d35d0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd68e0396aa6771b95db1eb7c16d35d0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd68e0396aa6771b95db1eb7c16d35d0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
