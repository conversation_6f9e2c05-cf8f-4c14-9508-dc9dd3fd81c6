package com.example.sharenshop.domain.use_case

import com.example.sharenshop.data.model.Seller
import com.example.sharenshop.domain.repository.SellerRepository
import kotlinx.coroutines.flow.Flow

// Grouping all seller-related use cases
data class SellerUseCases(
    val getSeller: GetSeller,
    val insertSeller: InsertSeller,
    val updateSeller: UpdateSeller,
    val deleteSeller: DeleteSeller,
    val getAllSellers: GetAllSellers,
    val searchSellers: SearchSellers
)

class GetSeller(private val repository: SellerRepository) {
    operator fun invoke(sellerId: String): Flow<Seller?> {
        return repository.getSellerById(sellerId)
    }
}

class InsertSeller(private val repository: SellerRepository) {
    suspend operator fun invoke(seller: Seller) {
        repository.insertSeller(seller)
    }
}

class UpdateSeller(private val repository: SellerRepository) {
    suspend operator fun invoke(seller: Seller) {
        repository.updateSeller(seller)
    }
}

class DeleteSeller(private val repository: SellerRepository) {
    suspend operator fun invoke(sellerId: String) {
        repository.deleteSellerById(sellerId)
    }
}

class GetAllSellers(private val repository: SellerRepository) {
    operator fun invoke(): Flow<List<Seller>> {
        return repository.getAllSellers()
    }
}

class SearchSellers(private val repository: SellerRepository) {
    operator fun invoke(query: String): Flow<List<Seller>> {
        return repository.searchSellers(query)
    }
} 