package com.example.sharenshop.domain.repository

import com.example.sharenshop.data.model.Notification
import com.example.sharenshop.data.model.NotificationType
import kotlinx.coroutines.flow.Flow

interface NotificationRepository {
    
    suspend fun getNotificationById(id: String): Notification?
    
    fun getNotificationsByUser(userId: String): Flow<List<Notification>>
    
    fun getUnreadNotificationsByUser(userId: String): Flow<List<Notification>>
    
    fun getNotificationsByUserAndType(userId: String, type: NotificationType): Flow<List<Notification>>
    
    suspend fun getUnreadNotificationsCount(userId: String): Int
    
    suspend fun insertNotification(notification: Notification)
    
    suspend fun insertNotifications(notifications: List<Notification>)
    
    suspend fun updateNotification(notification: Notification)
    
    suspend fun deleteNotification(notification: Notification)
    
    suspend fun markAsRead(notificationId: String)
    
    suspend fun markAllAsReadForUser(userId: String)
    
    suspend fun markAsReadByType(userId: String, type: NotificationType)
    
    suspend fun deleteReadNotifications(userId: String)
    
    suspend fun deleteOldNotifications(expireDate: Long)
    
    suspend fun getRecentNotifications(userId: String, limit: Int = 10): List<Notification>
}
