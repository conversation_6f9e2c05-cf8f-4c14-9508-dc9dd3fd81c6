-- 🚀 راه‌اندازی سریع SharenShop در Supabase
-- این اسکریپت را در SQL Editor پنل Supabase اجرا کنید

-- 1. ایجاد جدول کاربران
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    user_type VARCHAR(50) NOT NULL CHECK (user_type IN ('super_admin', 'cashier', 'customer')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. ایجاد جدول محصولات
CREATE TABLE IF NOT EXISTS public.products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(15,2) NOT NULL,
    stock INTEGER NOT NULL DEFAULT 0,
    image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. ایجاد جدول مشتریان
CREATE TABLE IF NOT EXISTS public.customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    balance DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. ایجاد جدول فاکتورها
CREATE TABLE IF NOT EXISTS public.invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID REFERENCES public.customers(id),
    user_id UUID REFERENCES public.users(id),
    invoice_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    total_amount DECIMAL(15,2) NOT NULL,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    final_amount DECIMAL(15,2) NOT NULL,
    payment_status VARCHAR(50) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'overdue')),
    payment_type VARCHAR(50) DEFAULT 'cash' CHECK (payment_type IN ('cash', 'credit')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. ایجاد جدول آیتم‌های فاکتور
CREATE TABLE IF NOT EXISTS public.invoice_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invoice_id UUID REFERENCES public.invoices(id) ON DELETE CASCADE,
    product_id UUID REFERENCES public.products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    total_price DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. ایجاد جدول درخواست‌های تایید مشتری
CREATE TABLE IF NOT EXISTS public.customer_approval_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_email VARCHAR(255) NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20) NOT NULL,
    referrer_code VARCHAR(100) NOT NULL,
    referrer_id UUID REFERENCES public.users(id),
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'expired')),
    request_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    response_date TIMESTAMP WITH TIME ZONE,
    notes TEXT
);

-- 7. ایجاد جدول نوتیفیکیشن‌ها
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id),
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. ایجاد اندیس‌ها
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON public.users(username);
CREATE INDEX IF NOT EXISTS idx_users_user_type ON public.users(user_type);
CREATE INDEX IF NOT EXISTS idx_invoices_customer_id ON public.invoices(customer_id);
CREATE INDEX IF NOT EXISTS idx_invoices_user_id ON public.invoices(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);

-- 9. اضافه کردن کاربران پیش‌فرض
-- مدیر کل
INSERT INTO public.users (email, username, user_type) VALUES 
('<EMAIL>', 'admin', 'super_admin')
ON CONFLICT (email) DO UPDATE SET 
    username = EXCLUDED.username,
    user_type = EXCLUDED.user_type;

-- فروشندگان
INSERT INTO public.users (email, username, user_type) VALUES 
('<EMAIL>', 'ahmad_seller', 'cashier'),
('<EMAIL>', 'ali_seller', 'cashier'),
('<EMAIL>', 'fateme_seller', 'cashier')
ON CONFLICT (email) DO UPDATE SET 
    username = EXCLUDED.username,
    user_type = EXCLUDED.user_type;

-- 10. محصولات نمونه
INSERT INTO public.products (name, description, price, stock) VALUES 
('لپ تاپ ایسوس', 'لپ تاپ ایسوس مدل X515', 15000000, 10),
('گوشی سامسونگ', 'گوشی سامسونگ گلکسی A54', 8000000, 25),
('هدفون بلوتوثی', 'هدفون بی‌سیم سونی', 1500000, 50),
('ماوس گیمینگ', 'ماوس گیمینگ لاجیتک', 800000, 30),
('کیبورد مکانیکی', 'کیبورد مکانیکی ریزر', 2500000, 15)
ON CONFLICT DO NOTHING;

-- 11. مشتریان نمونه
INSERT INTO public.customers (name, phone, email, balance) VALUES 
('محمد حسینی', '09123456789', '<EMAIL>', 500000),
('زهرا احمدی', '09987654321', '<EMAIL>', 750000),
('رضا محمدی', '09111222333', '<EMAIL>', 300000)
ON CONFLICT DO NOTHING;

-- 12. تنظیم RLS (Row Level Security) - ساده
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoice_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_approval_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Policy ساده برای شروع (همه چیز قابل دسترسی)
CREATE POLICY "Allow all for authenticated users" ON public.users FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Allow all for authenticated users" ON public.products FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Allow all for authenticated users" ON public.customers FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Allow all for authenticated users" ON public.invoices FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Allow all for authenticated users" ON public.invoice_items FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Allow all for authenticated users" ON public.customer_approval_requests FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Allow all for authenticated users" ON public.notifications FOR ALL USING (auth.role() = 'authenticated');

-- پیام موفقیت
SELECT 'SharenShop database setup completed successfully! 🎉' as message;
