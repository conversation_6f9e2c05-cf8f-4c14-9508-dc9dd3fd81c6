package com.example.sharenshop.data.remote

import com.example.sharenshop.BuildConfig

object SupabaseConstants {
    val SUPABASE_URL = BuildConfig.SUPABASE_URL
    val SUPABASE_PUBLIC_KEY = BuildConfig.SUPABASE_PUBLIC_KEY

    // Tables - Updated for new database structure
    const val USERS_TABLE = "users"
    const val PRODUCTS_TABLE = "products"
    const val CUSTOMERS_TABLE = "customers"
    const val SELLERS_TABLE = "sellers"
    const val INVOICES_TABLE = "invoices"
    const val INVOICE_ITEMS_TABLE = "invoice_items"
    const val PAYMENTS_TABLE = "payments"
    const val NOTIFICATIONS_TABLE = "notifications"
    const val SETTLEMENTS_TABLE = "settlements"
    const val SETTLEMENT_ITEMS_TABLE = "settlement_items"
    const val SETTINGS_TABLE = "settings"

    // Other Supabase related constants
    // val ANONYMOUS_KEY = "your_anonymous_key_if_any"
}