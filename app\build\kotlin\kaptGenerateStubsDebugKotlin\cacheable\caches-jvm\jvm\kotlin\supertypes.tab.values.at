/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity android.app.Application# "androidx.datastore.core.Serializer androidx.room.RoomDatabase3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer< ;com.example.sharenshop.domain.repository.CustomerRepository; :com.example.sharenshop.domain.repository.InvoiceRepository; :com.example.sharenshop.domain.repository.ProductRepository< ;com.example.sharenshop.domain.repository.SecurityRepository: 9com.example.sharenshop.domain.repository.SellerRepository< ;com.example.sharenshop.domain.repository.SettingsRepository> =com.example.sharenshop.domain.repository.StatisticsRepository8 7com.example.sharenshop.domain.repository.UserRepository7 6com.example.sharenshop.data.security.EncryptionService7 6com.example.sharenshop.data.security.EncryptionService7 6com.example.sharenshop.data.security.EncryptionService androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel, +com.example.sharenshop.ui.navigation.Screen, +com.example.sharenshop.ui.navigation.Screen, +com.example.sharenshop.ui.navigation.Screen, +com.example.sharenshop.ui.navigation.Screen, +com.example.sharenshop.ui.navigation.Screen, +com.example.sharenshop.ui.navigation.Screen, +com.example.sharenshop.ui.navigation.Screen, +com.example.sharenshop.ui.navigation.Screen, +com.example.sharenshop.ui.navigation.Screen, +com.example.sharenshop.ui.navigation.Screen, +com.example.sharenshop.ui.navigation.Screen, +com.example.sharenshop.ui.navigation.Screen androidx.room.RoomDatabase@ ?com.example.sharenshop.domain.repository.NotificationRepository androidx.lifecycle.ViewModel