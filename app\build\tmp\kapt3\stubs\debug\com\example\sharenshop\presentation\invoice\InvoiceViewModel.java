package com.example.sharenshop.presentation.invoice;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\nJ\u000e\u0010\u0017\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\nJ\u000e\u0010\u0018\u001a\u00020\u00152\u0006\u0010\u0019\u001a\u00020\u0007J\u000e\u0010\u001a\u001a\u00020\u00152\u0006\u0010\u001b\u001a\u00020\u0007J\u000e\u0010\u001c\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\nJ\u0006\u0010\u001d\u001a\u00020\u0015J\u000e\u0010\u001e\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\nR\u0016\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\r\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\f0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010\u00a8\u0006\u001f"}, d2 = {"Lcom/example/sharenshop/presentation/invoice/InvoiceViewModel;", "Landroidx/lifecycle/ViewModel;", "invoiceUseCases", "Lcom/example/sharenshop/domain/use_case/InvoiceUseCases;", "(Lcom/example/sharenshop/domain/use_case/InvoiceUseCases;)V", "_error", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_invoices", "", "Lcom/example/sharenshop/data/model/Invoice;", "_isLoading", "", "error", "Lkotlinx/coroutines/flow/StateFlow;", "getError", "()Lkotlinx/coroutines/flow/StateFlow;", "invoices", "getInvoices", "isLoading", "createInvoice", "", "invoice", "deleteInvoice", "getInvoicesByCustomer", "customerId", "getInvoicesByUser", "userId", "insertInvoice", "loadInvoices", "updateInvoice", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class InvoiceViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.InvoiceUseCases invoiceUseCases = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.sharenshop.data.model.Invoice>> _invoices = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.sharenshop.data.model.Invoice>> invoices = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _error = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> error = null;
    
    @javax.inject.Inject()
    public InvoiceViewModel(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.InvoiceUseCases invoiceUseCases) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.sharenshop.data.model.Invoice>> getInvoices() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getError() {
        return null;
    }
    
    public final void loadInvoices() {
    }
    
    public final void createInvoice(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Invoice invoice) {
    }
    
    public final void insertInvoice(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Invoice invoice) {
    }
    
    public final void updateInvoice(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Invoice invoice) {
    }
    
    public final void deleteInvoice(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Invoice invoice) {
    }
    
    public final void getInvoicesByCustomer(@org.jetbrains.annotations.NotNull()
    java.lang.String customerId) {
    }
    
    public final void getInvoicesByUser(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
    }
}