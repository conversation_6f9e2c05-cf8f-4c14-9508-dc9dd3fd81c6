// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.security.SharedPreferencesEncryptionService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SecurityModule_Companion_ProvideSharedPreferencesEncryptionServiceFactory implements Factory<SharedPreferencesEncryptionService> {
  @Override
  public SharedPreferencesEncryptionService get() {
    return provideSharedPreferencesEncryptionService();
  }

  public static SecurityModule_Companion_ProvideSharedPreferencesEncryptionServiceFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SharedPreferencesEncryptionService provideSharedPreferencesEncryptionService() {
    return Preconditions.checkNotNullFromProvides(SecurityModule.Companion.provideSharedPreferencesEncryptionService());
  }

  private static final class InstanceHolder {
    private static final SecurityModule_Companion_ProvideSharedPreferencesEncryptionServiceFactory INSTANCE = new SecurityModule_Companion_ProvideSharedPreferencesEncryptionServiceFactory();
  }
}
