// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.presentation.auth;

import com.example.sharenshop.data.local.SessionManager;
import com.example.sharenshop.domain.use_case.UserUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AuthViewModel_Factory implements Factory<AuthViewModel> {
  private final Provider<UserUseCases> userUseCasesProvider;

  private final Provider<SessionManager> sessionManagerProvider;

  public AuthViewModel_Factory(Provider<UserUseCases> userUseCasesProvider,
      Provider<SessionManager> sessionManagerProvider) {
    this.userUseCasesProvider = userUseCasesProvider;
    this.sessionManagerProvider = sessionManagerProvider;
  }

  @Override
  public AuthViewModel get() {
    return newInstance(userUseCasesProvider.get(), sessionManagerProvider.get());
  }

  public static AuthViewModel_Factory create(Provider<UserUseCases> userUseCasesProvider,
      Provider<SessionManager> sessionManagerProvider) {
    return new AuthViewModel_Factory(userUseCasesProvider, sessionManagerProvider);
  }

  public static AuthViewModel newInstance(UserUseCases userUseCases,
      SessionManager sessionManager) {
    return new AuthViewModel(userUseCases, sessionManager);
  }
}
