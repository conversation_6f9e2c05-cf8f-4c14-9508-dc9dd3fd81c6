package com.example.sharenshop.domain.use_case

import com.example.sharenshop.data.model.Customer
import com.example.sharenshop.domain.repository.CustomerRepository
import kotlinx.coroutines.flow.Flow

// Grouping all customer-related use cases
data class CustomerUseCases(
    val getCustomer: GetCustomer,
    val insertCustomer: InsertCustomer,
    val updateCustomer: UpdateCustomer,
    val deleteCustomer: DeleteCustomer,
    val getAllCustomers: GetAllCustomers,
    val searchCustomers: SearchCustomers
)

class GetCustomer(private val repository: CustomerRepository) {
    operator fun invoke(customerId: String): Flow<Customer?> {
        return repository.getCustomerById(customerId)
    }
}

class InsertCustomer(private val repository: CustomerRepository) {
    suspend operator fun invoke(customer: Customer) {
        repository.insertCustomer(customer)
    }
}

class UpdateCustomer(private val repository: CustomerRepository) {
    suspend operator fun invoke(customer: Customer) {
        repository.updateCustomer(customer)
    }
}

class DeleteCustomer(private val repository: CustomerRepository) {
    suspend operator fun invoke(customerId: String) {
        repository.deleteCustomerById(customerId)
    }
}

class GetAllCustomers(private val repository: CustomerRepository) {
    operator fun invoke(): Flow<List<Customer>> {
        return repository.getAllCustomers()
    }
}

class SearchCustomers(private val repository: CustomerRepository) {
    operator fun invoke(query: String): Flow<List<Customer>> {
        return repository.searchCustomers(query)
    }
} 