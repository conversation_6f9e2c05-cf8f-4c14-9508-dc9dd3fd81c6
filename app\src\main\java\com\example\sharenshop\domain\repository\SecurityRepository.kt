package com.example.sharenshop.domain.repository

import com.example.sharenshop.data.model.EncryptionSettings
import kotlinx.coroutines.flow.Flow

interface SecurityRepository {
    fun getEncryptionSettings(): Flow<EncryptionSettings>
    suspend fun saveEncryptionSettings(settings: EncryptionSettings)
    fun encryptData(data: String): String
    fun decryptData(encryptedData: String): String
    // Potentially add more security-related operations
} 