package com.example.sharenshop.ui.theme

import androidx.compose.ui.graphics.Color

// Custom SharenShop Theme Colors
// پس زمینه: Future Dusk
val FutureDusk = Color(0xFF2A2D3A)
val FutureDuskLight = Color(0xFF3A3E4F)
val FutureDuskDark = Color(0xFF1A1D2A)

// دکمه‌ها و المان‌های مهم: آبی عمیق
val DeepBlue = Color(0xFF1E3A8A)
val DeepBlueLight = Color(0xFF3B82F6)
val DeepBlueDark = Color(0xFF1E40AF)

// متون و آیکون‌ها: خاکستری ملایم
val SoftGray = Color(0xFF9CA3AF)
val SoftGrayLight = Color(0xFFD1D5DB)
val SoftGrayDark = Color(0xFF6B7280)

// تاکید و بخش‌های خاص: قهوه‌ای تیره
val DarkBrown = Color(0xFF8B4513)
val DarkBrownLight = Color(0xFFA0522D)
val DarkBrownDark = Color(0xFF654321)

// Light Theme Colors - SharenShop Custom
val md_theme_light_primary = DeepBlue
val md_theme_light_onPrimary = Color(0xFFFFFFFF)
val md_theme_light_primaryContainer = DeepBlueLight
val md_theme_light_onPrimaryContainer = Color(0xFFFFFFFF)
val md_theme_light_secondary = DarkBrown
val md_theme_light_onSecondary = Color(0xFFFFFFFF)
val md_theme_light_secondaryContainer = DarkBrownLight
val md_theme_light_onSecondaryContainer = Color(0xFFFFFFFF)
val md_theme_light_tertiary = SoftGray
val md_theme_light_onTertiary = Color(0xFFFFFFFF)
val md_theme_light_tertiaryContainer = SoftGrayLight
val md_theme_light_onTertiaryContainer = FutureDusk
val md_theme_light_error = Color(0xFFDC2626)
val md_theme_light_errorContainer = Color(0xFFFEE2E2)
val md_theme_light_onError = Color(0xFFFFFFFF)
val md_theme_light_onErrorContainer = Color(0xFFDC2626)
val md_theme_light_background = Color(0xFFF8FAFC)
val md_theme_light_onBackground = SoftGrayDark
val md_theme_light_surface = Color(0xFFFFFFFF)
val md_theme_light_onSurface = SoftGrayDark
val md_theme_light_surfaceVariant = SoftGrayLight
val md_theme_light_onSurfaceVariant = SoftGray
val md_theme_light_outline = SoftGray
val md_theme_light_inverseOnSurface = Color(0xFFFFFFFF)
val md_theme_light_inverseSurface = FutureDusk
val md_theme_light_inversePrimary = DeepBlueLight
val md_theme_light_shadow = Color(0xFF000000)
val md_theme_light_surfaceTint = DeepBlue
val md_theme_light_outlineVariant = SoftGrayLight
val md_theme_light_scrim = Color(0xFF000000)

// Dark Theme Colors - SharenShop Custom (Future Dusk Based)
val md_theme_dark_primary = DeepBlueLight
val md_theme_dark_onPrimary = Color(0xFFFFFFFF)
val md_theme_dark_primaryContainer = DeepBlueDark
val md_theme_dark_onPrimaryContainer = Color(0xFFFFFFFF)
val md_theme_dark_secondary = DarkBrownLight
val md_theme_dark_onSecondary = Color(0xFFFFFFFF)
val md_theme_dark_secondaryContainer = DarkBrownDark
val md_theme_dark_onSecondaryContainer = Color(0xFFFFFFFF)
val md_theme_dark_tertiary = SoftGrayLight
val md_theme_dark_onTertiary = FutureDusk
val md_theme_dark_tertiaryContainer = SoftGrayDark
val md_theme_dark_onTertiaryContainer = Color(0xFFFFFFFF)
val md_theme_dark_error = Color(0xFFF87171)
val md_theme_dark_errorContainer = Color(0xFFDC2626)
val md_theme_dark_onError = Color(0xFFFFFFFF)
val md_theme_dark_onErrorContainer = Color(0xFFFFFFFF)
val md_theme_dark_background = FutureDusk
val md_theme_dark_onBackground = SoftGrayLight
val md_theme_dark_surface = FutureDuskLight
val md_theme_dark_onSurface = SoftGrayLight
val md_theme_dark_surfaceVariant = FutureDuskDark
val md_theme_dark_onSurfaceVariant = SoftGray
val md_theme_dark_outline = SoftGrayDark
val md_theme_dark_inverseOnSurface = FutureDusk
val md_theme_dark_inverseSurface = SoftGrayLight
val md_theme_dark_inversePrimary = DeepBlueDark
val md_theme_dark_shadow = Color(0xFF000000)
val md_theme_dark_surfaceTint = DeepBlueLight
val md_theme_dark_outlineVariant = SoftGrayDark
val md_theme_dark_scrim = Color(0xFF000000)