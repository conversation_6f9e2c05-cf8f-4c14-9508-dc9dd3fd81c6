// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.domain.repository.UserRepository;
import com.example.sharenshop.domain.use_case.UserUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideUserUseCasesFactory implements Factory<UserUseCases> {
  private final Provider<UserRepository> repositoryProvider;

  public UseCaseModule_ProvideUserUseCasesFactory(Provider<UserRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public UserUseCases get() {
    return provideUserUseCases(repositoryProvider.get());
  }

  public static UseCaseModule_ProvideUserUseCasesFactory create(
      Provider<UserRepository> repositoryProvider) {
    return new UseCaseModule_ProvideUserUseCasesFactory(repositoryProvider);
  }

  public static UserUseCases provideUserUseCases(UserRepository repository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideUserUseCases(repository));
  }
}
