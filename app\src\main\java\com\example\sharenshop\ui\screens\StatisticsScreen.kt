package com.example.sharenshop.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
// import androidx.navigation.NavHostController // ایمپورت حذف شد
import androidx.compose.ui.tooling.preview.Preview

@Composable
fun StatisticsScreen() { // پارامتر navController حذف شد
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(text = "Statistics Screen")
        // TODO: Implement charts, graphs, and statistical data display
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewStatisticsScreen() {
    StatisticsScreen() // فراخوانی بدون navController
}
