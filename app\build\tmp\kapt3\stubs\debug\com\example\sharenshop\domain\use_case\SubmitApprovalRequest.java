package com.example.sharenshop.domain.use_case;

/**
 * ارسال درخواست تایید مشتری
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ<\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\f\u001a\u00020\u000b2\u0006\u0010\r\u001a\u00020\u000b2\u0006\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000bH\u0086B\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0010\u0010\u0011R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0012"}, d2 = {"Lcom/example/sharenshop/domain/use_case/SubmitApprovalRequest;", "", "customerApprovalRepository", "Lcom/example/sharenshop/domain/repository/CustomerApprovalRepository;", "userRepository", "Lcom/example/sharenshop/domain/repository/UserRepository;", "notificationRepository", "Lcom/example/sharenshop/domain/repository/NotificationRepository;", "(Lcom/example/sharenshop/domain/repository/CustomerApprovalRepository;Lcom/example/sharenshop/domain/repository/UserRepository;Lcom/example/sharenshop/domain/repository/NotificationRepository;)V", "invoke", "Lkotlin/Result;", "", "customerEmail", "customerName", "customerPhone", "referrerCode", "invoke-yxL6bBk", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class SubmitApprovalRequest {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.repository.CustomerApprovalRepository customerApprovalRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.repository.UserRepository userRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.repository.NotificationRepository notificationRepository = null;
    
    @javax.inject.Inject()
    public SubmitApprovalRequest(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.CustomerApprovalRepository customerApprovalRepository, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.UserRepository userRepository, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.NotificationRepository notificationRepository) {
        super();
    }
}