// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ValidateReferrerCode_Factory implements Factory<ValidateReferrerCode> {
  private final Provider<UserRepository> userRepositoryProvider;

  public ValidateReferrerCode_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public ValidateReferrerCode get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static ValidateReferrerCode_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new ValidateReferrerCode_Factory(userRepositoryProvider);
  }

  public static ValidateReferrerCode newInstance(UserRepository userRepository) {
    return new ValidateReferrerCode(userRepository);
  }
}
