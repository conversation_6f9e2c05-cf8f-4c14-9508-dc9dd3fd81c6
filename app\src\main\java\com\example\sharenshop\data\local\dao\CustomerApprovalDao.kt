package com.example.sharenshop.data.local.dao

import androidx.room.*
import com.example.sharenshop.data.model.CustomerApprovalRequest
import com.example.sharenshop.data.model.ApprovalStatus
import kotlinx.coroutines.flow.Flow

@Dao
interface CustomerApprovalDao {
    
    @Query("SELECT * FROM customer_approval_requests WHERE id = :id")
    suspend fun getRequestById(id: String): CustomerApprovalRequest?
    
    @Query("SELECT * FROM customer_approval_requests ORDER BY requestDate DESC")
    fun getAllRequests(): Flow<List<CustomerApprovalRequest>>
    
    @Query("SELECT * FROM customer_approval_requests WHERE referrerId = :referrerId ORDER BY requestDate DESC")
    fun getRequestsByReferrer(referrerId: String): Flow<List<CustomerApprovalRequest>>
    
    @Query("SELECT * FROM customer_approval_requests WHERE status = :status ORDER BY requestDate DESC")
    fun getRequestsByStatus(status: ApprovalStatus): Flow<List<CustomerApprovalRequest>>
    
    @Query("SELECT * FROM customer_approval_requests WHERE customerEmail = :email")
    suspend fun getRequestByCustomerEmail(email: String): CustomerApprovalRequest?
    
    @Query("SELECT * FROM customer_approval_requests WHERE referrerCode = :code AND status = 'pending'")
    suspend fun getPendingRequestsByReferrerCode(code: String): List<CustomerApprovalRequest>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRequest(request: CustomerApprovalRequest)
    
    @Update
    suspend fun updateRequest(request: CustomerApprovalRequest)
    
    @Delete
    suspend fun deleteRequest(request: CustomerApprovalRequest)
    
    @Query("UPDATE customer_approval_requests SET status = :status, responseDate = :responseDate WHERE id = :requestId")
    suspend fun updateRequestStatus(requestId: String, status: ApprovalStatus, responseDate: Long)
    
    @Query("DELETE FROM customer_approval_requests WHERE status = 'expired' OR (status = 'pending' AND requestDate < :expireDate)")
    suspend fun deleteExpiredRequests(expireDate: Long)
    
    @Query("SELECT COUNT(*) FROM customer_approval_requests WHERE referrerId = :referrerId AND status = 'pending'")
    suspend fun getPendingRequestsCount(referrerId: String): Int
}
