[versions]
androidGradlePlugin = "8.4.0"
kotlin = "1.9.22"
hilt = "2.48.1"
kotlinxSerialization = "1.6.3"
kotlinSerializationPlugin = "1.9.22"
compileSdk = "34"
minSdk = "24"
targetSdk = "34"
androidxCoreKtx = "1.12.0"
appcompat = "1.6.1"
material = "1.11.0" # این احتمالا لازم نیست
lifecycleRuntimeKtx = "2.7.0"
activityCompose = "1.8.2"
roomVersion = "2.6.1"
junit = "4.13.2"
androidxJunit = "1.1.5"
espressoCore = "3.5.1"
composeUiVersion = "1.6.0" # یک نسخه مشترک برای کامپوننت‌های UI Compose
composeCompiler = "1.5.10"
material3Version = "1.2.0"
datastorePreferences = "1.1.0"
desugarJdkLibs = "2.0.4"
supabaseBom = "2.5.4"
ktorVersion = "2.3.7"
navigationComposeVersion = "2.7.7"
hiltNavigationComposeVersion = "1.2.0"
lifecycleRuntimeComposeVersion = "2.7.0"


[plugins]
android-application = { id = "com.android.application", version.ref = "androidGradlePlugin" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
hilt-android = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlinSerializationPlugin" }
# kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" } # اگر به عنوان پلاگین جدا استفاده می‌کنید

[libraries]
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "androidxCoreKtx" }
# androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" } # احتمالا لازم نیست
# androidx-material = { group = "com.google.android.material", name = "material", version.ref = "material" } # از material3 استفاده می‌شود
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-datastore-preferences = { group = "androidx.datastore", name = "datastore-preferences", version.ref = "datastorePreferences" }

hilt-android = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
hilt-compiler = { group = "com.google.dagger", name = "hilt-compiler", version.ref = "hilt" }

room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "roomVersion" }
room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "roomVersion" }
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "roomVersion" }

kotlinx-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinxSerialization" }

# Supabase
supabase-bom = { group = "io.github.jan-tennert.supabase", name = "bom", version.ref = "supabaseBom" }
supabase-gotrue = { group = "io.github.jan-tennert.supabase", name = "gotrue-kt" }
supabase-postgrest = { group = "io.github.jan-tennert.supabase", name = "postgrest-kt" }
supabase-functions = { group = "io.github.jan-tennert.supabase", name = "functions-kt" }

# Ktor (برای Supabase HTTP client)
ktor-client-android = { group = "io.ktor", name = "ktor-client-android", version.ref = "ktorVersion" }



junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "androidxJunit" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }

# Compose UI libraries
androidx-compose-ui = { group = "androidx.compose.ui", name = "ui", version.ref = "composeUiVersion"}
androidx-compose-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics", version.ref = "composeUiVersion"}
androidx-compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling", version.ref = "composeUiVersion"} # برای debugImplementation
androidx-compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview", version.ref = "composeUiVersion"}
androidx-compose-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4", version.ref = "composeUiVersion"} # برای androidTestImplementation
androidx-compose-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest", version.ref = "composeUiVersion"} # برای debugImplementation

# Material 3
material3 = { group = "androidx.compose.material3", name = "material3", version.ref = "material3Version" }

# Navigation and other lifecycle compose
navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "navigationComposeVersion" }
hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "hiltNavigationComposeVersion" }
lifecycle-runtime-compose = { group = "androidx.lifecycle", name = "lifecycle-runtime-compose", version.ref = "lifecycleRuntimeComposeVersion" }

android-desugarJdkLibs = { group = "com.android.tools", name = "desugar_jdk_libs", version.ref = "desugarJdkLibs" }

[bundles]
# باندل compose شما از قبل تعاریف خوبی داشت، من فقط آن‌ها را به روز می‌کنم تا از نسخه‌های مشترک استفاده کنند
compose-essentials = [ # تغییر نام به essentials برای وضوح بیشتر
    "androidx-compose-ui",
    "androidx-compose-ui-graphics",
    "androidx-compose-ui-tooling-preview", # این برای implementation است
    "material3" # material3 به جای compose-material3
]
room = [
    "room-runtime",
    "room-ktx"
]
