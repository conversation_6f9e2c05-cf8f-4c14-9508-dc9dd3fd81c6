package com.example.sharenshop.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.sharenshop.data.model.*
import com.example.sharenshop.presentation.invoice.InvoiceViewModel
import com.example.sharenshop.ui.components.StatCard
import com.example.sharenshop.ui.components.formatCurrency
import java.math.BigDecimal
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*
import java.util.UUID

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InvoiceScreen(
    viewModel: InvoiceViewModel = hiltViewModel(),
    userRole: UserRole = UserRole.SUPER_ADMIN
) {
    val invoices by viewModel.invoices.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    var selectedTab by remember { mutableStateOf(0) }
    var showCreateDialog by remember { mutableStateOf(false) }

    val tabs = when (userRole) {
        UserRole.SUPER_ADMIN -> listOf("همه فاکتورها", "نقدی", "نسیه", "معوقه")
        UserRole.CASHIER -> listOf("فاکتورهای من", "نقدی", "نسیه")
        UserRole.CUSTOMER -> listOf("خریدهای من", "پرداخت شده", "در انتظار")
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        when (userRole) {
                            UserRole.SUPER_ADMIN -> "مدیریت فاکتورها"
                            UserRole.CASHIER -> "فاکتورهای فروش"
                            UserRole.CUSTOMER -> "فاکتورهای من"
                        }
                    )
                },
                actions = {
                    if (userRole != UserRole.CUSTOMER) {
                        IconButton(onClick = { showCreateDialog = true }) {
                            Icon(Icons.Default.Add, contentDescription = "فاکتور جدید")
                        }
                    }
                }
            )
        },
        floatingActionButton = {
            if (userRole != UserRole.CUSTOMER) {
                FloatingActionButton(
                    onClick = { showCreateDialog = true }
                ) {
                    Icon(Icons.Default.Add, contentDescription = "فاکتور جدید")
                }
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // تب‌ها
            TabRow(selectedTabIndex = selectedTab) {
                tabs.forEachIndexed { index, title ->
                    Tab(
                        selected = selectedTab == index,
                        onClick = { selectedTab = index },
                        text = { Text(title) }
                    )
                }
            }

            // محتوای تب انتخاب شده
            when (selectedTab) {
                0 -> InvoiceListContent(
                    invoices = invoices,
                    isLoading = isLoading,
                    userRole = userRole
                )
                1 -> InvoiceListContent(
                    invoices = invoices.filter { it.paymentType == "cash" },
                    isLoading = isLoading,
                    userRole = userRole
                )
                2 -> InvoiceListContent(
                    invoices = invoices.filter { it.paymentType == "credit" },
                    isLoading = isLoading,
                    userRole = userRole
                )
                3 -> if (userRole == UserRole.SUPER_ADMIN) {
                    InvoiceListContent(
                        invoices = invoices.filter { it.paymentStatus == "overdue" },
                        isLoading = isLoading,
                        userRole = userRole
                    )
                } else {
                    InvoiceListContent(
                        invoices = invoices.filter { it.paymentStatus == "pending" },
                        isLoading = isLoading,
                        userRole = userRole
                    )
                }
            }
        }
    }

    // دیالوگ ایجاد فاکتور جدید
    if (showCreateDialog) {
        CreateInvoiceDialog(
            onDismiss = { showCreateDialog = false },
            onSave = { invoice ->
                viewModel.createInvoice(invoice)
                showCreateDialog = false
            }
        )
    }
}

@Composable
fun InvoiceListContent(
    invoices: List<Invoice>,
    isLoading: Boolean,
    userRole: UserRole
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // آمار سریع
        InvoiceStatsRow(invoices, userRole)

        Spacer(modifier = Modifier.height(16.dp))

        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (invoices.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    @Suppress("DEPRECATION")
                    Icon(
                        Icons.Default.List,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "هیچ فاکتوری یافت نشد",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
        } else {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(invoices) { invoice ->
                    InvoiceCard(
                        invoice = invoice,
                        userRole = userRole,
                        onViewClick = { /* TODO: Navigate to invoice details */ },
                        onEditClick = { /* TODO: Edit invoice */ },
                        onDeleteClick = { /* TODO: Delete invoice */ }
                    )
                }
            }
        }
    }
}

@Composable
fun InvoiceStatsRow(invoices: List<Invoice>, userRole: UserRole) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        @Suppress("DEPRECATION")
        StatCard(
            title = "کل فاکتورها",
            value = invoices.size.toString(),
            icon = Icons.Default.List,
            color = Color(0xFF2196F3),
            modifier = Modifier.weight(1f)
        )

        StatCard(
            title = "مبلغ کل",
            value = formatCurrency(
                invoices.sumOf { it.finalAmount }
            ),
            icon = Icons.Default.Star,
            color = Color(0xFF4CAF50),
            modifier = Modifier.weight(1f)
        )

        if (userRole != UserRole.CUSTOMER) {
            StatCard(
                title = "معوقه",
                value = invoices.count { it.paymentStatus == "overdue" }.toString(),
                icon = Icons.Default.Warning,
                color = Color(0xFFFF9800),
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InvoiceCard(
    invoice: Invoice,
    userRole: UserRole,
    onViewClick: (Invoice) -> Unit,
    onEditClick: (Invoice) -> Unit,
    onDeleteClick: (Invoice) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        onClick = { onViewClick(invoice) }
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "فاکتور #${invoice.id.take(8)}",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Text(
                        text = formatDate(invoice.invoiceDate),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }

                // وضعیت پرداخت
                PaymentStatusChip(invoice.paymentStatus)
            }

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // مبلغ کل
                Column {
                    Text(
                        text = "مبلغ کل",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                    Text(
                        text = formatCurrency(invoice.finalAmount),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                }

                // نوع پرداخت
                Column(horizontalAlignment = Alignment.End) {
                    Text(
                        text = "نوع پرداخت",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                    Text(
                        text = when (invoice.paymentType) {
                            "cash" -> "نقدی"
                            "credit" -> "نسیه"
                            else -> "نامشخص"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                }
            }

            // مبلغ پرداخت شده و باقی‌مانده (برای نسیه)
            if (invoice.paymentType == "credit") {
                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Column {
                        Text(
                            text = "پرداخت شده",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                        Text(
                            text = formatCurrency(invoice.paidAmount),
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color(0xFF4CAF50)
                        )
                    }

                    Column(horizontalAlignment = Alignment.End) {
                        Text(
                            text = "باقی‌مانده",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                        Text(
                            text = formatCurrency(invoice.remainingAmount),
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (invoice.remainingAmount > BigDecimal.ZERO) {
                                Color(0xFFFF9800)
                            } else {
                                Color(0xFF4CAF50)
                            }
                        )
                    }
                }
            }

            // دکمه‌های عملیات
            if (userRole != UserRole.CUSTOMER) {
                Spacer(modifier = Modifier.height(12.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = { onEditClick(invoice) }) {
                        Text("ویرایش")
                    }

                    if (userRole == UserRole.SUPER_ADMIN) {
                        TextButton(
                            onClick = { onDeleteClick(invoice) },
                            colors = ButtonDefaults.textButtonColors(
                                contentColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Text("حذف")
                        }
                    }
                }
            }
        }
    }
}



@Composable
fun PaymentStatusChip(status: String) {
    val (text, color) = when (status) {
        "paid" -> "پرداخت شده" to Color(0xFF4CAF50)
        "pending" -> "در انتظار" to Color(0xFFFF9800)
        "partial" -> "جزئی" to Color(0xFF2196F3)
        "overdue" -> "معوقه" to Color(0xFFF44336)
        else -> "نامشخص" to Color(0xFF9E9E9E)
    }

    Surface(
        shape = RoundedCornerShape(12.dp),
        color = color.copy(alpha = 0.1f),
        modifier = Modifier.padding(4.dp)
    ) {
        Text(
            text = text,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            style = MaterialTheme.typography.bodySmall,
            color = color,
            fontWeight = FontWeight.Medium
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateInvoiceDialog(
    onDismiss: () -> Unit,
    onSave: (Invoice) -> Unit
) {
    var customerId by remember { mutableStateOf("") }
    var totalAmount by remember { mutableStateOf("") }
    var discountAmount by remember { mutableStateOf("0") }
    var paymentType by remember { mutableStateOf("cash") }
    var notes by remember { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("ایجاد فاکتور جدید") },
        text = {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    OutlinedTextField(
                        value = customerId,
                        onValueChange = { customerId = it },
                        label = { Text("شناسه مشتری *") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )
                }

                item {
                    OutlinedTextField(
                        value = totalAmount,
                        onValueChange = { totalAmount = it },
                        label = { Text("مبلغ کل (ریال) *") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        suffix = { Text("ریال") }
                    )
                }

                item {
                    OutlinedTextField(
                        value = discountAmount,
                        onValueChange = { discountAmount = it },
                        label = { Text("تخفیف (ریال)") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        suffix = { Text("ریال") }
                    )
                }

                item {
                    var expanded by remember { mutableStateOf(false) }

                    ExposedDropdownMenuBox(
                        expanded = expanded,
                        onExpandedChange = { expanded = !expanded }
                    ) {
                        OutlinedTextField(
                            value = when (paymentType) {
                                "cash" -> "نقدی"
                                "credit" -> "نسیه"
                                else -> "انتخاب کنید"
                            },
                            onValueChange = {},
                            readOnly = true,
                            label = { Text("نوع پرداخت") },
                            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                            modifier = Modifier
                                .menuAnchor()
                                .fillMaxWidth()
                        )

                        ExposedDropdownMenu(
                            expanded = expanded,
                            onDismissRequest = { expanded = false }
                        ) {
                            DropdownMenuItem(
                                text = { Text("نقدی") },
                                onClick = {
                                    paymentType = "cash"
                                    expanded = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text("نسیه") },
                                onClick = {
                                    paymentType = "credit"
                                    expanded = false
                                }
                            )
                        }
                    }
                }

                item {
                    OutlinedTextField(
                        value = notes,
                        onValueChange = { notes = it },
                        label = { Text("یادداشت") },
                        modifier = Modifier.fillMaxWidth(),
                        maxLines = 3
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (customerId.isNotBlank() && totalAmount.isNotBlank()) {
                        try {
                            val total = BigDecimal(totalAmount)
                            val discount = BigDecimal(discountAmount.ifBlank { "0" })
                            val finalAmount = total.subtract(discount)

                            val invoice = Invoice(
                                id = UUID.randomUUID().toString(),
                                customerId = customerId,
                                userId = "current_user_id", // TODO: Get from current user
                                invoiceDate = System.currentTimeMillis(),
                                totalAmount = total,
                                discountAmount = discount,
                                finalAmount = finalAmount,
                                paymentStatus = if (paymentType == "cash") "paid" else "pending",
                                paymentType = paymentType,
                                paidAmount = if (paymentType == "cash") finalAmount else BigDecimal.ZERO,
                                remainingAmount = if (paymentType == "cash") BigDecimal.ZERO else finalAmount,
                                notes = notes.takeIf { it.isNotBlank() },
                                createdAt = System.currentTimeMillis()
                            )
                            onSave(invoice)
                        } catch (e: Exception) {
                            // TODO: Show error message
                        }
                    }
                }
            ) {
                Text("ایجاد")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("لغو")
            }
        }
    )
}

// Helper functions
fun formatDate(timestamp: Long): String {
    val sdf = SimpleDateFormat("yyyy/MM/dd HH:mm", Locale("fa", "IR"))
    return sdf.format(Date(timestamp))
}

