package com.example.sharenshop.data.repository;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0010\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\rH\u0016J\u0010\u0010\u000f\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\rH\u0016J\u000e\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0012H\u0016J\u0016\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0002\u0010\u0016R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0017"}, d2 = {"Lcom/example/sharenshop/data/repository/SecurityRepositoryImpl;", "Lcom/example/sharenshop/domain/repository/SecurityRepository;", "databaseEncryptionService", "Lcom/example/sharenshop/data/security/DatabaseEncryptionService;", "fileEncryptionService", "Lcom/example/sharenshop/data/security/FileEncryptionService;", "sharedPreferencesEncryptionService", "Lcom/example/sharenshop/data/security/SharedPreferencesEncryptionService;", "(Lcom/example/sharenshop/data/security/DatabaseEncryptionService;Lcom/example/sharenshop/data/security/FileEncryptionService;Lcom/example/sharenshop/data/security/SharedPreferencesEncryptionService;)V", "_encryptionSettings", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/sharenshop/data/model/EncryptionSettings;", "decryptData", "", "encryptedData", "encryptData", "data", "getEncryptionSettings", "Lkotlinx/coroutines/flow/Flow;", "saveEncryptionSettings", "", "settings", "(Lcom/example/sharenshop/data/model/EncryptionSettings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class SecurityRepositoryImpl implements com.example.sharenshop.domain.repository.SecurityRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.data.security.DatabaseEncryptionService databaseEncryptionService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.data.security.FileEncryptionService fileEncryptionService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.data.security.SharedPreferencesEncryptionService sharedPreferencesEncryptionService = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.sharenshop.data.model.EncryptionSettings> _encryptionSettings = null;
    
    @javax.inject.Inject()
    public SecurityRepositoryImpl(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.security.DatabaseEncryptionService databaseEncryptionService, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.security.FileEncryptionService fileEncryptionService, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.security.SharedPreferencesEncryptionService sharedPreferencesEncryptionService) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<com.example.sharenshop.data.model.EncryptionSettings> getEncryptionSettings() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object saveEncryptionSettings(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.EncryptionSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String encryptData(@org.jetbrains.annotations.NotNull()
    java.lang.String data) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String decryptData(@org.jetbrains.annotations.NotNull()
    java.lang.String encryptedData) {
        return null;
    }
}