<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="B:\SHARENAPP\sharenshop\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="B:\SHARENAPP\sharenshop\app\src\main\res"><file name="ic_launcher_background" path="B:\SHARENAPP\sharenshop\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="B:\SHARENAPP\sharenshop\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="B:\SHARENAPP\sharenshop\app\src\main\res\mipmap\ic_launcher_foreground.xml" qualifiers="" type="mipmap"/><file name="ic_launcher" path="B:\SHARENAPP\sharenshop\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="B:\SHARENAPP\sharenshop\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file path="B:\SHARENAPP\sharenshop\app\src\main\res\values\colors.xml" qualifiers=""><color name="future_dusk">#2A2D3A</color><color name="future_dusk_light">#3A3E4F</color><color name="future_dusk_dark">#1A1D2A</color><color name="deep_blue">#1E3A8A</color><color name="deep_blue_light">#3B82F6</color><color name="deep_blue_dark">#1E40AF</color><color name="soft_gray">#9CA3AF</color><color name="soft_gray_light">#D1D5DB</color><color name="soft_gray_dark">#6B7280</color><color name="dark_brown">#8B4513</color><color name="dark_brown_light">#A0522D</color><color name="dark_brown_dark">#654321</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="ic_launcher_background">#2A2D3A</color></file><file path="B:\SHARENAPP\sharenshop\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">SharenShop</string></file><file path="B:\SHARENAPP\sharenshop\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.SharenShop" parent="android:Theme.Material.Light.NoActionBar">
        
    </style></file><file path="B:\SHARENAPP\sharenshop\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.SharenShop" parent="android:Theme.Material.NoActionBar">
        
    </style></file><file name="backup_rules" path="B:\SHARENAPP\sharenshop\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="B:\SHARENAPP\sharenshop\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="B:\SHARENAPP\sharenshop\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="B:\SHARENAPP\sharenshop\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="B:\SHARENAPP\sharenshop\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="B:\SHARENAPP\sharenshop\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>