package com.example.sharenshop.ui.theme;

/**
 * Helper functions برای انتخاب رنگ بر اساس وضعیت
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001d\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0007\u0010\bJ\u001d\u0010\t\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u0006H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u000b\u0010\bJ\'\u0010\f\u001a\u00020\u00042\u0006\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u000eH\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0010\u0010\u0011\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0012"}, d2 = {"Lcom/example/sharenshop/ui/theme/ColorHelper;", "", "()V", "getInvoiceStatusColor", "Landroidx/compose/ui/graphics/Color;", "status", "", "getInvoiceStatusColor-vNxB06k", "(Ljava/lang/String;)J", "getRoleColor", "userType", "getRoleColor-vNxB06k", "getStockStatusColor", "stock", "", "minStock", "getStockStatusColor-WaAFU9c", "(II)J", "app_debug"})
public final class ColorHelper {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.sharenshop.ui.theme.ColorHelper INSTANCE = null;
    
    private ColorHelper() {
        super();
    }
}