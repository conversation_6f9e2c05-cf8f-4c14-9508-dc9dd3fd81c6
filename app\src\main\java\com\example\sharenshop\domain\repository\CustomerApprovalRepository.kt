package com.example.sharenshop.domain.repository

import com.example.sharenshop.data.model.CustomerApprovalRequest
import com.example.sharenshop.data.model.ApprovalStatus
import kotlinx.coroutines.flow.Flow

interface CustomerApprovalRepository {
    
    suspend fun getRequestById(id: String): CustomerApprovalRequest?
    
    fun getAllRequests(): Flow<List<CustomerApprovalRequest>>
    
    fun getRequestsByReferrer(referrerId: String): Flow<List<CustomerApprovalRequest>>
    
    fun getRequestsByStatus(status: ApprovalStatus): Flow<List<CustomerApprovalRequest>>
    
    suspend fun getRequestByCustomerEmail(email: String): CustomerApprovalRequest?
    
    suspend fun getPendingRequestsByReferrerCode(code: String): List<CustomerApprovalRequest>
    
    suspend fun insertRequest(request: CustomerApprovalRequest)
    
    suspend fun updateRequest(request: CustomerApprovalRequest)
    
    suspend fun deleteRequest(request: CustomerApprovalRequest)
    
    suspend fun updateRequestStatus(requestId: String, status: ApprovalStatus, responseDate: Long)
    
    suspend fun deleteExpiredRequests(expireDate: Long)
    
    suspend fun getPendingRequestsCount(referrerId: String): Int
}
