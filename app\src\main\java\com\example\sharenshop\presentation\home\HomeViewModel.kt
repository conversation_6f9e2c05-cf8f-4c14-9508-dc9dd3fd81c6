package com.example.sharenshop.presentation.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharenshop.domain.use_case.StatisticsUseCases
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import java.math.BigDecimal
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val statisticsUseCases: StatisticsUseCases
) : ViewModel() {

    private val _totalSales = MutableStateFlow(BigDecimal.ZERO)
    val totalSales: StateFlow<BigDecimal> = _totalSales.asStateFlow()

    private val _totalProductsSold = MutableStateFlow(0)
    val totalProductsSold: StateFlow<Int> = _totalProductsSold.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    init {
        loadDashboardData()
    }

    private fun loadDashboardData() {
        _isLoading.value = true
        statisticsUseCases.getTotalSalesAmount()
            .onEach { amount ->
                _totalSales.value = amount
            }
            .catch { e ->
                _error.value = e.localizedMessage ?: "Unknown error loading total sales"
            }
            .launchIn(viewModelScope)

        statisticsUseCases.getTotalProductsSold()
            .onEach { count ->
                _totalProductsSold.value = count
            }
            .catch { e ->
                _error.value = e.localizedMessage ?: "Unknown error loading total products sold"
            }
            .launchIn(viewModelScope)

        // TODO: Load more dashboard data as needed (e.g., recent invoices, top customers)
        _isLoading.value = false // This should ideally be set after all data streams are collected
    }
} 