package com.example.sharenshop.data.remote;

/**
 * Supabase Client Configuration
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u000f\u001a\u00020\u0010R\u0011\u0010\u0003\u001a\u00020\u00048F\u00a2\u0006\u0006\u001a\u0004\b\u0005\u0010\u0006R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u000b\u001a\u00020\f8F\u00a2\u0006\u0006\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u0011"}, d2 = {"Lcom/example/sharenshop/data/remote/SupabaseClient;", "", "()V", "auth", "Lio/github/jan/supabase/gotrue/Auth;", "getAuth", "()Lio/github/jan/supabase/gotrue/Auth;", "client", "Lio/github/jan/supabase/SupabaseClient;", "getClient", "()Lio/github/jan/supabase/SupabaseClient;", "postgrest", "Lio/github/jan/supabase/postgrest/Postgrest;", "getPostgrest", "()Lio/github/jan/supabase/postgrest/Postgrest;", "isConfigured", "", "app_debug"})
public final class SupabaseClient {
    @org.jetbrains.annotations.NotNull()
    private static final io.github.jan.supabase.SupabaseClient client = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.sharenshop.data.remote.SupabaseClient INSTANCE = null;
    
    private SupabaseClient() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final io.github.jan.supabase.SupabaseClient getClient() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final io.github.jan.supabase.gotrue.Auth getAuth() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final io.github.jan.supabase.postgrest.Postgrest getPostgrest() {
        return null;
    }
    
    public final boolean isConfigured() {
        return false;
    }
}