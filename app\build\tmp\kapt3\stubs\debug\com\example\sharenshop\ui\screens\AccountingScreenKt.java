package com.example.sharenshop.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00004\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0012\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\b\u0010\u0004\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0005\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0006\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0007\u001a\u00020\u0001H\u0007\u001a\b\u0010\b\u001a\u00020\u0001H\u0007\u001a\b\u0010\t\u001a\u00020\u0001H\u0007\u001a<\u0010\n\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\f2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u0013H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0014\u0010\u0015\u001a \u0010\u0016\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\u0017\u001a\u00020\f2\u0006\u0010\u000e\u001a\u00020\u000fH\u0007\u001a \u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0019\u001a\u00020\f2\u0006\u0010\u001a\u001a\u00020\f2\u0006\u0010\u001b\u001a\u00020\fH\u0007\u001a(\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u0017\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\f2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\fH\u0007\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006 "}, d2 = {"AccountingScreen", "", "userRole", "Lcom/example/sharenshop/data/model/UserRole;", "AdminAccountsTab", "AdminFinancialDashboardTab", "AdminReportsTab", "AdminTransactionsTab", "CashierMyAccountTab", "CashierMyTransactionsTab", "FinancialSummaryCard", "title", "", "amount", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "color", "Landroidx/compose/ui/graphics/Color;", "modifier", "Landroidx/compose/ui/Modifier;", "FinancialSummaryCard-42QJj7c", "(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/graphics/vector/ImageVector;JLandroidx/compose/ui/Modifier;)V", "ReportButton", "description", "SellerBalanceItem", "sellerName", "totalReceived", "pendingSettlement", "TransactionItem", "type", "Lcom/example/sharenshop/data/model/TransactionCategory;", "date", "app_debug"})
public final class AccountingScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AccountingScreen(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AdminFinancialDashboardTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TransactionItem(@org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.lang.String amount, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.TransactionCategory type, @org.jetbrains.annotations.NotNull()
    java.lang.String date) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SellerBalanceItem(@org.jetbrains.annotations.NotNull()
    java.lang.String sellerName, @org.jetbrains.annotations.NotNull()
    java.lang.String totalReceived, @org.jetbrains.annotations.NotNull()
    java.lang.String pendingSettlement) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AdminTransactionsTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AdminAccountsTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AdminReportsTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ReportButton(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.graphics.vector.ImageVector icon) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CashierMyAccountTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CashierMyTransactionsTab() {
    }
}