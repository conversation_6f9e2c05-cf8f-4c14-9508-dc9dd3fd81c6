// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.local.dao.InvoiceItemDao;
import com.example.sharenshop.data.local.database.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaoModule_ProvideInvoiceItemDaoFactory implements Factory<InvoiceItemDao> {
  private final Provider<AppDatabase> appDatabaseProvider;

  public DaoModule_ProvideInvoiceItemDaoFactory(Provider<AppDatabase> appDatabaseProvider) {
    this.appDatabaseProvider = appDatabaseProvider;
  }

  @Override
  public InvoiceItemDao get() {
    return provideInvoiceItemDao(appDatabaseProvider.get());
  }

  public static DaoModule_ProvideInvoiceItemDaoFactory create(
      Provider<AppDatabase> appDatabaseProvider) {
    return new DaoModule_ProvideInvoiceItemDaoFactory(appDatabaseProvider);
  }

  public static InvoiceItemDao provideInvoiceItemDao(AppDatabase appDatabase) {
    return Preconditions.checkNotNullFromProvides(DaoModule.INSTANCE.provideInvoiceItemDao(appDatabase));
  }
}
