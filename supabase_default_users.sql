-- اضافه کردن کاربران پیش‌فرض به Supabase
-- این اسکریپت کاربران را در Authentication و جدول users ایجاد می‌کند

-- ابتدا کاربران را در auth.users ایجاد می‌کنیم
INSERT INTO auth.users (
    instance_id,
    id,
    aud,
    role,
    email,
    encrypted_password,
    email_confirmed_at,
    recovery_sent_at,
    last_sign_in_at,
    raw_app_meta_data,
    raw_user_meta_data,
    created_at,
    updated_at,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
) VALUES
-- مدیر اصلی
(
    '00000000-0000-0000-0000-000000000000',
    '00000000-0000-0000-0000-000000000001',
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('Admin123!@#', gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{}',
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
),
-- مدیر دوم
(
    '00000000-0000-0000-0000-000000000000',
    '00000000-0000-0000-0000-000000000005',
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('Manager123!@#', gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{}',
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
),
-- فروشنده احمد
(
    '00000000-0000-0000-0000-000000000000',
    '00000000-0000-0000-0000-000000000002',
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('Ahmad123!@#', gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{}',
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
),
-- فروشنده علی
(
    '00000000-0000-0000-0000-000000000000',
    '00000000-0000-0000-0000-000000000003',
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('Ali123!@#', gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{}',
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
),
-- فروشنده فاطمه
(
    '00000000-0000-0000-0000-000000000000',
    '00000000-0000-0000-0000-000000000004',
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('Fateme123!@#', gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{}',
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
)
ON CONFLICT (email) DO NOTHING;

-- حالا کاربران را در جدول public.users اضافه می‌کنیم
-- 1. مدیران کل
INSERT INTO public.users (id, email, username, user_type) VALUES
('00000000-0000-0000-0000-000000000001', '<EMAIL>', 'admin', 'super_admin'),
('00000000-0000-0000-0000-000000000005', '<EMAIL>', 'manager', 'super_admin')
ON CONFLICT (email) DO UPDATE SET
    username = EXCLUDED.username,
    user_type = EXCLUDED.user_type;

-- 2. فروشندگان نمونه
INSERT INTO users (id, email, username, user_type) VALUES 
('00000000-0000-0000-0000-000000000002', '<EMAIL>', 'ahmad_seller', 'cashier'),
('00000000-0000-0000-0000-000000000003', '<EMAIL>', 'ali_seller', 'cashier'),
('00000000-0000-0000-0000-000000000004', '<EMAIL>', 'fateme_seller', 'cashier')
ON CONFLICT (email) DO UPDATE SET 
    username = EXCLUDED.username,
    user_type = EXCLUDED.user_type;

-- 3. محصولات نمونه
INSERT INTO products (name, description, price, stock) VALUES 
('لپ تاپ ایسوس', 'لپ تاپ ایسوس مدل X515', 15000000, 10),
('گوشی سامسونگ', 'گوشی سامسونگ گلکسی A54', 8000000, 25),
('هدفون بلوتوثی', 'هدفون بی‌سیم سونی', 1500000, 50),
('ماوس گیمینگ', 'ماوس گیمینگ لاجیتک', 800000, 30),
('کیبورد مکانیکی', 'کیبورد مکانیکی ریزر', 2500000, 15);

-- 4. مشتریان نمونه
INSERT INTO customers (name, phone, email, balance) VALUES 
('محمد حسینی', '09123456789', '<EMAIL>', 500000),
('زهرا احمدی', '09987654321', '<EMAIL>', 750000),
('رضا محمدی', '09111222333', '<EMAIL>', 300000);

-- 5. فروشندگان (جدول جداگانه)
INSERT INTO sellers (name, contact_phone, contact_email, commission_rate) VALUES 
('احمد محمدی', '09123456789', '<EMAIL>', 5.00),
('علی رضایی', '09987654321', '<EMAIL>', 4.50),
('فاطمه احمدی', '09111222333', '<EMAIL>', 4.75);

-- 6. فاکتورهای نمونه
INSERT INTO invoices (customer_id, user_id, total_amount, discount_amount, final_amount, payment_status, payment_type) 
SELECT 
    c.id,
    u.id,
    5000000,
    250000,
    4750000,
    'paid',
    'cash'
FROM customers c, users u 
WHERE c.email = '<EMAIL>' AND u.username = 'ahmad_seller'
LIMIT 1;

INSERT INTO invoices (customer_id, user_id, total_amount, discount_amount, final_amount, payment_status, payment_type) 
SELECT 
    c.id,
    u.id,
    3200000,
    0,
    3200000,
    'pending',
    'credit'
FROM customers c, users u 
WHERE c.email = '<EMAIL>' AND u.username = 'ali_seller'
LIMIT 1;

-- 7. آیتم‌های فاکتور نمونه
INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, total_price)
SELECT 
    i.id,
    p.id,
    1,
    p.price,
    p.price
FROM invoices i, products p
WHERE i.final_amount = 4750000 AND p.name = 'لپ تاپ ایسوس'
LIMIT 1;

INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, total_price)
SELECT 
    i.id,
    p.id,
    2,
    p.price,
    p.price * 2
FROM invoices i, products p
WHERE i.final_amount = 3200000 AND p.name = 'هدفون بلوتوثی'
LIMIT 1;
