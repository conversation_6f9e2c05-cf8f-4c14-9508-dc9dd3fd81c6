package com.example.sharenshop.domain.use_case

import com.example.sharenshop.data.model.AppSettings
import com.example.sharenshop.domain.repository.SettingsRepository
import kotlinx.coroutines.flow.Flow

// Grouping all settings-related use cases
data class SettingsUseCases(
    val getAppSettings: GetAppSettings,
    val saveAppSettings: SaveAppSettings
)

class GetAppSettings(private val repository: SettingsRepository) {
    operator fun invoke(): Flow<AppSettings> {
        return repository.getAppSettings()
    }
}

class SaveAppSettings(private val repository: SettingsRepository) {
    suspend operator fun invoke(settings: AppSettings) {
        repository.saveAppSettings(settings)
    }
} 