package com.example.sharenshop.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable
import kotlinx.serialization.Contextual // این خط را اضافه کن
import java.math.BigDecimal

@Serializable
@Entity(tableName = "products")
data class Product(
    @PrimaryKey(autoGenerate = false)
    val id: String,
    val name: String,
    val description: String? = null,
    @Contextual
    val price: BigDecimal, // Use BigDecimal for monetary values
    val stock: Int,
    val imageUrl: String? = null,
    val createdAt: Long,
    val updatedAt: Long? = null
)