package com.example.sharenshop.ui.navigation;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\f\u0007\b\t\n\u000b\f\r\u000e\u000f\u0010\u0011\u0012B\u000f\b\u0004\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u0082\u0001\f\u0013\u0014\u0015\u0016\u0017\u0018\u0019\u001a\u001b\u001c\u001d\u001e\u00a8\u0006\u001f"}, d2 = {"Lcom/example/sharenshop/ui/navigation/Screen;", "", "route", "", "(Ljava/lang/String;)V", "getRoute", "()Ljava/lang/String;", "Accounting", "Auth", "Home", "Invoice", "Main", "Payment", "PendingApproval", "Product", "Seller", "Settlement", "Statistics", "UserManagement", "Lcom/example/sharenshop/ui/navigation/Screen$Accounting;", "Lcom/example/sharenshop/ui/navigation/Screen$Auth;", "Lcom/example/sharenshop/ui/navigation/Screen$Home;", "Lcom/example/sharenshop/ui/navigation/Screen$Invoice;", "Lcom/example/sharenshop/ui/navigation/Screen$Main;", "Lcom/example/sharenshop/ui/navigation/Screen$Payment;", "Lcom/example/sharenshop/ui/navigation/Screen$PendingApproval;", "Lcom/example/sharenshop/ui/navigation/Screen$Product;", "Lcom/example/sharenshop/ui/navigation/Screen$Seller;", "Lcom/example/sharenshop/ui/navigation/Screen$Settlement;", "Lcom/example/sharenshop/ui/navigation/Screen$Statistics;", "Lcom/example/sharenshop/ui/navigation/Screen$UserManagement;", "app_debug"})
public abstract class Screen {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String route = null;
    
    private Screen(java.lang.String route) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getRoute() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharenshop/ui/navigation/Screen$Accounting;", "Lcom/example/sharenshop/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Accounting extends com.example.sharenshop.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.sharenshop.ui.navigation.Screen.Accounting INSTANCE = null;
        
        private Accounting() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharenshop/ui/navigation/Screen$Auth;", "Lcom/example/sharenshop/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Auth extends com.example.sharenshop.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.sharenshop.ui.navigation.Screen.Auth INSTANCE = null;
        
        private Auth() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharenshop/ui/navigation/Screen$Home;", "Lcom/example/sharenshop/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Home extends com.example.sharenshop.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.sharenshop.ui.navigation.Screen.Home INSTANCE = null;
        
        private Home() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharenshop/ui/navigation/Screen$Invoice;", "Lcom/example/sharenshop/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Invoice extends com.example.sharenshop.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.sharenshop.ui.navigation.Screen.Invoice INSTANCE = null;
        
        private Invoice() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharenshop/ui/navigation/Screen$Main;", "Lcom/example/sharenshop/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Main extends com.example.sharenshop.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.sharenshop.ui.navigation.Screen.Main INSTANCE = null;
        
        private Main() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharenshop/ui/navigation/Screen$Payment;", "Lcom/example/sharenshop/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Payment extends com.example.sharenshop.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.sharenshop.ui.navigation.Screen.Payment INSTANCE = null;
        
        private Payment() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharenshop/ui/navigation/Screen$PendingApproval;", "Lcom/example/sharenshop/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class PendingApproval extends com.example.sharenshop.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.sharenshop.ui.navigation.Screen.PendingApproval INSTANCE = null;
        
        private PendingApproval() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharenshop/ui/navigation/Screen$Product;", "Lcom/example/sharenshop/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Product extends com.example.sharenshop.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.sharenshop.ui.navigation.Screen.Product INSTANCE = null;
        
        private Product() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharenshop/ui/navigation/Screen$Seller;", "Lcom/example/sharenshop/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Seller extends com.example.sharenshop.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.sharenshop.ui.navigation.Screen.Seller INSTANCE = null;
        
        private Seller() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharenshop/ui/navigation/Screen$Settlement;", "Lcom/example/sharenshop/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Settlement extends com.example.sharenshop.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.sharenshop.ui.navigation.Screen.Settlement INSTANCE = null;
        
        private Settlement() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharenshop/ui/navigation/Screen$Statistics;", "Lcom/example/sharenshop/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class Statistics extends com.example.sharenshop.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.sharenshop.ui.navigation.Screen.Statistics INSTANCE = null;
        
        private Statistics() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharenshop/ui/navigation/Screen$UserManagement;", "Lcom/example/sharenshop/ui/navigation/Screen;", "()V", "app_debug"})
    public static final class UserManagement extends com.example.sharenshop.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.sharenshop.ui.navigation.Screen.UserManagement INSTANCE = null;
        
        private UserManagement() {
        }
    }
}