  Activity android.app  Application android.app  Bundle android.app.Activity  Context android.content  SharedPreferences android.content  Bundle android.content.Context  MODE_PRIVATE android.content.Context  getSharedPreferences android.content.Context  Bundle android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  Log android.util  Bundle  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  
Composable androidx.compose.animation.core  Canvas androidx.compose.foundation  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  Invoice "androidx.compose.foundation.layout  PaymentDetails "androidx.compose.foundation.layout  
PaymentStatus "androidx.compose.foundation.layout  SettlementDetails "androidx.compose.foundation.layout  SettlementStatus "androidx.compose.foundation.layout  TransactionCategory "androidx.compose.foundation.layout  UserRole "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  	GridCells %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  
Composable &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  Invoice &androidx.compose.material.icons.filled  PaymentDetails &androidx.compose.material.icons.filled  
PaymentStatus &androidx.compose.material.icons.filled  SettlementDetails &androidx.compose.material.icons.filled  SettlementStatus &androidx.compose.material.icons.filled  TransactionCategory &androidx.compose.material.icons.filled  UserRole &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExposedDropdownMenuDefaults androidx.compose.material3  
FilterChip androidx.compose.material3  Invoice androidx.compose.material3  
MaterialTheme androidx.compose.material3  PaymentDetails androidx.compose.material3  
PaymentStatus androidx.compose.material3  SettlementDetails androidx.compose.material3  SettlementStatus androidx.compose.material3  Surface androidx.compose.material3  Tab androidx.compose.material3  TabRow androidx.compose.material3  Text androidx.compose.material3  TransactionCategory androidx.compose.material3  
Typography androidx.compose.material3  UserRole androidx.compose.material3  androidx androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  SharenShopColors &androidx.compose.material3.ColorScheme  
Composable androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  Invoice androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  PaymentDetails androidx.compose.runtime  
PaymentStatus androidx.compose.runtime  SettlementDetails androidx.compose.runtime  SettlementStatus androidx.compose.runtime  
SideEffect androidx.compose.runtime  TransactionCategory androidx.compose.runtime  UserRole androidx.compose.runtime  androidx androidx.compose.runtime  collectAsState androidx.compose.runtime  getValue androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  clip androidx.compose.ui.draw  rotate androidx.compose.ui.draw  scale androidx.compose.ui.draw  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  	DrawScope &androidx.compose.ui.graphics.drawscope  ImageVector #androidx.compose.ui.graphics.vector  LocalConfiguration androidx.compose.ui.platform  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  PasswordVisualTransformation androidx.compose.ui.text.input  	TextAlign androidx.compose.ui.text.style  TextOverflow androidx.compose.ui.text.style  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Dialog androidx.compose.ui.window  Bundle #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  	dataStore androidx.datastore  	DataStore androidx.datastore.core  
Serializer androidx.datastore.core  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  
BigDecimal androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  Invoice androidx.lifecycle.ViewModel  InvoiceUseCases androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  Map androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  Product androidx.lifecycle.ViewModel  ProductUseCases androidx.lifecycle.ViewModel  Seller androidx.lifecycle.ViewModel  SellerUseCases androidx.lifecycle.ViewModel  SessionManager androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  StatisticsUseCases androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  User androidx.lifecycle.ViewModel  UserRole androidx.lifecycle.ViewModel  UserUseCases androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  emptyMap androidx.lifecycle.ViewModel  collectAsStateWithLifecycle androidx.lifecycle.compose  NavHostController androidx.navigation  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  CustomerDao androidx.room.RoomDatabase  
InvoiceDao androidx.room.RoomDatabase  InvoiceItemDao androidx.room.RoomDatabase  
ProductDao androidx.room.RoomDatabase  	SellerDao androidx.room.RoomDatabase  UserDao androidx.room.RoomDatabase  BuildConfig com.example.sharenshop  MainActivity com.example.sharenshop  SharenShopApplication com.example.sharenshop  SUPABASE_PUBLIC_KEY "com.example.sharenshop.BuildConfig  SUPABASE_URL "com.example.sharenshop.BuildConfig  Bundle #com.example.sharenshop.MainActivity  AppSettings %com.example.sharenshop.data.datastore  AppSettingsSerializer %com.example.sharenshop.data.datastore  AppSettings ;com.example.sharenshop.data.datastore.AppSettingsSerializer  InputStream ;com.example.sharenshop.data.datastore.AppSettingsSerializer  OutputStream ;com.example.sharenshop.data.datastore.AppSettingsSerializer  invoke ;com.example.sharenshop.data.datastore.AppSettingsSerializer  Boolean !com.example.sharenshop.data.local  Context !com.example.sharenshop.data.local  Long !com.example.sharenshop.data.local  
PREFS_NAME !com.example.sharenshop.data.local  SessionManager !com.example.sharenshop.data.local  String !com.example.sharenshop.data.local  ApplicationContext 0com.example.sharenshop.data.local.SessionManager  Boolean 0com.example.sharenshop.data.local.SessionManager  Context 0com.example.sharenshop.data.local.SessionManager  Inject 0com.example.sharenshop.data.local.SessionManager  Long 0com.example.sharenshop.data.local.SessionManager  
PREFS_NAME 0com.example.sharenshop.data.local.SessionManager  SharedPreferences 0com.example.sharenshop.data.local.SessionManager  String 0com.example.sharenshop.data.local.SessionManager  UserRole 0com.example.sharenshop.data.local.SessionManager  context 0com.example.sharenshop.data.local.SessionManager  ApplicationContext :com.example.sharenshop.data.local.SessionManager.Companion  Boolean :com.example.sharenshop.data.local.SessionManager.Companion  Context :com.example.sharenshop.data.local.SessionManager.Companion  Inject :com.example.sharenshop.data.local.SessionManager.Companion  Long :com.example.sharenshop.data.local.SessionManager.Companion  
PREFS_NAME :com.example.sharenshop.data.local.SessionManager.Companion  SharedPreferences :com.example.sharenshop.data.local.SessionManager.Companion  String :com.example.sharenshop.data.local.SessionManager.Companion  UserRole :com.example.sharenshop.data.local.SessionManager.Companion  BigDecimalConverter +com.example.sharenshop.data.local.converter  String +com.example.sharenshop.data.local.converter  
BigDecimal ?com.example.sharenshop.data.local.converter.BigDecimalConverter  String ?com.example.sharenshop.data.local.converter.BigDecimalConverter  
TypeConverter ?com.example.sharenshop.data.local.converter.BigDecimalConverter  CustomerApprovalDao %com.example.sharenshop.data.local.dao  CustomerDao %com.example.sharenshop.data.local.dao  Dao %com.example.sharenshop.data.local.dao  Delete %com.example.sharenshop.data.local.dao  Insert %com.example.sharenshop.data.local.dao  Int %com.example.sharenshop.data.local.dao  
InvoiceDao %com.example.sharenshop.data.local.dao  InvoiceItemDao %com.example.sharenshop.data.local.dao  List %com.example.sharenshop.data.local.dao  Long %com.example.sharenshop.data.local.dao  NotificationDao %com.example.sharenshop.data.local.dao  OnConflictStrategy %com.example.sharenshop.data.local.dao  
ProductDao %com.example.sharenshop.data.local.dao  Query %com.example.sharenshop.data.local.dao  	SellerDao %com.example.sharenshop.data.local.dao  String %com.example.sharenshop.data.local.dao  Update %com.example.sharenshop.data.local.dao  UserDao %com.example.sharenshop.data.local.dao  ApprovalStatus 9com.example.sharenshop.data.local.dao.CustomerApprovalDao  CustomerApprovalRequest 9com.example.sharenshop.data.local.dao.CustomerApprovalDao  Delete 9com.example.sharenshop.data.local.dao.CustomerApprovalDao  Flow 9com.example.sharenshop.data.local.dao.CustomerApprovalDao  Insert 9com.example.sharenshop.data.local.dao.CustomerApprovalDao  Int 9com.example.sharenshop.data.local.dao.CustomerApprovalDao  List 9com.example.sharenshop.data.local.dao.CustomerApprovalDao  Long 9com.example.sharenshop.data.local.dao.CustomerApprovalDao  OnConflictStrategy 9com.example.sharenshop.data.local.dao.CustomerApprovalDao  Query 9com.example.sharenshop.data.local.dao.CustomerApprovalDao  String 9com.example.sharenshop.data.local.dao.CustomerApprovalDao  Update 9com.example.sharenshop.data.local.dao.CustomerApprovalDao  Customer 1com.example.sharenshop.data.local.dao.CustomerDao  Flow 1com.example.sharenshop.data.local.dao.CustomerDao  Insert 1com.example.sharenshop.data.local.dao.CustomerDao  List 1com.example.sharenshop.data.local.dao.CustomerDao  OnConflictStrategy 1com.example.sharenshop.data.local.dao.CustomerDao  Query 1com.example.sharenshop.data.local.dao.CustomerDao  String 1com.example.sharenshop.data.local.dao.CustomerDao  Update 1com.example.sharenshop.data.local.dao.CustomerDao  Flow 0com.example.sharenshop.data.local.dao.InvoiceDao  Insert 0com.example.sharenshop.data.local.dao.InvoiceDao  Invoice 0com.example.sharenshop.data.local.dao.InvoiceDao  List 0com.example.sharenshop.data.local.dao.InvoiceDao  OnConflictStrategy 0com.example.sharenshop.data.local.dao.InvoiceDao  Query 0com.example.sharenshop.data.local.dao.InvoiceDao  String 0com.example.sharenshop.data.local.dao.InvoiceDao  Update 0com.example.sharenshop.data.local.dao.InvoiceDao  Flow 4com.example.sharenshop.data.local.dao.InvoiceItemDao  Insert 4com.example.sharenshop.data.local.dao.InvoiceItemDao  InvoiceItem 4com.example.sharenshop.data.local.dao.InvoiceItemDao  List 4com.example.sharenshop.data.local.dao.InvoiceItemDao  OnConflictStrategy 4com.example.sharenshop.data.local.dao.InvoiceItemDao  Query 4com.example.sharenshop.data.local.dao.InvoiceItemDao  String 4com.example.sharenshop.data.local.dao.InvoiceItemDao  Update 4com.example.sharenshop.data.local.dao.InvoiceItemDao  Delete 5com.example.sharenshop.data.local.dao.NotificationDao  Flow 5com.example.sharenshop.data.local.dao.NotificationDao  Insert 5com.example.sharenshop.data.local.dao.NotificationDao  Int 5com.example.sharenshop.data.local.dao.NotificationDao  List 5com.example.sharenshop.data.local.dao.NotificationDao  Long 5com.example.sharenshop.data.local.dao.NotificationDao  Notification 5com.example.sharenshop.data.local.dao.NotificationDao  NotificationType 5com.example.sharenshop.data.local.dao.NotificationDao  OnConflictStrategy 5com.example.sharenshop.data.local.dao.NotificationDao  Query 5com.example.sharenshop.data.local.dao.NotificationDao  String 5com.example.sharenshop.data.local.dao.NotificationDao  Update 5com.example.sharenshop.data.local.dao.NotificationDao  Flow 0com.example.sharenshop.data.local.dao.ProductDao  Insert 0com.example.sharenshop.data.local.dao.ProductDao  List 0com.example.sharenshop.data.local.dao.ProductDao  OnConflictStrategy 0com.example.sharenshop.data.local.dao.ProductDao  Product 0com.example.sharenshop.data.local.dao.ProductDao  Query 0com.example.sharenshop.data.local.dao.ProductDao  String 0com.example.sharenshop.data.local.dao.ProductDao  Update 0com.example.sharenshop.data.local.dao.ProductDao  Flow /com.example.sharenshop.data.local.dao.SellerDao  Insert /com.example.sharenshop.data.local.dao.SellerDao  List /com.example.sharenshop.data.local.dao.SellerDao  OnConflictStrategy /com.example.sharenshop.data.local.dao.SellerDao  Query /com.example.sharenshop.data.local.dao.SellerDao  Seller /com.example.sharenshop.data.local.dao.SellerDao  String /com.example.sharenshop.data.local.dao.SellerDao  Update /com.example.sharenshop.data.local.dao.SellerDao  Flow -com.example.sharenshop.data.local.dao.UserDao  Insert -com.example.sharenshop.data.local.dao.UserDao  List -com.example.sharenshop.data.local.dao.UserDao  OnConflictStrategy -com.example.sharenshop.data.local.dao.UserDao  Query -com.example.sharenshop.data.local.dao.UserDao  String -com.example.sharenshop.data.local.dao.UserDao  Update -com.example.sharenshop.data.local.dao.UserDao  User -com.example.sharenshop.data.local.dao.UserDao  AppDatabase *com.example.sharenshop.data.local.database  BigDecimalConverter *com.example.sharenshop.data.local.database  CustomerDao 6com.example.sharenshop.data.local.database.AppDatabase  
InvoiceDao 6com.example.sharenshop.data.local.database.AppDatabase  InvoiceItemDao 6com.example.sharenshop.data.local.database.AppDatabase  
ProductDao 6com.example.sharenshop.data.local.database.AppDatabase  	SellerDao 6com.example.sharenshop.data.local.database.AppDatabase  UserDao 6com.example.sharenshop.data.local.database.AppDatabase  Account !com.example.sharenshop.data.model  AccountOwnerType !com.example.sharenshop.data.model  AccountType !com.example.sharenshop.data.model  AppSettings !com.example.sharenshop.data.model  ApprovalStatus !com.example.sharenshop.data.model  Boolean !com.example.sharenshop.data.model  
Composable !com.example.sharenshop.data.model  Customer !com.example.sharenshop.data.model  CustomerApprovalRequest !com.example.sharenshop.data.model  CustomerApprovalRequestDetails !com.example.sharenshop.data.model  DailySalesStatistics !com.example.sharenshop.data.model  Double !com.example.sharenshop.data.model  EncryptionSettings !com.example.sharenshop.data.model  ExperimentalMaterial3Api !com.example.sharenshop.data.model  FinancialReport !com.example.sharenshop.data.model  Int !com.example.sharenshop.data.model  Invoice !com.example.sharenshop.data.model  InvoiceItem !com.example.sharenshop.data.model  InvoicePaymentStatus !com.example.sharenshop.data.model  InvoicePaymentType !com.example.sharenshop.data.model  List !com.example.sharenshop.data.model  Long !com.example.sharenshop.data.model  MonthlySalesStatistics !com.example.sharenshop.data.model  Notification !com.example.sharenshop.data.model  NotificationType !com.example.sharenshop.data.model  OverallStatistics !com.example.sharenshop.data.model  Payment !com.example.sharenshop.data.model  PaymentDetails !com.example.sharenshop.data.model  
PaymentMethod !com.example.sharenshop.data.model  
PaymentStatus !com.example.sharenshop.data.model  
Permission !com.example.sharenshop.data.model  Product !com.example.sharenshop.data.model  Result !com.example.sharenshop.data.model  RolePermissions !com.example.sharenshop.data.model  Seller !com.example.sharenshop.data.model  SellerComparison !com.example.sharenshop.data.model  SellerSettlementSummary !com.example.sharenshop.data.model  SellerStatistics !com.example.sharenshop.data.model  Set !com.example.sharenshop.data.model  
Settlement !com.example.sharenshop.data.model  SettlementDetails !com.example.sharenshop.data.model  SettlementStatus !com.example.sharenshop.data.model  SettlementType !com.example.sharenshop.data.model  String !com.example.sharenshop.data.model  TopCustomer !com.example.sharenshop.data.model  TopSellingProduct !com.example.sharenshop.data.model  Transaction !com.example.sharenshop.data.model  TransactionCategory !com.example.sharenshop.data.model  TransactionType !com.example.sharenshop.data.model  User !com.example.sharenshop.data.model  UserRole !com.example.sharenshop.data.model  androidx !com.example.sharenshop.data.model  setOf !com.example.sharenshop.data.model  AccountOwnerType )com.example.sharenshop.data.model.Account  AccountType )com.example.sharenshop.data.model.Account  
BigDecimal )com.example.sharenshop.data.model.Account  Boolean )com.example.sharenshop.data.model.Account  
Contextual )com.example.sharenshop.data.model.Account  Long )com.example.sharenshop.data.model.Account  
PrimaryKey )com.example.sharenshop.data.model.Account  String )com.example.sharenshop.data.model.Account  AccountOwnerType 3com.example.sharenshop.data.model.Account.Companion  AccountType 3com.example.sharenshop.data.model.Account.Companion  
BigDecimal 3com.example.sharenshop.data.model.Account.Companion  Boolean 3com.example.sharenshop.data.model.Account.Companion  
Contextual 3com.example.sharenshop.data.model.Account.Companion  Long 3com.example.sharenshop.data.model.Account.Companion  
PrimaryKey 3com.example.sharenshop.data.model.Account.Companion  String 3com.example.sharenshop.data.model.Account.Companion  AccountOwnerType 2com.example.sharenshop.data.model.AccountOwnerType  String 2com.example.sharenshop.data.model.AccountOwnerType  AccountOwnerType <com.example.sharenshop.data.model.AccountOwnerType.Companion  String <com.example.sharenshop.data.model.AccountOwnerType.Companion  AccountType -com.example.sharenshop.data.model.AccountType  String -com.example.sharenshop.data.model.AccountType  AccountType 7com.example.sharenshop.data.model.AccountType.Companion  String 7com.example.sharenshop.data.model.AccountType.Companion  Boolean -com.example.sharenshop.data.model.AppSettings  Long -com.example.sharenshop.data.model.AppSettings  String -com.example.sharenshop.data.model.AppSettings  Boolean 7com.example.sharenshop.data.model.AppSettings.Companion  Long 7com.example.sharenshop.data.model.AppSettings.Companion  String 7com.example.sharenshop.data.model.AppSettings.Companion  invoke 7com.example.sharenshop.data.model.AppSettings.Companion  ApprovalStatus 0com.example.sharenshop.data.model.ApprovalStatus  String 0com.example.sharenshop.data.model.ApprovalStatus  ApprovalStatus :com.example.sharenshop.data.model.ApprovalStatus.Companion  String :com.example.sharenshop.data.model.ApprovalStatus.Companion  
BigDecimal *com.example.sharenshop.data.model.Customer  	Companion *com.example.sharenshop.data.model.Customer  
Contextual *com.example.sharenshop.data.model.Customer  Long *com.example.sharenshop.data.model.Customer  
PrimaryKey *com.example.sharenshop.data.model.Customer  String *com.example.sharenshop.data.model.Customer  
BigDecimal 4com.example.sharenshop.data.model.Customer.Companion  
Contextual 4com.example.sharenshop.data.model.Customer.Companion  Long 4com.example.sharenshop.data.model.Customer.Companion  
PrimaryKey 4com.example.sharenshop.data.model.Customer.Companion  String 4com.example.sharenshop.data.model.Customer.Companion  ApprovalStatus 9com.example.sharenshop.data.model.CustomerApprovalRequest  Long 9com.example.sharenshop.data.model.CustomerApprovalRequest  
PrimaryKey 9com.example.sharenshop.data.model.CustomerApprovalRequest  String 9com.example.sharenshop.data.model.CustomerApprovalRequest  ApprovalStatus Ccom.example.sharenshop.data.model.CustomerApprovalRequest.Companion  Long Ccom.example.sharenshop.data.model.CustomerApprovalRequest.Companion  
PrimaryKey Ccom.example.sharenshop.data.model.CustomerApprovalRequest.Companion  String Ccom.example.sharenshop.data.model.CustomerApprovalRequest.Companion  CustomerApprovalRequest @com.example.sharenshop.data.model.CustomerApprovalRequestDetails  String @com.example.sharenshop.data.model.CustomerApprovalRequestDetails  
BigDecimal 6com.example.sharenshop.data.model.DailySalesStatistics  
Contextual 6com.example.sharenshop.data.model.DailySalesStatistics  Int 6com.example.sharenshop.data.model.DailySalesStatistics  String 6com.example.sharenshop.data.model.DailySalesStatistics  
BigDecimal @com.example.sharenshop.data.model.DailySalesStatistics.Companion  
Contextual @com.example.sharenshop.data.model.DailySalesStatistics.Companion  Int @com.example.sharenshop.data.model.DailySalesStatistics.Companion  String @com.example.sharenshop.data.model.DailySalesStatistics.Companion  Boolean 4com.example.sharenshop.data.model.EncryptionSettings  Boolean >com.example.sharenshop.data.model.EncryptionSettings.Companion  invoke >com.example.sharenshop.data.model.EncryptionSettings.Companion  
BigDecimal 1com.example.sharenshop.data.model.FinancialReport  
Contextual 1com.example.sharenshop.data.model.FinancialReport  Long 1com.example.sharenshop.data.model.FinancialReport  
BigDecimal )com.example.sharenshop.data.model.Invoice  	Companion )com.example.sharenshop.data.model.Invoice  
Contextual )com.example.sharenshop.data.model.Invoice  Long )com.example.sharenshop.data.model.Invoice  
PrimaryKey )com.example.sharenshop.data.model.Invoice  String )com.example.sharenshop.data.model.Invoice  
BigDecimal 3com.example.sharenshop.data.model.Invoice.Companion  
Contextual 3com.example.sharenshop.data.model.Invoice.Companion  Long 3com.example.sharenshop.data.model.Invoice.Companion  
PrimaryKey 3com.example.sharenshop.data.model.Invoice.Companion  String 3com.example.sharenshop.data.model.Invoice.Companion  
BigDecimal -com.example.sharenshop.data.model.InvoiceItem  	Companion -com.example.sharenshop.data.model.InvoiceItem  
Contextual -com.example.sharenshop.data.model.InvoiceItem  Int -com.example.sharenshop.data.model.InvoiceItem  Long -com.example.sharenshop.data.model.InvoiceItem  
PrimaryKey -com.example.sharenshop.data.model.InvoiceItem  String -com.example.sharenshop.data.model.InvoiceItem  
BigDecimal 7com.example.sharenshop.data.model.InvoiceItem.Companion  
Contextual 7com.example.sharenshop.data.model.InvoiceItem.Companion  Int 7com.example.sharenshop.data.model.InvoiceItem.Companion  Long 7com.example.sharenshop.data.model.InvoiceItem.Companion  
PrimaryKey 7com.example.sharenshop.data.model.InvoiceItem.Companion  String 7com.example.sharenshop.data.model.InvoiceItem.Companion  InvoicePaymentStatus 6com.example.sharenshop.data.model.InvoicePaymentStatus  String 6com.example.sharenshop.data.model.InvoicePaymentStatus  InvoicePaymentStatus @com.example.sharenshop.data.model.InvoicePaymentStatus.Companion  String @com.example.sharenshop.data.model.InvoicePaymentStatus.Companion  InvoicePaymentType 4com.example.sharenshop.data.model.InvoicePaymentType  String 4com.example.sharenshop.data.model.InvoicePaymentType  InvoicePaymentType >com.example.sharenshop.data.model.InvoicePaymentType.Companion  String >com.example.sharenshop.data.model.InvoicePaymentType.Companion  
BigDecimal 8com.example.sharenshop.data.model.MonthlySalesStatistics  
Contextual 8com.example.sharenshop.data.model.MonthlySalesStatistics  Int 8com.example.sharenshop.data.model.MonthlySalesStatistics  String 8com.example.sharenshop.data.model.MonthlySalesStatistics  
BigDecimal Bcom.example.sharenshop.data.model.MonthlySalesStatistics.Companion  
Contextual Bcom.example.sharenshop.data.model.MonthlySalesStatistics.Companion  Int Bcom.example.sharenshop.data.model.MonthlySalesStatistics.Companion  String Bcom.example.sharenshop.data.model.MonthlySalesStatistics.Companion  Boolean .com.example.sharenshop.data.model.Notification  Long .com.example.sharenshop.data.model.Notification  NotificationType .com.example.sharenshop.data.model.Notification  
PrimaryKey .com.example.sharenshop.data.model.Notification  String .com.example.sharenshop.data.model.Notification  Boolean 8com.example.sharenshop.data.model.Notification.Companion  Long 8com.example.sharenshop.data.model.Notification.Companion  NotificationType 8com.example.sharenshop.data.model.Notification.Companion  
PrimaryKey 8com.example.sharenshop.data.model.Notification.Companion  String 8com.example.sharenshop.data.model.Notification.Companion  NotificationType 2com.example.sharenshop.data.model.NotificationType  String 2com.example.sharenshop.data.model.NotificationType  NotificationType <com.example.sharenshop.data.model.NotificationType.Companion  String <com.example.sharenshop.data.model.NotificationType.Companion  
BigDecimal 3com.example.sharenshop.data.model.OverallStatistics  
Contextual 3com.example.sharenshop.data.model.OverallStatistics  Double 3com.example.sharenshop.data.model.OverallStatistics  Int 3com.example.sharenshop.data.model.OverallStatistics  Long 3com.example.sharenshop.data.model.OverallStatistics  String 3com.example.sharenshop.data.model.OverallStatistics  
BigDecimal =com.example.sharenshop.data.model.OverallStatistics.Companion  
Contextual =com.example.sharenshop.data.model.OverallStatistics.Companion  Double =com.example.sharenshop.data.model.OverallStatistics.Companion  Int =com.example.sharenshop.data.model.OverallStatistics.Companion  Long =com.example.sharenshop.data.model.OverallStatistics.Companion  String =com.example.sharenshop.data.model.OverallStatistics.Companion  
BigDecimal )com.example.sharenshop.data.model.Payment  
Contextual )com.example.sharenshop.data.model.Payment  Long )com.example.sharenshop.data.model.Payment  
PaymentMethod )com.example.sharenshop.data.model.Payment  
PaymentStatus )com.example.sharenshop.data.model.Payment  
PrimaryKey )com.example.sharenshop.data.model.Payment  String )com.example.sharenshop.data.model.Payment  
BigDecimal 3com.example.sharenshop.data.model.Payment.Companion  
Contextual 3com.example.sharenshop.data.model.Payment.Companion  Long 3com.example.sharenshop.data.model.Payment.Companion  
PaymentMethod 3com.example.sharenshop.data.model.Payment.Companion  
PaymentStatus 3com.example.sharenshop.data.model.Payment.Companion  
PrimaryKey 3com.example.sharenshop.data.model.Payment.Companion  String 3com.example.sharenshop.data.model.Payment.Companion  
BigDecimal 0com.example.sharenshop.data.model.PaymentDetails  Payment 0com.example.sharenshop.data.model.PaymentDetails  String 0com.example.sharenshop.data.model.PaymentDetails  
PaymentMethod /com.example.sharenshop.data.model.PaymentMethod  String /com.example.sharenshop.data.model.PaymentMethod  
PaymentMethod 9com.example.sharenshop.data.model.PaymentMethod.Companion  String 9com.example.sharenshop.data.model.PaymentMethod.Companion  
PaymentStatus /com.example.sharenshop.data.model.PaymentStatus  String /com.example.sharenshop.data.model.PaymentStatus  
PaymentStatus 9com.example.sharenshop.data.model.PaymentStatus.Companion  String 9com.example.sharenshop.data.model.PaymentStatus.Companion  BACKUP_RESTORE ,com.example.sharenshop.data.model.Permission  CREATE_CUSTOMERS ,com.example.sharenshop.data.model.Permission  CREATE_INVOICES ,com.example.sharenshop.data.model.Permission  CREATE_PRODUCTS ,com.example.sharenshop.data.model.Permission  CREATE_SELLERS ,com.example.sharenshop.data.model.Permission  CREATE_USERS ,com.example.sharenshop.data.model.Permission  DELETE_CUSTOMERS ,com.example.sharenshop.data.model.Permission  DELETE_INVOICES ,com.example.sharenshop.data.model.Permission  DELETE_PRODUCTS ,com.example.sharenshop.data.model.Permission  DELETE_SELLERS ,com.example.sharenshop.data.model.Permission  DELETE_USERS ,com.example.sharenshop.data.model.Permission  EDIT_CUSTOMERS ,com.example.sharenshop.data.model.Permission  
EDIT_INVOICES ,com.example.sharenshop.data.model.Permission  
EDIT_PRODUCTS ,com.example.sharenshop.data.model.Permission  EDIT_PRODUCT_PRICES ,com.example.sharenshop.data.model.Permission  EDIT_PRODUCT_STOCK ,com.example.sharenshop.data.model.Permission  EDIT_SELLERS ,com.example.sharenshop.data.model.Permission  
EDIT_USERS ,com.example.sharenshop.data.model.Permission  MANAGE_SECURITY ,com.example.sharenshop.data.model.Permission  MANAGE_SETTINGS ,com.example.sharenshop.data.model.Permission  
Permission ,com.example.sharenshop.data.model.Permission  String ,com.example.sharenshop.data.model.Permission  VIEW_ALL_INVOICES ,com.example.sharenshop.data.model.Permission  VIEW_ALL_SELLERS_STATS ,com.example.sharenshop.data.model.Permission  VIEW_CASH_SALES ,com.example.sharenshop.data.model.Permission  VIEW_CREDIT_SALES ,com.example.sharenshop.data.model.Permission  VIEW_CUSTOMERS ,com.example.sharenshop.data.model.Permission  VIEW_CUSTOMER_ADDITIONS ,com.example.sharenshop.data.model.Permission  VIEW_CUSTOMER_BALANCE ,com.example.sharenshop.data.model.Permission  VIEW_DETAILED_REPORTS ,com.example.sharenshop.data.model.Permission  VIEW_FINANCIAL_REPORTS ,com.example.sharenshop.data.model.Permission  
VIEW_INVOICES ,com.example.sharenshop.data.model.Permission  VIEW_INVOICE_COUNT ,com.example.sharenshop.data.model.Permission  VIEW_OWN_INVOICES ,com.example.sharenshop.data.model.Permission  
VIEW_PRODUCTS ,com.example.sharenshop.data.model.Permission  VIEW_SELLERS ,com.example.sharenshop.data.model.Permission  VIEW_STATISTICS ,com.example.sharenshop.data.model.Permission  
VIEW_USERS ,com.example.sharenshop.data.model.Permission  
BigDecimal )com.example.sharenshop.data.model.Product  	Companion )com.example.sharenshop.data.model.Product  
Contextual )com.example.sharenshop.data.model.Product  Int )com.example.sharenshop.data.model.Product  Long )com.example.sharenshop.data.model.Product  
PrimaryKey )com.example.sharenshop.data.model.Product  String )com.example.sharenshop.data.model.Product  
BigDecimal 3com.example.sharenshop.data.model.Product.Companion  
Contextual 3com.example.sharenshop.data.model.Product.Companion  Int 3com.example.sharenshop.data.model.Product.Companion  Long 3com.example.sharenshop.data.model.Product.Companion  
PrimaryKey 3com.example.sharenshop.data.model.Product.Companion  String 3com.example.sharenshop.data.model.Product.Companion  Boolean 1com.example.sharenshop.data.model.RolePermissions  
Permission 1com.example.sharenshop.data.model.RolePermissions  Set 1com.example.sharenshop.data.model.RolePermissions  UserRole 1com.example.sharenshop.data.model.RolePermissions  getSETOf 1com.example.sharenshop.data.model.RolePermissions  getSetOf 1com.example.sharenshop.data.model.RolePermissions  setOf 1com.example.sharenshop.data.model.RolePermissions  
BigDecimal (com.example.sharenshop.data.model.Seller  	Companion (com.example.sharenshop.data.model.Seller  
Contextual (com.example.sharenshop.data.model.Seller  Long (com.example.sharenshop.data.model.Seller  
PrimaryKey (com.example.sharenshop.data.model.Seller  String (com.example.sharenshop.data.model.Seller  
BigDecimal 2com.example.sharenshop.data.model.Seller.Companion  
Contextual 2com.example.sharenshop.data.model.Seller.Companion  Long 2com.example.sharenshop.data.model.Seller.Companion  
PrimaryKey 2com.example.sharenshop.data.model.Seller.Companion  String 2com.example.sharenshop.data.model.Seller.Companion  
BigDecimal 2com.example.sharenshop.data.model.SellerComparison  
Contextual 2com.example.sharenshop.data.model.SellerComparison  Int 2com.example.sharenshop.data.model.SellerComparison  String 2com.example.sharenshop.data.model.SellerComparison  
BigDecimal <com.example.sharenshop.data.model.SellerComparison.Companion  
Contextual <com.example.sharenshop.data.model.SellerComparison.Companion  Int <com.example.sharenshop.data.model.SellerComparison.Companion  String <com.example.sharenshop.data.model.SellerComparison.Companion  
BigDecimal 9com.example.sharenshop.data.model.SellerSettlementSummary  
Contextual 9com.example.sharenshop.data.model.SellerSettlementSummary  Long 9com.example.sharenshop.data.model.SellerSettlementSummary  String 9com.example.sharenshop.data.model.SellerSettlementSummary  
BigDecimal 2com.example.sharenshop.data.model.SellerStatistics  
Contextual 2com.example.sharenshop.data.model.SellerStatistics  Double 2com.example.sharenshop.data.model.SellerStatistics  Int 2com.example.sharenshop.data.model.SellerStatistics  Long 2com.example.sharenshop.data.model.SellerStatistics  String 2com.example.sharenshop.data.model.SellerStatistics  
BigDecimal <com.example.sharenshop.data.model.SellerStatistics.Companion  
Contextual <com.example.sharenshop.data.model.SellerStatistics.Companion  Double <com.example.sharenshop.data.model.SellerStatistics.Companion  Int <com.example.sharenshop.data.model.SellerStatistics.Companion  Long <com.example.sharenshop.data.model.SellerStatistics.Companion  String <com.example.sharenshop.data.model.SellerStatistics.Companion  
BigDecimal ,com.example.sharenshop.data.model.Settlement  
Contextual ,com.example.sharenshop.data.model.Settlement  Long ,com.example.sharenshop.data.model.Settlement  
PrimaryKey ,com.example.sharenshop.data.model.Settlement  SettlementStatus ,com.example.sharenshop.data.model.Settlement  SettlementType ,com.example.sharenshop.data.model.Settlement  String ,com.example.sharenshop.data.model.Settlement  
BigDecimal 6com.example.sharenshop.data.model.Settlement.Companion  
Contextual 6com.example.sharenshop.data.model.Settlement.Companion  Long 6com.example.sharenshop.data.model.Settlement.Companion  
PrimaryKey 6com.example.sharenshop.data.model.Settlement.Companion  SettlementStatus 6com.example.sharenshop.data.model.Settlement.Companion  SettlementType 6com.example.sharenshop.data.model.Settlement.Companion  String 6com.example.sharenshop.data.model.Settlement.Companion  List 3com.example.sharenshop.data.model.SettlementDetails  Payment 3com.example.sharenshop.data.model.SettlementDetails  
Settlement 3com.example.sharenshop.data.model.SettlementDetails  String 3com.example.sharenshop.data.model.SettlementDetails  SettlementStatus 2com.example.sharenshop.data.model.SettlementStatus  String 2com.example.sharenshop.data.model.SettlementStatus  SettlementStatus <com.example.sharenshop.data.model.SettlementStatus.Companion  String <com.example.sharenshop.data.model.SettlementStatus.Companion  SettlementType 0com.example.sharenshop.data.model.SettlementType  String 0com.example.sharenshop.data.model.SettlementType  SettlementType :com.example.sharenshop.data.model.SettlementType.Companion  String :com.example.sharenshop.data.model.SettlementType.Companion  
BigDecimal -com.example.sharenshop.data.model.TopCustomer  
Contextual -com.example.sharenshop.data.model.TopCustomer  Int -com.example.sharenshop.data.model.TopCustomer  Long -com.example.sharenshop.data.model.TopCustomer  String -com.example.sharenshop.data.model.TopCustomer  
BigDecimal 7com.example.sharenshop.data.model.TopCustomer.Companion  
Contextual 7com.example.sharenshop.data.model.TopCustomer.Companion  Int 7com.example.sharenshop.data.model.TopCustomer.Companion  Long 7com.example.sharenshop.data.model.TopCustomer.Companion  String 7com.example.sharenshop.data.model.TopCustomer.Companion  
BigDecimal 3com.example.sharenshop.data.model.TopSellingProduct  
Contextual 3com.example.sharenshop.data.model.TopSellingProduct  Int 3com.example.sharenshop.data.model.TopSellingProduct  List 3com.example.sharenshop.data.model.TopSellingProduct  String 3com.example.sharenshop.data.model.TopSellingProduct  
BigDecimal =com.example.sharenshop.data.model.TopSellingProduct.Companion  
Contextual =com.example.sharenshop.data.model.TopSellingProduct.Companion  Int =com.example.sharenshop.data.model.TopSellingProduct.Companion  List =com.example.sharenshop.data.model.TopSellingProduct.Companion  String =com.example.sharenshop.data.model.TopSellingProduct.Companion  
BigDecimal -com.example.sharenshop.data.model.Transaction  
Contextual -com.example.sharenshop.data.model.Transaction  Long -com.example.sharenshop.data.model.Transaction  
PrimaryKey -com.example.sharenshop.data.model.Transaction  String -com.example.sharenshop.data.model.Transaction  TransactionCategory -com.example.sharenshop.data.model.Transaction  TransactionType -com.example.sharenshop.data.model.Transaction  
BigDecimal 7com.example.sharenshop.data.model.Transaction.Companion  
Contextual 7com.example.sharenshop.data.model.Transaction.Companion  Long 7com.example.sharenshop.data.model.Transaction.Companion  
PrimaryKey 7com.example.sharenshop.data.model.Transaction.Companion  String 7com.example.sharenshop.data.model.Transaction.Companion  TransactionCategory 7com.example.sharenshop.data.model.Transaction.Companion  TransactionType 7com.example.sharenshop.data.model.Transaction.Companion  String 5com.example.sharenshop.data.model.TransactionCategory  TransactionCategory 5com.example.sharenshop.data.model.TransactionCategory  String ?com.example.sharenshop.data.model.TransactionCategory.Companion  TransactionCategory ?com.example.sharenshop.data.model.TransactionCategory.Companion  String 1com.example.sharenshop.data.model.TransactionType  TransactionType 1com.example.sharenshop.data.model.TransactionType  String ;com.example.sharenshop.data.model.TransactionType.Companion  TransactionType ;com.example.sharenshop.data.model.TransactionType.Companion  	Companion &com.example.sharenshop.data.model.User  Long &com.example.sharenshop.data.model.User  
PrimaryKey &com.example.sharenshop.data.model.User  String &com.example.sharenshop.data.model.User  UserRole &com.example.sharenshop.data.model.User  userType &com.example.sharenshop.data.model.User  Long 0com.example.sharenshop.data.model.User.Companion  
PrimaryKey 0com.example.sharenshop.data.model.User.Companion  String 0com.example.sharenshop.data.model.User.Companion  UserRole 0com.example.sharenshop.data.model.User.Companion  CUSTOMER *com.example.sharenshop.data.model.UserRole  String *com.example.sharenshop.data.model.UserRole  UserRole *com.example.sharenshop.data.model.UserRole  
fromString *com.example.sharenshop.data.model.UserRole  value *com.example.sharenshop.data.model.UserRole  String 4com.example.sharenshop.data.model.UserRole.Companion  UserRole 4com.example.sharenshop.data.model.UserRole.Companion  
fromString 4com.example.sharenshop.data.model.UserRole.Companion  Auth "com.example.sharenshop.data.remote  Boolean "com.example.sharenshop.data.remote  BuildConfig "com.example.sharenshop.data.remote  	Postgrest "com.example.sharenshop.data.remote  SupabaseClient "com.example.sharenshop.data.remote  SupabaseConstants "com.example.sharenshop.data.remote  createSupabaseClient "com.example.sharenshop.data.remote  Auth 1com.example.sharenshop.data.remote.SupabaseClient  Boolean 1com.example.sharenshop.data.remote.SupabaseClient  BuildConfig 1com.example.sharenshop.data.remote.SupabaseClient  	Postgrest 1com.example.sharenshop.data.remote.SupabaseClient  auth 1com.example.sharenshop.data.remote.SupabaseClient  client 1com.example.sharenshop.data.remote.SupabaseClient  createSupabaseClient 1com.example.sharenshop.data.remote.SupabaseClient  getCREATESupabaseClient 1com.example.sharenshop.data.remote.SupabaseClient  getCreateSupabaseClient 1com.example.sharenshop.data.remote.SupabaseClient  	postgrest 1com.example.sharenshop.data.remote.SupabaseClient  BuildConfig 4com.example.sharenshop.data.remote.SupabaseConstants  CustomerRepositoryImpl &com.example.sharenshop.data.repository  EncryptionSettings &com.example.sharenshop.data.repository  Int &com.example.sharenshop.data.repository  InvoiceRepositoryImpl &com.example.sharenshop.data.repository  List &com.example.sharenshop.data.repository  Long &com.example.sharenshop.data.repository  Map &com.example.sharenshop.data.repository  MutableStateFlow &com.example.sharenshop.data.repository  ProductRepositoryImpl &com.example.sharenshop.data.repository  SecurityRepositoryImpl &com.example.sharenshop.data.repository  SellerRepositoryImpl &com.example.sharenshop.data.repository  SettingsRepositoryImpl &com.example.sharenshop.data.repository  StatisticsRepositoryImpl &com.example.sharenshop.data.repository  String &com.example.sharenshop.data.repository  UserRepositoryImpl &com.example.sharenshop.data.repository  appSettingsDataStore &com.example.sharenshop.data.repository  provideDelegate &com.example.sharenshop.data.repository  Customer =com.example.sharenshop.data.repository.CustomerRepositoryImpl  CustomerDao =com.example.sharenshop.data.repository.CustomerRepositoryImpl  Flow =com.example.sharenshop.data.repository.CustomerRepositoryImpl  Inject =com.example.sharenshop.data.repository.CustomerRepositoryImpl  List =com.example.sharenshop.data.repository.CustomerRepositoryImpl  String =com.example.sharenshop.data.repository.CustomerRepositoryImpl  Flow <com.example.sharenshop.data.repository.InvoiceRepositoryImpl  Inject <com.example.sharenshop.data.repository.InvoiceRepositoryImpl  Invoice <com.example.sharenshop.data.repository.InvoiceRepositoryImpl  
InvoiceDao <com.example.sharenshop.data.repository.InvoiceRepositoryImpl  InvoiceItem <com.example.sharenshop.data.repository.InvoiceRepositoryImpl  InvoiceItemDao <com.example.sharenshop.data.repository.InvoiceRepositoryImpl  List <com.example.sharenshop.data.repository.InvoiceRepositoryImpl  String <com.example.sharenshop.data.repository.InvoiceRepositoryImpl  Flow <com.example.sharenshop.data.repository.ProductRepositoryImpl  Inject <com.example.sharenshop.data.repository.ProductRepositoryImpl  List <com.example.sharenshop.data.repository.ProductRepositoryImpl  Product <com.example.sharenshop.data.repository.ProductRepositoryImpl  
ProductDao <com.example.sharenshop.data.repository.ProductRepositoryImpl  String <com.example.sharenshop.data.repository.ProductRepositoryImpl  DatabaseEncryptionService =com.example.sharenshop.data.repository.SecurityRepositoryImpl  EncryptionSettings =com.example.sharenshop.data.repository.SecurityRepositoryImpl  FileEncryptionService =com.example.sharenshop.data.repository.SecurityRepositoryImpl  Flow =com.example.sharenshop.data.repository.SecurityRepositoryImpl  Inject =com.example.sharenshop.data.repository.SecurityRepositoryImpl  MutableStateFlow =com.example.sharenshop.data.repository.SecurityRepositoryImpl  "SharedPreferencesEncryptionService =com.example.sharenshop.data.repository.SecurityRepositoryImpl  String =com.example.sharenshop.data.repository.SecurityRepositoryImpl  invoke =com.example.sharenshop.data.repository.SecurityRepositoryImpl  Flow ;com.example.sharenshop.data.repository.SellerRepositoryImpl  Inject ;com.example.sharenshop.data.repository.SellerRepositoryImpl  List ;com.example.sharenshop.data.repository.SellerRepositoryImpl  Seller ;com.example.sharenshop.data.repository.SellerRepositoryImpl  	SellerDao ;com.example.sharenshop.data.repository.SellerRepositoryImpl  String ;com.example.sharenshop.data.repository.SellerRepositoryImpl  AppSettings =com.example.sharenshop.data.repository.SettingsRepositoryImpl  Context =com.example.sharenshop.data.repository.SettingsRepositoryImpl  Flow =com.example.sharenshop.data.repository.SettingsRepositoryImpl  Inject =com.example.sharenshop.data.repository.SettingsRepositoryImpl  
BigDecimal ?com.example.sharenshop.data.repository.StatisticsRepositoryImpl  CustomerDao ?com.example.sharenshop.data.repository.StatisticsRepositoryImpl  Flow ?com.example.sharenshop.data.repository.StatisticsRepositoryImpl  Inject ?com.example.sharenshop.data.repository.StatisticsRepositoryImpl  Int ?com.example.sharenshop.data.repository.StatisticsRepositoryImpl  
InvoiceDao ?com.example.sharenshop.data.repository.StatisticsRepositoryImpl  InvoiceItemDao ?com.example.sharenshop.data.repository.StatisticsRepositoryImpl  Long ?com.example.sharenshop.data.repository.StatisticsRepositoryImpl  Map ?com.example.sharenshop.data.repository.StatisticsRepositoryImpl  
ProductDao ?com.example.sharenshop.data.repository.StatisticsRepositoryImpl  String ?com.example.sharenshop.data.repository.StatisticsRepositoryImpl  Flow 9com.example.sharenshop.data.repository.UserRepositoryImpl  Inject 9com.example.sharenshop.data.repository.UserRepositoryImpl  List 9com.example.sharenshop.data.repository.UserRepositoryImpl  String 9com.example.sharenshop.data.repository.UserRepositoryImpl  User 9com.example.sharenshop.data.repository.UserRepositoryImpl  UserDao 9com.example.sharenshop.data.repository.UserRepositoryImpl  DatabaseEncryptionService $com.example.sharenshop.data.security  EncryptionService $com.example.sharenshop.data.security  FileEncryptionService $com.example.sharenshop.data.security  "SharedPreferencesEncryptionService $com.example.sharenshop.data.security  String $com.example.sharenshop.data.security  Inject >com.example.sharenshop.data.security.DatabaseEncryptionService  String >com.example.sharenshop.data.security.DatabaseEncryptionService  String 6com.example.sharenshop.data.security.EncryptionService  Inject :com.example.sharenshop.data.security.FileEncryptionService  String :com.example.sharenshop.data.security.FileEncryptionService  Inject Gcom.example.sharenshop.data.security.SharedPreferencesEncryptionService  String Gcom.example.sharenshop.data.security.SharedPreferencesEncryptionService  	DaoModule com.example.sharenshop.di  DatabaseModule com.example.sharenshop.di  
NetworkModule com.example.sharenshop.di  RepositoryModule com.example.sharenshop.di  SecurityModule com.example.sharenshop.di  SingletonComponent com.example.sharenshop.di  
UseCaseModule com.example.sharenshop.di  AppDatabase #com.example.sharenshop.di.DaoModule  CustomerDao #com.example.sharenshop.di.DaoModule  
InvoiceDao #com.example.sharenshop.di.DaoModule  InvoiceItemDao #com.example.sharenshop.di.DaoModule  
ProductDao #com.example.sharenshop.di.DaoModule  Provides #com.example.sharenshop.di.DaoModule  	SellerDao #com.example.sharenshop.di.DaoModule  	Singleton #com.example.sharenshop.di.DaoModule  UserDao #com.example.sharenshop.di.DaoModule  AppDatabase (com.example.sharenshop.di.DatabaseModule  ApplicationContext (com.example.sharenshop.di.DatabaseModule  Context (com.example.sharenshop.di.DatabaseModule  Provides (com.example.sharenshop.di.DatabaseModule  	Singleton (com.example.sharenshop.di.DatabaseModule  Provides 'com.example.sharenshop.di.NetworkModule  	Singleton 'com.example.sharenshop.di.NetworkModule  SupabaseClient 'com.example.sharenshop.di.NetworkModule  Binds *com.example.sharenshop.di.RepositoryModule  CustomerRepository *com.example.sharenshop.di.RepositoryModule  CustomerRepositoryImpl *com.example.sharenshop.di.RepositoryModule  InvoiceRepository *com.example.sharenshop.di.RepositoryModule  InvoiceRepositoryImpl *com.example.sharenshop.di.RepositoryModule  ProductRepository *com.example.sharenshop.di.RepositoryModule  ProductRepositoryImpl *com.example.sharenshop.di.RepositoryModule  SellerRepository *com.example.sharenshop.di.RepositoryModule  SellerRepositoryImpl *com.example.sharenshop.di.RepositoryModule  SettingsRepository *com.example.sharenshop.di.RepositoryModule  SettingsRepositoryImpl *com.example.sharenshop.di.RepositoryModule  	Singleton *com.example.sharenshop.di.RepositoryModule  StatisticsRepository *com.example.sharenshop.di.RepositoryModule  StatisticsRepositoryImpl *com.example.sharenshop.di.RepositoryModule  UserRepository *com.example.sharenshop.di.RepositoryModule  UserRepositoryImpl *com.example.sharenshop.di.RepositoryModule  Binds (com.example.sharenshop.di.SecurityModule  DatabaseEncryptionService (com.example.sharenshop.di.SecurityModule  FileEncryptionService (com.example.sharenshop.di.SecurityModule  Provides (com.example.sharenshop.di.SecurityModule  SecurityRepository (com.example.sharenshop.di.SecurityModule  SecurityRepositoryImpl (com.example.sharenshop.di.SecurityModule  "SharedPreferencesEncryptionService (com.example.sharenshop.di.SecurityModule  	Singleton (com.example.sharenshop.di.SecurityModule  Binds 2com.example.sharenshop.di.SecurityModule.Companion  DatabaseEncryptionService 2com.example.sharenshop.di.SecurityModule.Companion  FileEncryptionService 2com.example.sharenshop.di.SecurityModule.Companion  Provides 2com.example.sharenshop.di.SecurityModule.Companion  SecurityRepository 2com.example.sharenshop.di.SecurityModule.Companion  SecurityRepositoryImpl 2com.example.sharenshop.di.SecurityModule.Companion  "SharedPreferencesEncryptionService 2com.example.sharenshop.di.SecurityModule.Companion  	Singleton 2com.example.sharenshop.di.SecurityModule.Companion  CustomerRepository 'com.example.sharenshop.di.UseCaseModule  CustomerUseCases 'com.example.sharenshop.di.UseCaseModule  InvoiceRepository 'com.example.sharenshop.di.UseCaseModule  InvoiceUseCases 'com.example.sharenshop.di.UseCaseModule  ProductRepository 'com.example.sharenshop.di.UseCaseModule  ProductUseCases 'com.example.sharenshop.di.UseCaseModule  Provides 'com.example.sharenshop.di.UseCaseModule  SecurityRepository 'com.example.sharenshop.di.UseCaseModule  SecurityUseCases 'com.example.sharenshop.di.UseCaseModule  SellerRepository 'com.example.sharenshop.di.UseCaseModule  SellerUseCases 'com.example.sharenshop.di.UseCaseModule  SettingsRepository 'com.example.sharenshop.di.UseCaseModule  SettingsUseCases 'com.example.sharenshop.di.UseCaseModule  	Singleton 'com.example.sharenshop.di.UseCaseModule  StatisticsRepository 'com.example.sharenshop.di.UseCaseModule  StatisticsUseCases 'com.example.sharenshop.di.UseCaseModule  UserRepository 'com.example.sharenshop.di.UseCaseModule  UserUseCases 'com.example.sharenshop.di.UseCaseModule  CustomerApprovalRepository (com.example.sharenshop.domain.repository  CustomerRepository (com.example.sharenshop.domain.repository  Int (com.example.sharenshop.domain.repository  InvoiceRepository (com.example.sharenshop.domain.repository  List (com.example.sharenshop.domain.repository  Long (com.example.sharenshop.domain.repository  Map (com.example.sharenshop.domain.repository  NotificationRepository (com.example.sharenshop.domain.repository  ProductRepository (com.example.sharenshop.domain.repository  SecurityRepository (com.example.sharenshop.domain.repository  SellerRepository (com.example.sharenshop.domain.repository  SettingsRepository (com.example.sharenshop.domain.repository  StatisticsRepository (com.example.sharenshop.domain.repository  String (com.example.sharenshop.domain.repository  UserRepository (com.example.sharenshop.domain.repository  ApprovalStatus Ccom.example.sharenshop.domain.repository.CustomerApprovalRepository  CustomerApprovalRequest Ccom.example.sharenshop.domain.repository.CustomerApprovalRepository  Flow Ccom.example.sharenshop.domain.repository.CustomerApprovalRepository  Int Ccom.example.sharenshop.domain.repository.CustomerApprovalRepository  List Ccom.example.sharenshop.domain.repository.CustomerApprovalRepository  Long Ccom.example.sharenshop.domain.repository.CustomerApprovalRepository  String Ccom.example.sharenshop.domain.repository.CustomerApprovalRepository  Customer ;com.example.sharenshop.domain.repository.CustomerRepository  Flow ;com.example.sharenshop.domain.repository.CustomerRepository  List ;com.example.sharenshop.domain.repository.CustomerRepository  String ;com.example.sharenshop.domain.repository.CustomerRepository  Flow :com.example.sharenshop.domain.repository.InvoiceRepository  Invoice :com.example.sharenshop.domain.repository.InvoiceRepository  List :com.example.sharenshop.domain.repository.InvoiceRepository  String :com.example.sharenshop.domain.repository.InvoiceRepository  Flow ?com.example.sharenshop.domain.repository.NotificationRepository  Int ?com.example.sharenshop.domain.repository.NotificationRepository  List ?com.example.sharenshop.domain.repository.NotificationRepository  Long ?com.example.sharenshop.domain.repository.NotificationRepository  Notification ?com.example.sharenshop.domain.repository.NotificationRepository  NotificationType ?com.example.sharenshop.domain.repository.NotificationRepository  String ?com.example.sharenshop.domain.repository.NotificationRepository  Flow :com.example.sharenshop.domain.repository.ProductRepository  List :com.example.sharenshop.domain.repository.ProductRepository  Product :com.example.sharenshop.domain.repository.ProductRepository  String :com.example.sharenshop.domain.repository.ProductRepository  EncryptionSettings ;com.example.sharenshop.domain.repository.SecurityRepository  Flow ;com.example.sharenshop.domain.repository.SecurityRepository  String ;com.example.sharenshop.domain.repository.SecurityRepository  Flow 9com.example.sharenshop.domain.repository.SellerRepository  List 9com.example.sharenshop.domain.repository.SellerRepository  Seller 9com.example.sharenshop.domain.repository.SellerRepository  String 9com.example.sharenshop.domain.repository.SellerRepository  AppSettings ;com.example.sharenshop.domain.repository.SettingsRepository  Flow ;com.example.sharenshop.domain.repository.SettingsRepository  
BigDecimal =com.example.sharenshop.domain.repository.StatisticsRepository  Flow =com.example.sharenshop.domain.repository.StatisticsRepository  Int =com.example.sharenshop.domain.repository.StatisticsRepository  Long =com.example.sharenshop.domain.repository.StatisticsRepository  Map =com.example.sharenshop.domain.repository.StatisticsRepository  String =com.example.sharenshop.domain.repository.StatisticsRepository  Flow 7com.example.sharenshop.domain.repository.UserRepository  List 7com.example.sharenshop.domain.repository.UserRepository  String 7com.example.sharenshop.domain.repository.UserRepository  User 7com.example.sharenshop.domain.repository.UserRepository  ApproveCustomer &com.example.sharenshop.domain.use_case  CustomerApprovalRequest &com.example.sharenshop.domain.use_case  CustomerApprovalUseCases &com.example.sharenshop.domain.use_case  CustomerUseCases &com.example.sharenshop.domain.use_case  DecryptData &com.example.sharenshop.domain.use_case  DeleteCustomer &com.example.sharenshop.domain.use_case  
DeleteInvoice &com.example.sharenshop.domain.use_case  
DeleteProduct &com.example.sharenshop.domain.use_case  DeleteSeller &com.example.sharenshop.domain.use_case  
DeleteUser &com.example.sharenshop.domain.use_case  EncryptData &com.example.sharenshop.domain.use_case  GetAllCustomers &com.example.sharenshop.domain.use_case  GetAllInvoices &com.example.sharenshop.domain.use_case  GetAllProducts &com.example.sharenshop.domain.use_case  
GetAllSellers &com.example.sharenshop.domain.use_case  GetAllUsers &com.example.sharenshop.domain.use_case  GetAppSettings &com.example.sharenshop.domain.use_case  GetCustomer &com.example.sharenshop.domain.use_case  GetCustomerSpending &com.example.sharenshop.domain.use_case  GetEncryptionSettings &com.example.sharenshop.domain.use_case  
GetInvoice &com.example.sharenshop.domain.use_case  GetInvoicesByCustomer &com.example.sharenshop.domain.use_case  GetInvoicesByUser &com.example.sharenshop.domain.use_case  GetPendingRequests &com.example.sharenshop.domain.use_case  
GetProduct &com.example.sharenshop.domain.use_case  GetRequestsByReferrer &com.example.sharenshop.domain.use_case  GetSalesByPaymentStatus &com.example.sharenshop.domain.use_case  GetSalesByTimePeriod &com.example.sharenshop.domain.use_case  	GetSeller &com.example.sharenshop.domain.use_case  GetTopSellingProducts &com.example.sharenshop.domain.use_case  GetTotalProductsSold &com.example.sharenshop.domain.use_case  GetTotalSalesAmount &com.example.sharenshop.domain.use_case  GetUser &com.example.sharenshop.domain.use_case  GetUsersByType &com.example.sharenshop.domain.use_case  InsertCustomer &com.example.sharenshop.domain.use_case  
InsertInvoice &com.example.sharenshop.domain.use_case  
InsertProduct &com.example.sharenshop.domain.use_case  InsertSeller &com.example.sharenshop.domain.use_case  
InsertUser &com.example.sharenshop.domain.use_case  Int &com.example.sharenshop.domain.use_case  InvoiceUseCases &com.example.sharenshop.domain.use_case  List &com.example.sharenshop.domain.use_case  Long &com.example.sharenshop.domain.use_case  Map &com.example.sharenshop.domain.use_case  ProductUseCases &com.example.sharenshop.domain.use_case  RejectCustomer &com.example.sharenshop.domain.use_case  Result &com.example.sharenshop.domain.use_case  SaveAppSettings &com.example.sharenshop.domain.use_case  SaveEncryptionSettings &com.example.sharenshop.domain.use_case  SearchCustomers &com.example.sharenshop.domain.use_case  SearchProducts &com.example.sharenshop.domain.use_case  
SearchSellers &com.example.sharenshop.domain.use_case  SecurityUseCases &com.example.sharenshop.domain.use_case  SellerUseCases &com.example.sharenshop.domain.use_case  SettingsUseCases &com.example.sharenshop.domain.use_case  StatisticsUseCases &com.example.sharenshop.domain.use_case  String &com.example.sharenshop.domain.use_case  SubmitApprovalRequest &com.example.sharenshop.domain.use_case  Unit &com.example.sharenshop.domain.use_case  UpdateCustomer &com.example.sharenshop.domain.use_case  
UpdateInvoice &com.example.sharenshop.domain.use_case  
UpdateProduct &com.example.sharenshop.domain.use_case  UpdateSeller &com.example.sharenshop.domain.use_case  
UpdateUser &com.example.sharenshop.domain.use_case  User &com.example.sharenshop.domain.use_case  UserUseCases &com.example.sharenshop.domain.use_case  ValidateReferrerCode &com.example.sharenshop.domain.use_case  CustomerApprovalRepository 6com.example.sharenshop.domain.use_case.ApproveCustomer  Inject 6com.example.sharenshop.domain.use_case.ApproveCustomer  NotificationRepository 6com.example.sharenshop.domain.use_case.ApproveCustomer  Result 6com.example.sharenshop.domain.use_case.ApproveCustomer  String 6com.example.sharenshop.domain.use_case.ApproveCustomer  Unit 6com.example.sharenshop.domain.use_case.ApproveCustomer  UserRepository 6com.example.sharenshop.domain.use_case.ApproveCustomer  ApproveCustomer ?com.example.sharenshop.domain.use_case.CustomerApprovalUseCases  GetPendingRequests ?com.example.sharenshop.domain.use_case.CustomerApprovalUseCases  GetRequestsByReferrer ?com.example.sharenshop.domain.use_case.CustomerApprovalUseCases  Inject ?com.example.sharenshop.domain.use_case.CustomerApprovalUseCases  RejectCustomer ?com.example.sharenshop.domain.use_case.CustomerApprovalUseCases  SubmitApprovalRequest ?com.example.sharenshop.domain.use_case.CustomerApprovalUseCases  ValidateReferrerCode ?com.example.sharenshop.domain.use_case.CustomerApprovalUseCases  DeleteCustomer 7com.example.sharenshop.domain.use_case.CustomerUseCases  GetAllCustomers 7com.example.sharenshop.domain.use_case.CustomerUseCases  GetCustomer 7com.example.sharenshop.domain.use_case.CustomerUseCases  InsertCustomer 7com.example.sharenshop.domain.use_case.CustomerUseCases  SearchCustomers 7com.example.sharenshop.domain.use_case.CustomerUseCases  UpdateCustomer 7com.example.sharenshop.domain.use_case.CustomerUseCases  SecurityRepository 2com.example.sharenshop.domain.use_case.DecryptData  String 2com.example.sharenshop.domain.use_case.DecryptData  CustomerRepository 5com.example.sharenshop.domain.use_case.DeleteCustomer  String 5com.example.sharenshop.domain.use_case.DeleteCustomer  InvoiceRepository 4com.example.sharenshop.domain.use_case.DeleteInvoice  String 4com.example.sharenshop.domain.use_case.DeleteInvoice  ProductRepository 4com.example.sharenshop.domain.use_case.DeleteProduct  String 4com.example.sharenshop.domain.use_case.DeleteProduct  SellerRepository 3com.example.sharenshop.domain.use_case.DeleteSeller  String 3com.example.sharenshop.domain.use_case.DeleteSeller  String 1com.example.sharenshop.domain.use_case.DeleteUser  UserRepository 1com.example.sharenshop.domain.use_case.DeleteUser  SecurityRepository 2com.example.sharenshop.domain.use_case.EncryptData  String 2com.example.sharenshop.domain.use_case.EncryptData  Customer 6com.example.sharenshop.domain.use_case.GetAllCustomers  CustomerRepository 6com.example.sharenshop.domain.use_case.GetAllCustomers  Flow 6com.example.sharenshop.domain.use_case.GetAllCustomers  List 6com.example.sharenshop.domain.use_case.GetAllCustomers  Flow 5com.example.sharenshop.domain.use_case.GetAllInvoices  Invoice 5com.example.sharenshop.domain.use_case.GetAllInvoices  InvoiceRepository 5com.example.sharenshop.domain.use_case.GetAllInvoices  List 5com.example.sharenshop.domain.use_case.GetAllInvoices  Flow 5com.example.sharenshop.domain.use_case.GetAllProducts  List 5com.example.sharenshop.domain.use_case.GetAllProducts  Product 5com.example.sharenshop.domain.use_case.GetAllProducts  ProductRepository 5com.example.sharenshop.domain.use_case.GetAllProducts  Flow 4com.example.sharenshop.domain.use_case.GetAllSellers  List 4com.example.sharenshop.domain.use_case.GetAllSellers  Seller 4com.example.sharenshop.domain.use_case.GetAllSellers  SellerRepository 4com.example.sharenshop.domain.use_case.GetAllSellers  Flow 2com.example.sharenshop.domain.use_case.GetAllUsers  List 2com.example.sharenshop.domain.use_case.GetAllUsers  User 2com.example.sharenshop.domain.use_case.GetAllUsers  UserRepository 2com.example.sharenshop.domain.use_case.GetAllUsers  AppSettings 5com.example.sharenshop.domain.use_case.GetAppSettings  Flow 5com.example.sharenshop.domain.use_case.GetAppSettings  SettingsRepository 5com.example.sharenshop.domain.use_case.GetAppSettings  Customer 2com.example.sharenshop.domain.use_case.GetCustomer  CustomerRepository 2com.example.sharenshop.domain.use_case.GetCustomer  Flow 2com.example.sharenshop.domain.use_case.GetCustomer  String 2com.example.sharenshop.domain.use_case.GetCustomer  
BigDecimal :com.example.sharenshop.domain.use_case.GetCustomerSpending  Flow :com.example.sharenshop.domain.use_case.GetCustomerSpending  StatisticsRepository :com.example.sharenshop.domain.use_case.GetCustomerSpending  String :com.example.sharenshop.domain.use_case.GetCustomerSpending  EncryptionSettings <com.example.sharenshop.domain.use_case.GetEncryptionSettings  Flow <com.example.sharenshop.domain.use_case.GetEncryptionSettings  SecurityRepository <com.example.sharenshop.domain.use_case.GetEncryptionSettings  Flow 1com.example.sharenshop.domain.use_case.GetInvoice  Invoice 1com.example.sharenshop.domain.use_case.GetInvoice  InvoiceRepository 1com.example.sharenshop.domain.use_case.GetInvoice  String 1com.example.sharenshop.domain.use_case.GetInvoice  Flow <com.example.sharenshop.domain.use_case.GetInvoicesByCustomer  Invoice <com.example.sharenshop.domain.use_case.GetInvoicesByCustomer  InvoiceRepository <com.example.sharenshop.domain.use_case.GetInvoicesByCustomer  List <com.example.sharenshop.domain.use_case.GetInvoicesByCustomer  String <com.example.sharenshop.domain.use_case.GetInvoicesByCustomer  Flow 8com.example.sharenshop.domain.use_case.GetInvoicesByUser  Invoice 8com.example.sharenshop.domain.use_case.GetInvoicesByUser  InvoiceRepository 8com.example.sharenshop.domain.use_case.GetInvoicesByUser  List 8com.example.sharenshop.domain.use_case.GetInvoicesByUser  String 8com.example.sharenshop.domain.use_case.GetInvoicesByUser  CustomerApprovalRepository 9com.example.sharenshop.domain.use_case.GetPendingRequests  CustomerApprovalRequest 9com.example.sharenshop.domain.use_case.GetPendingRequests  Flow 9com.example.sharenshop.domain.use_case.GetPendingRequests  Inject 9com.example.sharenshop.domain.use_case.GetPendingRequests  List 9com.example.sharenshop.domain.use_case.GetPendingRequests  Flow 1com.example.sharenshop.domain.use_case.GetProduct  Product 1com.example.sharenshop.domain.use_case.GetProduct  ProductRepository 1com.example.sharenshop.domain.use_case.GetProduct  String 1com.example.sharenshop.domain.use_case.GetProduct  CustomerApprovalRepository <com.example.sharenshop.domain.use_case.GetRequestsByReferrer  CustomerApprovalRequest <com.example.sharenshop.domain.use_case.GetRequestsByReferrer  Flow <com.example.sharenshop.domain.use_case.GetRequestsByReferrer  Inject <com.example.sharenshop.domain.use_case.GetRequestsByReferrer  List <com.example.sharenshop.domain.use_case.GetRequestsByReferrer  String <com.example.sharenshop.domain.use_case.GetRequestsByReferrer  
BigDecimal >com.example.sharenshop.domain.use_case.GetSalesByPaymentStatus  Flow >com.example.sharenshop.domain.use_case.GetSalesByPaymentStatus  StatisticsRepository >com.example.sharenshop.domain.use_case.GetSalesByPaymentStatus  String >com.example.sharenshop.domain.use_case.GetSalesByPaymentStatus  
BigDecimal ;com.example.sharenshop.domain.use_case.GetSalesByTimePeriod  Flow ;com.example.sharenshop.domain.use_case.GetSalesByTimePeriod  Long ;com.example.sharenshop.domain.use_case.GetSalesByTimePeriod  StatisticsRepository ;com.example.sharenshop.domain.use_case.GetSalesByTimePeriod  Flow 0com.example.sharenshop.domain.use_case.GetSeller  Seller 0com.example.sharenshop.domain.use_case.GetSeller  SellerRepository 0com.example.sharenshop.domain.use_case.GetSeller  String 0com.example.sharenshop.domain.use_case.GetSeller  Flow <com.example.sharenshop.domain.use_case.GetTopSellingProducts  Int <com.example.sharenshop.domain.use_case.GetTopSellingProducts  Map <com.example.sharenshop.domain.use_case.GetTopSellingProducts  StatisticsRepository <com.example.sharenshop.domain.use_case.GetTopSellingProducts  String <com.example.sharenshop.domain.use_case.GetTopSellingProducts  Flow ;com.example.sharenshop.domain.use_case.GetTotalProductsSold  Int ;com.example.sharenshop.domain.use_case.GetTotalProductsSold  StatisticsRepository ;com.example.sharenshop.domain.use_case.GetTotalProductsSold  
BigDecimal :com.example.sharenshop.domain.use_case.GetTotalSalesAmount  Flow :com.example.sharenshop.domain.use_case.GetTotalSalesAmount  StatisticsRepository :com.example.sharenshop.domain.use_case.GetTotalSalesAmount  Flow .com.example.sharenshop.domain.use_case.GetUser  String .com.example.sharenshop.domain.use_case.GetUser  User .com.example.sharenshop.domain.use_case.GetUser  UserRepository .com.example.sharenshop.domain.use_case.GetUser  Flow 5com.example.sharenshop.domain.use_case.GetUsersByType  List 5com.example.sharenshop.domain.use_case.GetUsersByType  String 5com.example.sharenshop.domain.use_case.GetUsersByType  User 5com.example.sharenshop.domain.use_case.GetUsersByType  UserRepository 5com.example.sharenshop.domain.use_case.GetUsersByType  Customer 5com.example.sharenshop.domain.use_case.InsertCustomer  CustomerRepository 5com.example.sharenshop.domain.use_case.InsertCustomer  Invoice 4com.example.sharenshop.domain.use_case.InsertInvoice  InvoiceRepository 4com.example.sharenshop.domain.use_case.InsertInvoice  Product 4com.example.sharenshop.domain.use_case.InsertProduct  ProductRepository 4com.example.sharenshop.domain.use_case.InsertProduct  Seller 3com.example.sharenshop.domain.use_case.InsertSeller  SellerRepository 3com.example.sharenshop.domain.use_case.InsertSeller  User 1com.example.sharenshop.domain.use_case.InsertUser  UserRepository 1com.example.sharenshop.domain.use_case.InsertUser  
DeleteInvoice 6com.example.sharenshop.domain.use_case.InvoiceUseCases  GetAllInvoices 6com.example.sharenshop.domain.use_case.InvoiceUseCases  
GetInvoice 6com.example.sharenshop.domain.use_case.InvoiceUseCases  GetInvoicesByCustomer 6com.example.sharenshop.domain.use_case.InvoiceUseCases  GetInvoicesByUser 6com.example.sharenshop.domain.use_case.InvoiceUseCases  
InsertInvoice 6com.example.sharenshop.domain.use_case.InvoiceUseCases  
UpdateInvoice 6com.example.sharenshop.domain.use_case.InvoiceUseCases  
DeleteProduct 6com.example.sharenshop.domain.use_case.ProductUseCases  GetAllProducts 6com.example.sharenshop.domain.use_case.ProductUseCases  
GetProduct 6com.example.sharenshop.domain.use_case.ProductUseCases  
InsertProduct 6com.example.sharenshop.domain.use_case.ProductUseCases  SearchProducts 6com.example.sharenshop.domain.use_case.ProductUseCases  
UpdateProduct 6com.example.sharenshop.domain.use_case.ProductUseCases  CustomerApprovalRepository 5com.example.sharenshop.domain.use_case.RejectCustomer  Inject 5com.example.sharenshop.domain.use_case.RejectCustomer  Result 5com.example.sharenshop.domain.use_case.RejectCustomer  String 5com.example.sharenshop.domain.use_case.RejectCustomer  Unit 5com.example.sharenshop.domain.use_case.RejectCustomer  AppSettings 6com.example.sharenshop.domain.use_case.SaveAppSettings  SettingsRepository 6com.example.sharenshop.domain.use_case.SaveAppSettings  EncryptionSettings =com.example.sharenshop.domain.use_case.SaveEncryptionSettings  SecurityRepository =com.example.sharenshop.domain.use_case.SaveEncryptionSettings  Customer 6com.example.sharenshop.domain.use_case.SearchCustomers  CustomerRepository 6com.example.sharenshop.domain.use_case.SearchCustomers  Flow 6com.example.sharenshop.domain.use_case.SearchCustomers  List 6com.example.sharenshop.domain.use_case.SearchCustomers  String 6com.example.sharenshop.domain.use_case.SearchCustomers  Flow 5com.example.sharenshop.domain.use_case.SearchProducts  List 5com.example.sharenshop.domain.use_case.SearchProducts  Product 5com.example.sharenshop.domain.use_case.SearchProducts  ProductRepository 5com.example.sharenshop.domain.use_case.SearchProducts  String 5com.example.sharenshop.domain.use_case.SearchProducts  Flow 4com.example.sharenshop.domain.use_case.SearchSellers  List 4com.example.sharenshop.domain.use_case.SearchSellers  Seller 4com.example.sharenshop.domain.use_case.SearchSellers  SellerRepository 4com.example.sharenshop.domain.use_case.SearchSellers  String 4com.example.sharenshop.domain.use_case.SearchSellers  DecryptData 7com.example.sharenshop.domain.use_case.SecurityUseCases  EncryptData 7com.example.sharenshop.domain.use_case.SecurityUseCases  GetEncryptionSettings 7com.example.sharenshop.domain.use_case.SecurityUseCases  SaveEncryptionSettings 7com.example.sharenshop.domain.use_case.SecurityUseCases  DeleteSeller 5com.example.sharenshop.domain.use_case.SellerUseCases  
GetAllSellers 5com.example.sharenshop.domain.use_case.SellerUseCases  	GetSeller 5com.example.sharenshop.domain.use_case.SellerUseCases  InsertSeller 5com.example.sharenshop.domain.use_case.SellerUseCases  
SearchSellers 5com.example.sharenshop.domain.use_case.SellerUseCases  UpdateSeller 5com.example.sharenshop.domain.use_case.SellerUseCases  GetAppSettings 7com.example.sharenshop.domain.use_case.SettingsUseCases  SaveAppSettings 7com.example.sharenshop.domain.use_case.SettingsUseCases  GetCustomerSpending 9com.example.sharenshop.domain.use_case.StatisticsUseCases  GetSalesByPaymentStatus 9com.example.sharenshop.domain.use_case.StatisticsUseCases  GetSalesByTimePeriod 9com.example.sharenshop.domain.use_case.StatisticsUseCases  GetTopSellingProducts 9com.example.sharenshop.domain.use_case.StatisticsUseCases  GetTotalProductsSold 9com.example.sharenshop.domain.use_case.StatisticsUseCases  GetTotalSalesAmount 9com.example.sharenshop.domain.use_case.StatisticsUseCases  CustomerApprovalRepository <com.example.sharenshop.domain.use_case.SubmitApprovalRequest  Inject <com.example.sharenshop.domain.use_case.SubmitApprovalRequest  NotificationRepository <com.example.sharenshop.domain.use_case.SubmitApprovalRequest  Result <com.example.sharenshop.domain.use_case.SubmitApprovalRequest  String <com.example.sharenshop.domain.use_case.SubmitApprovalRequest  UserRepository <com.example.sharenshop.domain.use_case.SubmitApprovalRequest  Customer 5com.example.sharenshop.domain.use_case.UpdateCustomer  CustomerRepository 5com.example.sharenshop.domain.use_case.UpdateCustomer  Invoice 4com.example.sharenshop.domain.use_case.UpdateInvoice  InvoiceRepository 4com.example.sharenshop.domain.use_case.UpdateInvoice  Product 4com.example.sharenshop.domain.use_case.UpdateProduct  ProductRepository 4com.example.sharenshop.domain.use_case.UpdateProduct  Seller 3com.example.sharenshop.domain.use_case.UpdateSeller  SellerRepository 3com.example.sharenshop.domain.use_case.UpdateSeller  User 1com.example.sharenshop.domain.use_case.UpdateUser  UserRepository 1com.example.sharenshop.domain.use_case.UpdateUser  
DeleteUser 3com.example.sharenshop.domain.use_case.UserUseCases  GetAllUsers 3com.example.sharenshop.domain.use_case.UserUseCases  GetUser 3com.example.sharenshop.domain.use_case.UserUseCases  GetUsersByType 3com.example.sharenshop.domain.use_case.UserUseCases  
InsertUser 3com.example.sharenshop.domain.use_case.UserUseCases  
UpdateUser 3com.example.sharenshop.domain.use_case.UserUseCases  Inject ;com.example.sharenshop.domain.use_case.ValidateReferrerCode  Result ;com.example.sharenshop.domain.use_case.ValidateReferrerCode  String ;com.example.sharenshop.domain.use_case.ValidateReferrerCode  User ;com.example.sharenshop.domain.use_case.ValidateReferrerCode  UserRepository ;com.example.sharenshop.domain.use_case.ValidateReferrerCode  
AuthViewModel (com.example.sharenshop.presentation.auth  Boolean (com.example.sharenshop.presentation.auth  MutableStateFlow (com.example.sharenshop.presentation.auth  String (com.example.sharenshop.presentation.auth  UserRole (com.example.sharenshop.presentation.auth  asStateFlow (com.example.sharenshop.presentation.auth  Boolean 6com.example.sharenshop.presentation.auth.AuthViewModel  Inject 6com.example.sharenshop.presentation.auth.AuthViewModel  MutableStateFlow 6com.example.sharenshop.presentation.auth.AuthViewModel  SessionManager 6com.example.sharenshop.presentation.auth.AuthViewModel  	StateFlow 6com.example.sharenshop.presentation.auth.AuthViewModel  String 6com.example.sharenshop.presentation.auth.AuthViewModel  UserRole 6com.example.sharenshop.presentation.auth.AuthViewModel  UserUseCases 6com.example.sharenshop.presentation.auth.AuthViewModel  
_authError 6com.example.sharenshop.presentation.auth.AuthViewModel  _email 6com.example.sharenshop.presentation.auth.AuthViewModel  _isAuthenticated 6com.example.sharenshop.presentation.auth.AuthViewModel  _isCustomerSignupMode 6com.example.sharenshop.presentation.auth.AuthViewModel  
_isLoading 6com.example.sharenshop.presentation.auth.AuthViewModel  _isLoginMode 6com.example.sharenshop.presentation.auth.AuthViewModel  _name 6com.example.sharenshop.presentation.auth.AuthViewModel  	_password 6com.example.sharenshop.presentation.auth.AuthViewModel  _pendingApproval 6com.example.sharenshop.presentation.auth.AuthViewModel  _phone 6com.example.sharenshop.presentation.auth.AuthViewModel  
_referrerCode 6com.example.sharenshop.presentation.auth.AuthViewModel  _role 6com.example.sharenshop.presentation.auth.AuthViewModel  asStateFlow 6com.example.sharenshop.presentation.auth.AuthViewModel  getASStateFlow 6com.example.sharenshop.presentation.auth.AuthViewModel  getAsStateFlow 6com.example.sharenshop.presentation.auth.AuthViewModel  
BigDecimal (com.example.sharenshop.presentation.home  Boolean (com.example.sharenshop.presentation.home  
HomeViewModel (com.example.sharenshop.presentation.home  Int (com.example.sharenshop.presentation.home  MutableStateFlow (com.example.sharenshop.presentation.home  String (com.example.sharenshop.presentation.home  asStateFlow (com.example.sharenshop.presentation.home  
BigDecimal 6com.example.sharenshop.presentation.home.HomeViewModel  Boolean 6com.example.sharenshop.presentation.home.HomeViewModel  Inject 6com.example.sharenshop.presentation.home.HomeViewModel  Int 6com.example.sharenshop.presentation.home.HomeViewModel  MutableStateFlow 6com.example.sharenshop.presentation.home.HomeViewModel  	StateFlow 6com.example.sharenshop.presentation.home.HomeViewModel  StatisticsUseCases 6com.example.sharenshop.presentation.home.HomeViewModel  String 6com.example.sharenshop.presentation.home.HomeViewModel  _error 6com.example.sharenshop.presentation.home.HomeViewModel  
_isLoading 6com.example.sharenshop.presentation.home.HomeViewModel  _totalProductsSold 6com.example.sharenshop.presentation.home.HomeViewModel  _totalSales 6com.example.sharenshop.presentation.home.HomeViewModel  asStateFlow 6com.example.sharenshop.presentation.home.HomeViewModel  getASStateFlow 6com.example.sharenshop.presentation.home.HomeViewModel  getAsStateFlow 6com.example.sharenshop.presentation.home.HomeViewModel  Boolean +com.example.sharenshop.presentation.invoice  InvoiceViewModel +com.example.sharenshop.presentation.invoice  List +com.example.sharenshop.presentation.invoice  MutableStateFlow +com.example.sharenshop.presentation.invoice  String +com.example.sharenshop.presentation.invoice  asStateFlow +com.example.sharenshop.presentation.invoice  	emptyList +com.example.sharenshop.presentation.invoice  Boolean <com.example.sharenshop.presentation.invoice.InvoiceViewModel  Inject <com.example.sharenshop.presentation.invoice.InvoiceViewModel  Invoice <com.example.sharenshop.presentation.invoice.InvoiceViewModel  InvoiceUseCases <com.example.sharenshop.presentation.invoice.InvoiceViewModel  List <com.example.sharenshop.presentation.invoice.InvoiceViewModel  MutableStateFlow <com.example.sharenshop.presentation.invoice.InvoiceViewModel  	StateFlow <com.example.sharenshop.presentation.invoice.InvoiceViewModel  String <com.example.sharenshop.presentation.invoice.InvoiceViewModel  _error <com.example.sharenshop.presentation.invoice.InvoiceViewModel  	_invoices <com.example.sharenshop.presentation.invoice.InvoiceViewModel  
_isLoading <com.example.sharenshop.presentation.invoice.InvoiceViewModel  asStateFlow <com.example.sharenshop.presentation.invoice.InvoiceViewModel  	emptyList <com.example.sharenshop.presentation.invoice.InvoiceViewModel  getASStateFlow <com.example.sharenshop.presentation.invoice.InvoiceViewModel  getAsStateFlow <com.example.sharenshop.presentation.invoice.InvoiceViewModel  getEMPTYList <com.example.sharenshop.presentation.invoice.InvoiceViewModel  getEmptyList <com.example.sharenshop.presentation.invoice.InvoiceViewModel  
MainViewModel (com.example.sharenshop.presentation.main  Inject 6com.example.sharenshop.presentation.main.MainViewModel  Boolean +com.example.sharenshop.presentation.product  List +com.example.sharenshop.presentation.product  MutableStateFlow +com.example.sharenshop.presentation.product  ProductViewModel +com.example.sharenshop.presentation.product  String +com.example.sharenshop.presentation.product  asStateFlow +com.example.sharenshop.presentation.product  	emptyList +com.example.sharenshop.presentation.product  Boolean <com.example.sharenshop.presentation.product.ProductViewModel  Inject <com.example.sharenshop.presentation.product.ProductViewModel  List <com.example.sharenshop.presentation.product.ProductViewModel  MutableStateFlow <com.example.sharenshop.presentation.product.ProductViewModel  Product <com.example.sharenshop.presentation.product.ProductViewModel  ProductUseCases <com.example.sharenshop.presentation.product.ProductViewModel  	StateFlow <com.example.sharenshop.presentation.product.ProductViewModel  String <com.example.sharenshop.presentation.product.ProductViewModel  _error <com.example.sharenshop.presentation.product.ProductViewModel  
_isLoading <com.example.sharenshop.presentation.product.ProductViewModel  	_products <com.example.sharenshop.presentation.product.ProductViewModel  _searchQuery <com.example.sharenshop.presentation.product.ProductViewModel  asStateFlow <com.example.sharenshop.presentation.product.ProductViewModel  	emptyList <com.example.sharenshop.presentation.product.ProductViewModel  getASStateFlow <com.example.sharenshop.presentation.product.ProductViewModel  getAsStateFlow <com.example.sharenshop.presentation.product.ProductViewModel  getEMPTYList <com.example.sharenshop.presentation.product.ProductViewModel  getEmptyList <com.example.sharenshop.presentation.product.ProductViewModel  Boolean *com.example.sharenshop.presentation.seller  List *com.example.sharenshop.presentation.seller  MutableStateFlow *com.example.sharenshop.presentation.seller  SellerViewModel *com.example.sharenshop.presentation.seller  String *com.example.sharenshop.presentation.seller  asStateFlow *com.example.sharenshop.presentation.seller  	emptyList *com.example.sharenshop.presentation.seller  Boolean :com.example.sharenshop.presentation.seller.SellerViewModel  Inject :com.example.sharenshop.presentation.seller.SellerViewModel  List :com.example.sharenshop.presentation.seller.SellerViewModel  MutableStateFlow :com.example.sharenshop.presentation.seller.SellerViewModel  Seller :com.example.sharenshop.presentation.seller.SellerViewModel  SellerUseCases :com.example.sharenshop.presentation.seller.SellerViewModel  	StateFlow :com.example.sharenshop.presentation.seller.SellerViewModel  String :com.example.sharenshop.presentation.seller.SellerViewModel  _error :com.example.sharenshop.presentation.seller.SellerViewModel  
_isLoading :com.example.sharenshop.presentation.seller.SellerViewModel  _sellers :com.example.sharenshop.presentation.seller.SellerViewModel  asStateFlow :com.example.sharenshop.presentation.seller.SellerViewModel  	emptyList :com.example.sharenshop.presentation.seller.SellerViewModel  getASStateFlow :com.example.sharenshop.presentation.seller.SellerViewModel  getAsStateFlow :com.example.sharenshop.presentation.seller.SellerViewModel  getEMPTYList :com.example.sharenshop.presentation.seller.SellerViewModel  getEmptyList :com.example.sharenshop.presentation.seller.SellerViewModel  
BigDecimal .com.example.sharenshop.presentation.statistics  Boolean .com.example.sharenshop.presentation.statistics  Int .com.example.sharenshop.presentation.statistics  Map .com.example.sharenshop.presentation.statistics  MutableStateFlow .com.example.sharenshop.presentation.statistics  StatisticsViewModel .com.example.sharenshop.presentation.statistics  String .com.example.sharenshop.presentation.statistics  asStateFlow .com.example.sharenshop.presentation.statistics  emptyMap .com.example.sharenshop.presentation.statistics  
BigDecimal Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  Boolean Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  Inject Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  Int Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  Map Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  MutableStateFlow Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  	StateFlow Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  StatisticsUseCases Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  String Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  _error Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  
_isLoading Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  _topSellingProducts Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  _totalProductsSold Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  _totalSalesAmount Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  asStateFlow Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  emptyMap Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  getASStateFlow Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  getAsStateFlow Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  getEMPTYMap Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  getEmptyMap Bcom.example.sharenshop.presentation.statistics.StatisticsViewModel  Boolean 3com.example.sharenshop.presentation.user_management  List 3com.example.sharenshop.presentation.user_management  MutableStateFlow 3com.example.sharenshop.presentation.user_management  String 3com.example.sharenshop.presentation.user_management  UserManagementViewModel 3com.example.sharenshop.presentation.user_management  asStateFlow 3com.example.sharenshop.presentation.user_management  	emptyList 3com.example.sharenshop.presentation.user_management  Boolean Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  Inject Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  List Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  MutableStateFlow Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  	StateFlow Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  String Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  User Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  UserUseCases Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  _error Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  
_isLoading Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  _users Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  asStateFlow Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  	emptyList Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  getASStateFlow Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  getAsStateFlow Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  getEMPTYList Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  getEmptyList Kcom.example.sharenshop.presentation.user_management.UserManagementViewModel  StatCard $com.example.sharenshop.ui.components  String $com.example.sharenshop.ui.components  formatCurrency $com.example.sharenshop.ui.components  NavGraph $com.example.sharenshop.ui.navigation  Screen $com.example.sharenshop.ui.navigation  String $com.example.sharenshop.ui.navigation  Screen +com.example.sharenshop.ui.navigation.Screen  String +com.example.sharenshop.ui.navigation.Screen  AccountingScreen !com.example.sharenshop.ui.screens  AddEditProductDialog !com.example.sharenshop.ui.screens  AdminAccountsTab !com.example.sharenshop.ui.screens  AdminAllPaymentsTab !com.example.sharenshop.ui.screens  AdminFinancialDashboardTab !com.example.sharenshop.ui.screens  AdminPaymentStatsTab !com.example.sharenshop.ui.screens  AdminPendingSettlementsTab !com.example.sharenshop.ui.screens  AdminReportsTab !com.example.sharenshop.ui.screens  AdminSettlementHistoryTab !com.example.sharenshop.ui.screens  AdminSettlementReviewCard !com.example.sharenshop.ui.screens  AdminSettlementStatsTab !com.example.sharenshop.ui.screens  AdminTransactionsTab !com.example.sharenshop.ui.screens  AllMessagesTab !com.example.sharenshop.ui.screens  AnimatedDot !com.example.sharenshop.ui.screens  ApprovalRequestCard !com.example.sharenshop.ui.screens  ApprovalRequestsTab !com.example.sharenshop.ui.screens  
AuthScreen !com.example.sharenshop.ui.screens  Boolean !com.example.sharenshop.ui.screens  BottomNavigationBar !com.example.sharenshop.ui.screens  CashierApprovalRequestsTab !com.example.sharenshop.ui.screens  CashierMessagesTab !com.example.sharenshop.ui.screens  CashierMyAccountTab !com.example.sharenshop.ui.screens  CashierMyRequestsTab !com.example.sharenshop.ui.screens  CashierMyTransactionsTab !com.example.sharenshop.ui.screens  CashierNewSettlementTab !com.example.sharenshop.ui.screens  CashierPaymentHistoryTab !com.example.sharenshop.ui.screens  CashierPendingPaymentsTab !com.example.sharenshop.ui.screens  CashierSettlementHistoryTab !com.example.sharenshop.ui.screens  
Composable !com.example.sharenshop.ui.screens  CreateInvoiceDialog !com.example.sharenshop.ui.screens  CustomerApprovalStatusTab !com.example.sharenshop.ui.screens  CustomerManagementScreen !com.example.sharenshop.ui.screens  CustomerMessagesTab !com.example.sharenshop.ui.screens  CustomerNewPaymentTab !com.example.sharenshop.ui.screens  CustomerNotificationsTab !com.example.sharenshop.ui.screens  CustomerPaymentHistoryTab !com.example.sharenshop.ui.screens  CustomerSummaryCard !com.example.sharenshop.ui.screens  DashboardContent !com.example.sharenshop.ui.screens  DetailedInvoiceCard !com.example.sharenshop.ui.screens  DetailedInvoicesTab !com.example.sharenshop.ui.screens  DetailedPaymentCard !com.example.sharenshop.ui.screens  DetailedPaymentsTab !com.example.sharenshop.ui.screens  DetailedStatsTab !com.example.sharenshop.ui.screens  
DetailsScreen !com.example.sharenshop.ui.screens  ExperimentalMaterial3Api !com.example.sharenshop.ui.screens  FashionIcon !com.example.sharenshop.ui.screens  FinancialReportTab !com.example.sharenshop.ui.screens  FinancialSummaryCard !com.example.sharenshop.ui.screens  FinancialSummaryItem !com.example.sharenshop.ui.screens  Float !com.example.sharenshop.ui.screens  
HomeScreen !com.example.sharenshop.ui.screens  Int !com.example.sharenshop.ui.screens  Invoice !com.example.sharenshop.ui.screens  InvoiceCard !com.example.sharenshop.ui.screens  InvoiceListContent !com.example.sharenshop.ui.screens  
InvoiceScreen !com.example.sharenshop.ui.screens  InvoiceStatsRow !com.example.sharenshop.ui.screens  InvoiceSummaryCard !com.example.sharenshop.ui.screens  List !com.example.sharenshop.ui.screens  LogoutConfirmationDialog !com.example.sharenshop.ui.screens  Long !com.example.sharenshop.ui.screens  MainMenuGrid !com.example.sharenshop.ui.screens  
MainScreen !com.example.sharenshop.ui.screens  MainStatisticsScreen !com.example.sharenshop.ui.screens  MainUserManagementScreen !com.example.sharenshop.ui.screens  MenuItem !com.example.sharenshop.ui.screens  MenuItemCard !com.example.sharenshop.ui.screens  MessageCard !com.example.sharenshop.ui.screens  MessagesScreen !com.example.sharenshop.ui.screens  NotificationsTab !com.example.sharenshop.ui.screens  OptIn !com.example.sharenshop.ui.screens  PaymentDetails !com.example.sharenshop.ui.screens  PaymentHistoryCard !com.example.sharenshop.ui.screens  
PaymentScreen !com.example.sharenshop.ui.screens  
PaymentStatus !com.example.sharenshop.ui.screens  PaymentStatusChip !com.example.sharenshop.ui.screens  PendingApprovalScreen !com.example.sharenshop.ui.screens  PreviewHomeScreen !com.example.sharenshop.ui.screens  PreviewSellerScreen !com.example.sharenshop.ui.screens  PreviewStatisticsScreen !com.example.sharenshop.ui.screens  PreviewUserManagementScreen !com.example.sharenshop.ui.screens  ProductCard !com.example.sharenshop.ui.screens  
ProductScreen !com.example.sharenshop.ui.screens  ProductStatsRow !com.example.sharenshop.ui.screens  ProfileInfoRow !com.example.sharenshop.ui.screens  	QuickStat !com.example.sharenshop.ui.screens  
QuickStatCard !com.example.sharenshop.ui.screens  QuickStatsCards !com.example.sharenshop.ui.screens  RecentActivities !com.example.sharenshop.ui.screens  RecentActivityItem !com.example.sharenshop.ui.screens  ReportButton !com.example.sharenshop.ui.screens  	SearchBar !com.example.sharenshop.ui.screens  SellerBalanceItem !com.example.sharenshop.ui.screens  
SellerCard !com.example.sharenshop.ui.screens  SellerCustomersTab !com.example.sharenshop.ui.screens  SellerDetailsScreen !com.example.sharenshop.ui.screens  SellerInvoicesTab !com.example.sharenshop.ui.screens  SellerPerformanceRow !com.example.sharenshop.ui.screens  SellerScreen !com.example.sharenshop.ui.screens  SellerStatsTab !com.example.sharenshop.ui.screens  SellersManagementScreen !com.example.sharenshop.ui.screens  SettlementDetails !com.example.sharenshop.ui.screens  SettlementRequestCard !com.example.sharenshop.ui.screens  SettlementScreen !com.example.sharenshop.ui.screens  SettlementStatus !com.example.sharenshop.ui.screens  SettlementStatusChip !com.example.sharenshop.ui.screens  StatCard !com.example.sharenshop.ui.screens  StatisticsScreen !com.example.sharenshop.ui.screens  
StatusChip !com.example.sharenshop.ui.screens  String !com.example.sharenshop.ui.screens  Suppress !com.example.sharenshop.ui.screens  SystemMessagesTab !com.example.sharenshop.ui.screens  TimeStatsRow !com.example.sharenshop.ui.screens  TransactionCategory !com.example.sharenshop.ui.screens  TransactionItem !com.example.sharenshop.ui.screens  Unit !com.example.sharenshop.ui.screens  UserManagementScreen !com.example.sharenshop.ui.screens  UserProfileDialog !com.example.sharenshop.ui.screens  UserRole !com.example.sharenshop.ui.screens  
WelcomeHeader !com.example.sharenshop.ui.screens  androidx !com.example.sharenshop.ui.screens  drawFashionRing !com.example.sharenshop.ui.screens  
formatDate !com.example.sharenshop.ui.screens  getBottomNavItemsForRole !com.example.sharenshop.ui.screens  getCashierStats !com.example.sharenshop.ui.screens  getCustomerStats !com.example.sharenshop.ui.screens  getMenuItemsForRole !com.example.sharenshop.ui.screens  getSuperAdminStats !com.example.sharenshop.ui.screens  getUserDisplayName !com.example.sharenshop.ui.screens  getUserEmail !com.example.sharenshop.ui.screens  getUserPhone !com.example.sharenshop.ui.screens  Color *com.example.sharenshop.ui.screens.MenuItem  ImageVector *com.example.sharenshop.ui.screens.MenuItem  String *com.example.sharenshop.ui.screens.MenuItem  Color +com.example.sharenshop.ui.screens.QuickStat  ImageVector +com.example.sharenshop.ui.screens.QuickStat  String +com.example.sharenshop.ui.screens.QuickStat  Boolean com.example.sharenshop.ui.theme  Color com.example.sharenshop.ui.theme  ColorHelper com.example.sharenshop.ui.theme  	DarkBrown com.example.sharenshop.ui.theme  
DarkBrownDark com.example.sharenshop.ui.theme  DarkBrownLight com.example.sharenshop.ui.theme  DarkColorScheme com.example.sharenshop.ui.theme  DeepBlue com.example.sharenshop.ui.theme  DeepBlueDark com.example.sharenshop.ui.theme  
DeepBlueLight com.example.sharenshop.ui.theme  
FutureDusk com.example.sharenshop.ui.theme  FutureDuskDark com.example.sharenshop.ui.theme  FutureDuskLight com.example.sharenshop.ui.theme  Int com.example.sharenshop.ui.theme  LightColorScheme com.example.sharenshop.ui.theme  SharenShopColors com.example.sharenshop.ui.theme  SharenShopTheme com.example.sharenshop.ui.theme  SoftGray com.example.sharenshop.ui.theme  SoftGrayDark com.example.sharenshop.ui.theme  
SoftGrayLight com.example.sharenshop.ui.theme  String com.example.sharenshop.ui.theme  
Typography com.example.sharenshop.ui.theme  Unit com.example.sharenshop.ui.theme  cashierColor com.example.sharenshop.ui.theme  
customerColor com.example.sharenshop.ui.theme  info com.example.sharenshop.ui.theme  	lossColor com.example.sharenshop.ui.theme  md_theme_dark_background com.example.sharenshop.ui.theme  md_theme_dark_error com.example.sharenshop.ui.theme  md_theme_dark_errorContainer com.example.sharenshop.ui.theme  md_theme_dark_inverseOnSurface com.example.sharenshop.ui.theme  md_theme_dark_inversePrimary com.example.sharenshop.ui.theme  md_theme_dark_inverseSurface com.example.sharenshop.ui.theme  md_theme_dark_onBackground com.example.sharenshop.ui.theme  md_theme_dark_onError com.example.sharenshop.ui.theme  md_theme_dark_onErrorContainer com.example.sharenshop.ui.theme  md_theme_dark_onPrimary com.example.sharenshop.ui.theme   md_theme_dark_onPrimaryContainer com.example.sharenshop.ui.theme  md_theme_dark_onSecondary com.example.sharenshop.ui.theme  "md_theme_dark_onSecondaryContainer com.example.sharenshop.ui.theme  md_theme_dark_onSurface com.example.sharenshop.ui.theme  md_theme_dark_onSurfaceVariant com.example.sharenshop.ui.theme  md_theme_dark_onTertiary com.example.sharenshop.ui.theme  !md_theme_dark_onTertiaryContainer com.example.sharenshop.ui.theme  md_theme_dark_outline com.example.sharenshop.ui.theme  md_theme_dark_outlineVariant com.example.sharenshop.ui.theme  md_theme_dark_primary com.example.sharenshop.ui.theme  md_theme_dark_primaryContainer com.example.sharenshop.ui.theme  md_theme_dark_scrim com.example.sharenshop.ui.theme  md_theme_dark_secondary com.example.sharenshop.ui.theme   md_theme_dark_secondaryContainer com.example.sharenshop.ui.theme  md_theme_dark_shadow com.example.sharenshop.ui.theme  md_theme_dark_surface com.example.sharenshop.ui.theme  md_theme_dark_surfaceTint com.example.sharenshop.ui.theme  md_theme_dark_surfaceVariant com.example.sharenshop.ui.theme  md_theme_dark_tertiary com.example.sharenshop.ui.theme  md_theme_dark_tertiaryContainer com.example.sharenshop.ui.theme  md_theme_light_background com.example.sharenshop.ui.theme  md_theme_light_error com.example.sharenshop.ui.theme  md_theme_light_errorContainer com.example.sharenshop.ui.theme  md_theme_light_inverseOnSurface com.example.sharenshop.ui.theme  md_theme_light_inversePrimary com.example.sharenshop.ui.theme  md_theme_light_inverseSurface com.example.sharenshop.ui.theme  md_theme_light_onBackground com.example.sharenshop.ui.theme  md_theme_light_onError com.example.sharenshop.ui.theme  md_theme_light_onErrorContainer com.example.sharenshop.ui.theme  md_theme_light_onPrimary com.example.sharenshop.ui.theme  !md_theme_light_onPrimaryContainer com.example.sharenshop.ui.theme  md_theme_light_onSecondary com.example.sharenshop.ui.theme  #md_theme_light_onSecondaryContainer com.example.sharenshop.ui.theme  md_theme_light_onSurface com.example.sharenshop.ui.theme  md_theme_light_onSurfaceVariant com.example.sharenshop.ui.theme  md_theme_light_onTertiary com.example.sharenshop.ui.theme  "md_theme_light_onTertiaryContainer com.example.sharenshop.ui.theme  md_theme_light_outline com.example.sharenshop.ui.theme  md_theme_light_outlineVariant com.example.sharenshop.ui.theme  md_theme_light_primary com.example.sharenshop.ui.theme  md_theme_light_primaryContainer com.example.sharenshop.ui.theme  md_theme_light_scrim com.example.sharenshop.ui.theme  md_theme_light_secondary com.example.sharenshop.ui.theme  !md_theme_light_secondaryContainer com.example.sharenshop.ui.theme  md_theme_light_shadow com.example.sharenshop.ui.theme  md_theme_light_surface com.example.sharenshop.ui.theme  md_theme_light_surfaceTint com.example.sharenshop.ui.theme  md_theme_light_surfaceVariant com.example.sharenshop.ui.theme  md_theme_light_tertiary com.example.sharenshop.ui.theme   md_theme_light_tertiaryContainer com.example.sharenshop.ui.theme  neutralColor com.example.sharenshop.ui.theme  profitColor com.example.sharenshop.ui.theme  success com.example.sharenshop.ui.theme  superAdminColor com.example.sharenshop.ui.theme  warning com.example.sharenshop.ui.theme  Color +com.example.sharenshop.ui.theme.ColorHelper  
Composable +com.example.sharenshop.ui.theme.ColorHelper  Int +com.example.sharenshop.ui.theme.ColorHelper  String +com.example.sharenshop.ui.theme.ColorHelper  CashierColor 0com.example.sharenshop.ui.theme.SharenShopColors  Color 0com.example.sharenshop.ui.theme.SharenShopColors  
CustomerColor 0com.example.sharenshop.ui.theme.SharenShopColors  DeepBlue 0com.example.sharenshop.ui.theme.SharenShopColors  Info 0com.example.sharenshop.ui.theme.SharenShopColors  	LossColor 0com.example.sharenshop.ui.theme.SharenShopColors  NeutralColor 0com.example.sharenshop.ui.theme.SharenShopColors  ProfitColor 0com.example.sharenshop.ui.theme.SharenShopColors  SoftGray 0com.example.sharenshop.ui.theme.SharenShopColors  Success 0com.example.sharenshop.ui.theme.SharenShopColors  SuperAdminColor 0com.example.sharenshop.ui.theme.SharenShopColors  Warning 0com.example.sharenshop.ui.theme.SharenShopColors  invoke 0com.example.sharenshop.ui.theme.SharenShopColors  Boolean com.example.sharenshop.ui.utils  
Composable com.example.sharenshop.ui.utils  ExperimentalMaterial3Api com.example.sharenshop.ui.utils  Float com.example.sharenshop.ui.utils  Int com.example.sharenshop.ui.utils  getAdaptiveCardHeight com.example.sharenshop.ui.utils  getAdaptivePadding com.example.sharenshop.ui.utils  getAdaptiveSpacing com.example.sharenshop.ui.utils  getGridColumns com.example.sharenshop.ui.utils  getScreenHeight com.example.sharenshop.ui.utils  getScreenWidth com.example.sharenshop.ui.utils  getStatsCardHeight com.example.sharenshop.ui.utils  getStatsCardWidth com.example.sharenshop.ui.utils  getTextSizeMultiplier com.example.sharenshop.ui.utils  isLandscape com.example.sharenshop.ui.utils  isTablet com.example.sharenshop.ui.utils  shouldUseHorizontalLayout com.example.sharenshop.ui.utils  Boolean com.example.sharenshop.utils  PermissionHelper com.example.sharenshop.utils  Set com.example.sharenshop.utils  String com.example.sharenshop.utils  SupabaseTestHelper com.example.sharenshop.utils  Boolean -com.example.sharenshop.utils.PermissionHelper  
Permission -com.example.sharenshop.utils.PermissionHelper  Set -com.example.sharenshop.utils.PermissionHelper  User -com.example.sharenshop.utils.PermissionHelper  UserRole -com.example.sharenshop.utils.PermissionHelper  String /com.example.sharenshop.utils.SupabaseTestHelper  Binds dagger  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  SupabaseClient io.github.jan.supabase  SupabaseClientBuilder io.github.jan.supabase  createSupabaseClient io.github.jan.supabase  auth %io.github.jan.supabase.SupabaseClient  getAUTH %io.github.jan.supabase.SupabaseClient  getAuth %io.github.jan.supabase.SupabaseClient  getPOSTGREST %io.github.jan.supabase.SupabaseClient  getPostgrest %io.github.jan.supabase.SupabaseClient  	postgrest %io.github.jan.supabase.SupabaseClient  Auth ,io.github.jan.supabase.SupabaseClientBuilder  	Postgrest ,io.github.jan.supabase.SupabaseClientBuilder  install ,io.github.jan.supabase.SupabaseClientBuilder  Auth io.github.jan.supabase.gotrue  auth io.github.jan.supabase.gotrue  	Companion "io.github.jan.supabase.gotrue.Auth  	Postgrest  io.github.jan.supabase.postgrest  	postgrest  io.github.jan.supabase.postgrest  	Companion *io.github.jan.supabase.postgrest.Postgrest  InputStream java.io  OutputStream java.io  AppSettings 	java.lang  Auth 	java.lang  
BigDecimal 	java.lang  BigDecimalConverter 	java.lang  BuildConfig 	java.lang  Color 	java.lang  Context 	java.lang  EncryptionSettings 	java.lang  ExperimentalMaterial3Api 	java.lang  MutableStateFlow 	java.lang  OnConflictStrategy 	java.lang  
PREFS_NAME 	java.lang  
Permission 	java.lang  	Postgrest 	java.lang  SharenShopColors 	java.lang  SingletonComponent 	java.lang  UserRole 	java.lang  androidx 	java.lang  asStateFlow 	java.lang  createSupabaseClient 	java.lang  	emptyList 	java.lang  emptyMap 	java.lang  provideDelegate 	java.lang  setOf 	java.lang  
BigDecimal 	java.math  ZERO java.math.BigDecimal  NumberFormat 	java.text  SimpleDateFormat 	java.text  
Composable 	java.util  CustomerApprovalRequest 	java.util  ExperimentalMaterial3Api 	java.util  Invoice 	java.util  PaymentDetails 	java.util  
PaymentStatus 	java.util  Result 	java.util  SettlementDetails 	java.util  SettlementStatus 	java.util  TransactionCategory 	java.util  UUID 	java.util  User 	java.util  UserRole 	java.util  androidx 	java.util  Inject javax.inject  	Singleton javax.inject  AppSettings kotlin  Array kotlin  Auth kotlin  
BigDecimal kotlin  BigDecimalConverter kotlin  Boolean kotlin  BuildConfig kotlin  Color kotlin  Context kotlin  Double kotlin  EncryptionSettings kotlin  ExperimentalMaterial3Api kotlin  Float kotlin  	Function1 kotlin  Int kotlin  Long kotlin  MutableStateFlow kotlin  Nothing kotlin  OnConflictStrategy kotlin  OptIn kotlin  
PREFS_NAME kotlin  
Permission kotlin  	Postgrest kotlin  Result kotlin  SharenShopColors kotlin  SingletonComponent kotlin  String kotlin  Suppress kotlin  Unit kotlin  UserRole kotlin  androidx kotlin  arrayOf kotlin  asStateFlow kotlin  createSupabaseClient kotlin  	emptyList kotlin  emptyMap kotlin  provideDelegate kotlin  setOf kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  AppSettings kotlin.annotation  Auth kotlin.annotation  
BigDecimal kotlin.annotation  BigDecimalConverter kotlin.annotation  BuildConfig kotlin.annotation  Color kotlin.annotation  Context kotlin.annotation  EncryptionSettings kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  MutableStateFlow kotlin.annotation  OnConflictStrategy kotlin.annotation  
PREFS_NAME kotlin.annotation  
Permission kotlin.annotation  	Postgrest kotlin.annotation  Result kotlin.annotation  SharenShopColors kotlin.annotation  SingletonComponent kotlin.annotation  UserRole kotlin.annotation  androidx kotlin.annotation  asStateFlow kotlin.annotation  createSupabaseClient kotlin.annotation  	emptyList kotlin.annotation  emptyMap kotlin.annotation  provideDelegate kotlin.annotation  setOf kotlin.annotation  AppSettings kotlin.collections  Auth kotlin.collections  
BigDecimal kotlin.collections  BigDecimalConverter kotlin.collections  BuildConfig kotlin.collections  Color kotlin.collections  Context kotlin.collections  EncryptionSettings kotlin.collections  ExperimentalMaterial3Api kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableStateFlow kotlin.collections  OnConflictStrategy kotlin.collections  
PREFS_NAME kotlin.collections  
Permission kotlin.collections  	Postgrest kotlin.collections  Result kotlin.collections  Set kotlin.collections  SharenShopColors kotlin.collections  SingletonComponent kotlin.collections  UserRole kotlin.collections  androidx kotlin.collections  asStateFlow kotlin.collections  createSupabaseClient kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  provideDelegate kotlin.collections  setOf kotlin.collections  AppSettings kotlin.comparisons  Auth kotlin.comparisons  
BigDecimal kotlin.comparisons  BigDecimalConverter kotlin.comparisons  BuildConfig kotlin.comparisons  Color kotlin.comparisons  Context kotlin.comparisons  EncryptionSettings kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  MutableStateFlow kotlin.comparisons  OnConflictStrategy kotlin.comparisons  
PREFS_NAME kotlin.comparisons  
Permission kotlin.comparisons  	Postgrest kotlin.comparisons  Result kotlin.comparisons  SharenShopColors kotlin.comparisons  SingletonComponent kotlin.comparisons  UserRole kotlin.comparisons  androidx kotlin.comparisons  asStateFlow kotlin.comparisons  createSupabaseClient kotlin.comparisons  	emptyList kotlin.comparisons  emptyMap kotlin.comparisons  provideDelegate kotlin.comparisons  setOf kotlin.comparisons  AppSettings 	kotlin.io  Auth 	kotlin.io  
BigDecimal 	kotlin.io  BigDecimalConverter 	kotlin.io  BuildConfig 	kotlin.io  Color 	kotlin.io  Context 	kotlin.io  EncryptionSettings 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  MutableStateFlow 	kotlin.io  OnConflictStrategy 	kotlin.io  
PREFS_NAME 	kotlin.io  
Permission 	kotlin.io  	Postgrest 	kotlin.io  Result 	kotlin.io  SharenShopColors 	kotlin.io  SingletonComponent 	kotlin.io  UserRole 	kotlin.io  androidx 	kotlin.io  asStateFlow 	kotlin.io  createSupabaseClient 	kotlin.io  	emptyList 	kotlin.io  emptyMap 	kotlin.io  provideDelegate 	kotlin.io  setOf 	kotlin.io  AppSettings 
kotlin.jvm  Auth 
kotlin.jvm  
BigDecimal 
kotlin.jvm  BigDecimalConverter 
kotlin.jvm  BuildConfig 
kotlin.jvm  Color 
kotlin.jvm  Context 
kotlin.jvm  EncryptionSettings 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  
PREFS_NAME 
kotlin.jvm  
Permission 
kotlin.jvm  	Postgrest 
kotlin.jvm  Result 
kotlin.jvm  SharenShopColors 
kotlin.jvm  SingletonComponent 
kotlin.jvm  UserRole 
kotlin.jvm  androidx 
kotlin.jvm  asStateFlow 
kotlin.jvm  createSupabaseClient 
kotlin.jvm  	emptyList 
kotlin.jvm  emptyMap 
kotlin.jvm  provideDelegate 
kotlin.jvm  setOf 
kotlin.jvm  PI kotlin.math  cos kotlin.math  sin kotlin.math  ReadOnlyProperty kotlin.properties  getPROVIDEDelegate "kotlin.properties.ReadOnlyProperty  getProvideDelegate "kotlin.properties.ReadOnlyProperty  getValue "kotlin.properties.ReadOnlyProperty  provideDelegate "kotlin.properties.ReadOnlyProperty  AppSettings 
kotlin.ranges  Auth 
kotlin.ranges  
BigDecimal 
kotlin.ranges  BigDecimalConverter 
kotlin.ranges  BuildConfig 
kotlin.ranges  Color 
kotlin.ranges  Context 
kotlin.ranges  EncryptionSettings 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  
PREFS_NAME 
kotlin.ranges  
Permission 
kotlin.ranges  	Postgrest 
kotlin.ranges  Result 
kotlin.ranges  SharenShopColors 
kotlin.ranges  SingletonComponent 
kotlin.ranges  UserRole 
kotlin.ranges  androidx 
kotlin.ranges  asStateFlow 
kotlin.ranges  createSupabaseClient 
kotlin.ranges  	emptyList 
kotlin.ranges  emptyMap 
kotlin.ranges  provideDelegate 
kotlin.ranges  setOf 
kotlin.ranges  KClass kotlin.reflect  AppSettings kotlin.sequences  Auth kotlin.sequences  
BigDecimal kotlin.sequences  BigDecimalConverter kotlin.sequences  BuildConfig kotlin.sequences  Color kotlin.sequences  Context kotlin.sequences  EncryptionSettings kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  MutableStateFlow kotlin.sequences  OnConflictStrategy kotlin.sequences  
PREFS_NAME kotlin.sequences  
Permission kotlin.sequences  	Postgrest kotlin.sequences  Result kotlin.sequences  SharenShopColors kotlin.sequences  SingletonComponent kotlin.sequences  UserRole kotlin.sequences  androidx kotlin.sequences  asStateFlow kotlin.sequences  createSupabaseClient kotlin.sequences  	emptyList kotlin.sequences  emptyMap kotlin.sequences  provideDelegate kotlin.sequences  setOf kotlin.sequences  AppSettings kotlin.text  Auth kotlin.text  
BigDecimal kotlin.text  BigDecimalConverter kotlin.text  BuildConfig kotlin.text  Color kotlin.text  Context kotlin.text  EncryptionSettings kotlin.text  ExperimentalMaterial3Api kotlin.text  MutableStateFlow kotlin.text  OnConflictStrategy kotlin.text  
PREFS_NAME kotlin.text  
Permission kotlin.text  	Postgrest kotlin.text  Result kotlin.text  SharenShopColors kotlin.text  SingletonComponent kotlin.text  UserRole kotlin.text  androidx kotlin.text  asStateFlow kotlin.text  createSupabaseClient kotlin.text  	emptyList kotlin.text  emptyMap kotlin.text  provideDelegate kotlin.text  setOf kotlin.text  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  catch kotlinx.coroutines.flow  combine kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  launchIn kotlinx.coroutines.flow  map kotlinx.coroutines.flow  onEach kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  
Contextual kotlinx.serialization  Serializable kotlinx.serialization  
serializer kotlinx.serialization.builtins  Json kotlinx.serialization.json  ExperimentalMaterial3Api androidx.compose.animation.core  	ErrorCard !com.example.sharenshop.ui.screens  EnhancedApprovalRequestCard !com.example.sharenshop.ui.screens  NotificationHelper 0com.example.sharenshop.presentation.notification  AnimatedTabRow !com.example.sharenshop.ui.screens  TabInfo !com.example.sharenshop.ui.screens  NotificationRepositoryImpl &com.example.sharenshop.data.repository  NotificationUiState 0com.example.sharenshop.presentation.notification  NotificationViewModel 0com.example.sharenshop.presentation.notification  ApprovalStatusCard !com.example.sharenshop.ui.screens  EnhancedMessageCard !com.example.sharenshop.ui.screens  AnimatedContent androidx.compose.animation  slideInHorizontally androidx.compose.animation  slideOutHorizontally androidx.compose.animation  with androidx.compose.animation  alpha androidx.compose.ui.draw  Notification androidx.lifecycle.ViewModel  NotificationRepository androidx.lifecycle.ViewModel  NotificationType androidx.lifecycle.ViewModel  NotificationUiState androidx.lifecycle.ViewModel  UserRepository androidx.lifecycle.ViewModel  NotificationDao androidx.room.RoomDatabase  NotificationDao 6com.example.sharenshop.data.local.database.AppDatabase  	Companion .com.example.sharenshop.data.model.Notification  Flow Acom.example.sharenshop.data.repository.NotificationRepositoryImpl  Inject Acom.example.sharenshop.data.repository.NotificationRepositoryImpl  Int Acom.example.sharenshop.data.repository.NotificationRepositoryImpl  List Acom.example.sharenshop.data.repository.NotificationRepositoryImpl  Long Acom.example.sharenshop.data.repository.NotificationRepositoryImpl  Notification Acom.example.sharenshop.data.repository.NotificationRepositoryImpl  NotificationDao Acom.example.sharenshop.data.repository.NotificationRepositoryImpl  NotificationType Acom.example.sharenshop.data.repository.NotificationRepositoryImpl  String Acom.example.sharenshop.data.repository.NotificationRepositoryImpl  NotificationDao #com.example.sharenshop.di.DaoModule  NotificationRepository *com.example.sharenshop.di.RepositoryModule  NotificationRepositoryImpl *com.example.sharenshop.di.RepositoryModule  Boolean 0com.example.sharenshop.presentation.notification  Int 0com.example.sharenshop.presentation.notification  List 0com.example.sharenshop.presentation.notification  MutableStateFlow 0com.example.sharenshop.presentation.notification  	StateFlow 0com.example.sharenshop.presentation.notification  String 0com.example.sharenshop.presentation.notification  asStateFlow 0com.example.sharenshop.presentation.notification  Notification Ccom.example.sharenshop.presentation.notification.NotificationHelper  String Ccom.example.sharenshop.presentation.notification.NotificationHelper  Boolean Dcom.example.sharenshop.presentation.notification.NotificationUiState  Int Dcom.example.sharenshop.presentation.notification.NotificationUiState  List Dcom.example.sharenshop.presentation.notification.NotificationUiState  Notification Dcom.example.sharenshop.presentation.notification.NotificationUiState  String Dcom.example.sharenshop.presentation.notification.NotificationUiState  Inject Fcom.example.sharenshop.presentation.notification.NotificationViewModel  List Fcom.example.sharenshop.presentation.notification.NotificationViewModel  MutableStateFlow Fcom.example.sharenshop.presentation.notification.NotificationViewModel  Notification Fcom.example.sharenshop.presentation.notification.NotificationViewModel  NotificationRepository Fcom.example.sharenshop.presentation.notification.NotificationViewModel  NotificationType Fcom.example.sharenshop.presentation.notification.NotificationViewModel  NotificationUiState Fcom.example.sharenshop.presentation.notification.NotificationViewModel  	StateFlow Fcom.example.sharenshop.presentation.notification.NotificationViewModel  String Fcom.example.sharenshop.presentation.notification.NotificationViewModel  UserRepository Fcom.example.sharenshop.presentation.notification.NotificationViewModel  _currentUserId Fcom.example.sharenshop.presentation.notification.NotificationViewModel  _uiState Fcom.example.sharenshop.presentation.notification.NotificationViewModel  asStateFlow Fcom.example.sharenshop.presentation.notification.NotificationViewModel  getASStateFlow Fcom.example.sharenshop.presentation.notification.NotificationViewModel  getAsStateFlow Fcom.example.sharenshop.presentation.notification.NotificationViewModel  Color )com.example.sharenshop.ui.screens.TabInfo  ImageVector )com.example.sharenshop.ui.screens.TabInfo  String )com.example.sharenshop.ui.screens.TabInfo  NotificationUiState 	java.lang  MutableStateFlow 	java.util  NotificationUiState 	java.util  	StateFlow 	java.util  asStateFlow 	java.util  NotificationUiState kotlin  NotificationUiState kotlin.annotation  NotificationUiState kotlin.collections  NotificationUiState kotlin.comparisons  NotificationUiState 	kotlin.io  NotificationUiState 
kotlin.jvm  NotificationUiState 
kotlin.ranges  NotificationUiState kotlin.sequences  NotificationUiState kotlin.text  NotificationUiState kotlinx.coroutines.flow  
formatTime !com.example.sharenshop.ui.screens  getNotificationColor !com.example.sharenshop.ui.screens  getNotificationIcon !com.example.sharenshop.ui.screens  LoadingIndicator !com.example.sharenshop.ui.screens  ExperimentalAnimationApi androidx.compose.animation                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   