package com.example.sharenshop.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00004\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\"\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0006\u0010\u0007\u001a\u001c\u0010\b\u001a\u00020\u00012\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u001a(\u0010\r\u001a\u00020\u00012\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\u000f2\u000e\b\u0002\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u000fH\u0007\u001a \u0010\u0011\u001a\u00020\u00012\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0004\u001a\u00020\u0005\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0014\u0010\u0015\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0016"}, d2 = {"AnimatedDot", "", "delay", "", "color", "Landroidx/compose/ui/graphics/Color;", "AnimatedDot-4WTKRHQ", "(IJ)V", "FashionIcon", "modifier", "Landroidx/compose/ui/Modifier;", "alpha", "", "PendingApprovalScreen", "onRetryLogin", "Lkotlin/Function0;", "onContactSupport", "drawFashionRing", "drawScope", "Landroidx/compose/ui/graphics/drawscope/DrawScope;", "drawFashionRing-4WTKRHQ", "(Landroidx/compose/ui/graphics/drawscope/DrawScope;J)V", "app_debug"})
public final class PendingApprovalScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void PendingApprovalScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRetryLogin, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onContactSupport) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void FashionIcon(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, float alpha) {
    }
}