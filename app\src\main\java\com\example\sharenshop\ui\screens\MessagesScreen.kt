package com.example.sharenshop.ui.screens

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.core.*
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.with
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.sharenshop.data.model.Notification
import com.example.sharenshop.data.model.NotificationType
import com.example.sharenshop.data.model.UserRole
import com.example.sharenshop.presentation.notification.NotificationViewModel
import com.example.sharenshop.presentation.notification.NotificationUiState
import java.text.SimpleDateFormat
import java.util.*

data class TabInfo(
    val title: String,
    val icon: ImageVector,
    val color: Color
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MessagesScreen(
    userRole: UserRole,
    viewModel: NotificationViewModel = hiltViewModel()
) {
    var selectedTab by remember { mutableStateOf(0) }
    val uiState by viewModel.uiState.collectAsState()
    
    // انیمیشن‌های زیبا
    val infiniteTransition = rememberInfiniteTransition(label = "messages_animation")
    
    val pulseAlpha by infiniteTransition.animateFloat(
        initialValue = 0.6f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse"
    )

    val tabs = when (userRole) {
        UserRole.SUPER_ADMIN -> listOf(
            TabInfo("همه پیام‌ها", Icons.Default.Email, Color(0xFF2196F3)),
            TabInfo("درخواست‌ها", Icons.Default.Person, Color(0xFFFF9800)),
            TabInfo("سیستم", Icons.Default.Settings, Color(0xFF9C27B0))
        )
        UserRole.CASHIER -> listOf(
            TabInfo("پیام‌های من", Icons.Default.Email, Color(0xFF2196F3)),
            TabInfo("درخواست‌ها", Icons.Default.Person, Color(0xFFFF9800)),
            TabInfo("اعلان‌ها", Icons.Default.Notifications, Color(0xFF4CAF50))
        )
        UserRole.CUSTOMER -> listOf(
            TabInfo("پیام‌های من", Icons.Default.Email, Color(0xFF2196F3)),
            TabInfo("وضعیت تایید", Icons.Default.CheckCircle, Color(0xFFFF9800)),
            TabInfo("اعلان‌ها", Icons.Default.Notifications, Color(0xFF4CAF50))
        )
    }

    LaunchedEffect(userRole) {
        // TODO: Set current user ID from session
        viewModel.setCurrentUser("current_user_id")
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            Icons.Default.Email,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onPrimary,
                            modifier = Modifier
                                .size(24.dp)
                                .alpha(pulseAlpha)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("پیام‌ها و اعلان‌ها")
                        
                        // نمایش تعداد پیام‌های خوانده نشده
                        if (uiState.unreadCount > 0) {
                            Spacer(modifier = Modifier.width(8.dp))
                            Badge(
                                modifier = Modifier.alpha(pulseAlpha)
                            ) {
                                Text(
                                    text = uiState.unreadCount.toString(),
                                    style = MaterialTheme.typography.labelSmall
                                )
                            }
                        }
                    }
                },
                actions = {
                    // دکمه علامت‌گذاری همه به عنوان خوانده شده
                    IconButton(
                        onClick = { viewModel.markAllAsRead() }
                    ) {
                        Icon(
                            Icons.Default.Done,
                            contentDescription = "علامت‌گذاری همه",
                            tint = MaterialTheme.colorScheme.onPrimary
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            // Tab Row با انیمیشن
            AnimatedTabRow(
                selectedTabIndex = selectedTab,
                tabs = tabs,
                onTabSelected = { selectedTab = it }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // نمایش خطا
            uiState.error?.let { error ->
                ErrorCard(
                    error = error,
                    onDismiss = { viewModel.clearError() }
                )
                Spacer(modifier = Modifier.height(8.dp))
            }

            // محتوای تب‌ها
            @OptIn(ExperimentalAnimationApi::class)
            AnimatedContent(
                targetState = selectedTab,
                transitionSpec = {
                    slideInHorizontally(
                        initialOffsetX = { if (targetState > initialState) 300 else -300 },
                        animationSpec = tween(300)
                    ) with slideOutHorizontally(
                        targetOffsetX = { if (targetState > initialState) -300 else 300 },
                        animationSpec = tween(300)
                    )
                },
                label = "tab_content"
            ) { tabIndex ->
                when (userRole) {
                    UserRole.SUPER_ADMIN -> {
                        when (tabIndex) {
                            0 -> AllMessagesTab(viewModel, uiState)
                            1 -> ApprovalRequestsTab(viewModel, uiState)
                            2 -> SystemMessagesTab(viewModel, uiState)
                        }
                    }
                    UserRole.CASHIER -> {
                        when (tabIndex) {
                            0 -> CashierMessagesTab(viewModel, uiState)
                            1 -> CashierApprovalRequestsTab(viewModel, uiState)
                            2 -> NotificationsTab(viewModel, uiState)
                        }
                    }
                    UserRole.CUSTOMER -> {
                        when (tabIndex) {
                            0 -> CustomerMessagesTab(viewModel, uiState)
                            1 -> CustomerApprovalStatusTab(viewModel, uiState)
                            2 -> CustomerNotificationsTab(viewModel, uiState)
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun AnimatedTabRow(
    selectedTabIndex: Int,
    tabs: List<TabInfo>,
    onTabSelected: (Int) -> Unit
) {
    TabRow(
        selectedTabIndex = selectedTabIndex,
        containerColor = MaterialTheme.colorScheme.surface,
        contentColor = MaterialTheme.colorScheme.onSurface
    ) {
        tabs.forEachIndexed { index, tab ->
            Tab(
                selected = selectedTabIndex == index,
                onClick = { onTabSelected(index) },
                icon = {
                    Icon(
                        imageVector = tab.icon,
                        contentDescription = tab.title,
                        tint = if (selectedTabIndex == index) tab.color else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                },
                text = {
                    Text(
                        text = tab.title,
                        fontWeight = if (selectedTabIndex == index) FontWeight.Bold else FontWeight.Normal,
                        color = if (selectedTabIndex == index) tab.color else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            )
        }
    }
}

@Composable
fun ErrorCard(
    error: String,
    onDismiss: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                Icons.Default.Warning,
                contentDescription = "خطا",
                tint = MaterialTheme.colorScheme.error
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = error,
                modifier = Modifier.weight(1f),
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            IconButton(onClick = onDismiss) {
                Icon(
                    Icons.Default.Close,
                    contentDescription = "بستن",
                    tint = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
    }
}

// Tab Content Functions
@Composable
fun AllMessagesTab(
    viewModel: NotificationViewModel,
    uiState: NotificationUiState
) {
    if (uiState.isLoading) {
        LoadingIndicator()
    } else {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(uiState.notifications) { notification ->
                EnhancedMessageCard(
                    notification = notification,
                    onMarkAsRead = { viewModel.markAsRead(notification.id) },
                    onDelete = { viewModel.deleteNotification(notification) }
                )
            }
        }
    }
}

@Composable
fun ApprovalRequestsTab(
    viewModel: NotificationViewModel,
    uiState: NotificationUiState
) {
    val approvalRequests = viewModel.getNotificationsByType(NotificationType.CUSTOMER_APPROVAL_REQUEST)

    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(approvalRequests) { notification ->
            EnhancedApprovalRequestCard(
                notification = notification,
                onApprove = {
                    // TODO: Implement approval logic
                    viewModel.markAsRead(notification.id)
                },
                onReject = {
                    // TODO: Implement rejection logic
                    viewModel.markAsRead(notification.id)
                }
            )
        }
    }
}

@Composable
fun SystemMessagesTab(
    viewModel: NotificationViewModel,
    uiState: NotificationUiState
) {
    val systemMessages = viewModel.getNotificationsByType(NotificationType.SYSTEM_MESSAGE)

    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(systemMessages) { notification ->
            EnhancedMessageCard(
                notification = notification,
                onMarkAsRead = { viewModel.markAsRead(notification.id) },
                onDelete = { viewModel.deleteNotification(notification) }
            )
        }
    }
}

@Composable
fun CashierMessagesTab(
    viewModel: NotificationViewModel,
    uiState: NotificationUiState
) {
    AllMessagesTab(viewModel, uiState)
}

@Composable
fun CashierApprovalRequestsTab(
    viewModel: NotificationViewModel,
    uiState: NotificationUiState
) {
    ApprovalRequestsTab(viewModel, uiState)
}

@Composable
fun NotificationsTab(
    viewModel: NotificationViewModel,
    uiState: NotificationUiState
) {
    AllMessagesTab(viewModel, uiState)
}

@Composable
fun CustomerMessagesTab(
    viewModel: NotificationViewModel,
    uiState: NotificationUiState
) {
    AllMessagesTab(viewModel, uiState)
}

@Composable
fun CustomerApprovalStatusTab(
    viewModel: NotificationViewModel,
    uiState: NotificationUiState
) {
    val approvalMessages = uiState.notifications.filter {
        it.type == NotificationType.CUSTOMER_APPROVED || it.type == NotificationType.CUSTOMER_REJECTED
    }

    if (approvalMessages.isNotEmpty()) {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(approvalMessages) { notification ->
                ApprovalStatusCard(notification = notification)
            }
        }
    } else {
        // نمایش وضعیت پیش‌فرض
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "در انتظار",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(48.dp)
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "در انتظار تایید",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Text(
                    text = "درخواست شما در حال بررسی است",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                )
            }
        }
    }
}

@Composable
fun CustomerNotificationsTab(
    viewModel: NotificationViewModel,
    uiState: NotificationUiState
) {
    AllMessagesTab(viewModel, uiState)
}

// Enhanced UI Components
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EnhancedMessageCard(
    notification: Notification,
    onMarkAsRead: () -> Unit,
    onDelete: () -> Unit
) {
    // انیمیشن‌های زیبا
    val infiniteTransition = rememberInfiniteTransition(label = "message_card_animation")

    val shimmerAlpha by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "shimmer"
    )

    Card(
        onClick = { if (!notification.isRead) onMarkAsRead() },
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (notification.isRead)
                MaterialTheme.colorScheme.surface
            else
                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.Top
        ) {
            // آیکون جذاب با انیمیشن
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        color = getNotificationColor(notification.type).copy(alpha = 0.2f),
                        shape = RoundedCornerShape(12.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = getNotificationIcon(notification.type),
                    contentDescription = notification.type.displayName,
                    tint = getNotificationColor(notification.type).copy(alpha = shimmerAlpha),
                    modifier = Modifier.size(24.dp)
                )
            }

            Spacer(modifier = Modifier.width(12.dp))

            Column(modifier = Modifier.weight(1f)) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.Top
                ) {
                    Text(
                        text = notification.title,
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = if (notification.isRead) FontWeight.Normal else FontWeight.Bold,
                        modifier = Modifier.weight(1f)
                    )

                    Text(
                        text = formatTime(notification.createdAt),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }

                Spacer(modifier = Modifier.height(4.dp))

                // نام کاربری و نقش
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Person,
                        contentDescription = "کاربر",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = getUserDisplayName(notification.userId),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Badge(
                        containerColor = getNotificationColor(notification.type).copy(alpha = 0.2f)
                    ) {
                        Text(
                            text = notification.type.displayName,
                            style = MaterialTheme.typography.labelSmall,
                            color = getNotificationColor(notification.type)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = notification.message,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
                )
            }

            // نشانگر خوانده نشده با انیمیشن
            if (!notification.isRead) {
                Spacer(modifier = Modifier.width(8.dp))
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .background(
                            MaterialTheme.colorScheme.primary.copy(alpha = shimmerAlpha),
                            shape = RoundedCornerShape(6.dp)
                        )
                )
            }
        }
    }
}

@Composable
fun EnhancedApprovalRequestCard(
    notification: Notification,
    onApprove: () -> Unit,
    onReject: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            Icons.Default.Person,
                            contentDescription = "درخواست تایید",
                            tint = Color(0xFFFF9800),
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = notification.title,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = notification.message,
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            Icons.Default.Person,
                            contentDescription = "کاربر",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = getUserDisplayName(notification.userId),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }

                Text(
                    text = formatTime(notification.createdAt),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = onReject,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(
                        Icons.Default.Close,
                        contentDescription = "رد",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("رد")
                }

                Button(
                    onClick = onApprove,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF4CAF50)
                    )
                ) {
                    Icon(
                        Icons.Default.Check,
                        contentDescription = "تایید",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("تایید")
                }
            }
        }
    }
}

@Composable
fun ApprovalStatusCard(notification: Notification) {
    val isApproved = notification.type == NotificationType.CUSTOMER_APPROVED

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isApproved)
                Color(0xFF4CAF50).copy(alpha = 0.1f)
            else
                MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = if (isApproved) Icons.Default.CheckCircle else Icons.Default.Close,
                contentDescription = if (isApproved) "تایید شده" else "رد شده",
                tint = if (isApproved) Color(0xFF4CAF50) else MaterialTheme.colorScheme.error,
                modifier = Modifier.size(48.dp)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = notification.title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Text(
                text = notification.message,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = formatTime(notification.createdAt),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        }
    }
}

@Composable
fun LoadingIndicator() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator(
                color = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "در حال بارگذاری پیام‌ها...",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

// Helper Functions
fun getNotificationIcon(type: NotificationType): ImageVector {
    return when (type) {
        NotificationType.CUSTOMER_APPROVAL_REQUEST -> Icons.Default.Person
        NotificationType.CUSTOMER_APPROVED -> Icons.Default.CheckCircle
        NotificationType.CUSTOMER_REJECTED -> Icons.Default.Close
        NotificationType.SYSTEM_MESSAGE -> Icons.Default.Settings
    }
}

fun getNotificationColor(type: NotificationType): Color {
    return when (type) {
        NotificationType.CUSTOMER_APPROVAL_REQUEST -> Color(0xFFFF9800)
        NotificationType.CUSTOMER_APPROVED -> Color(0xFF4CAF50)
        NotificationType.CUSTOMER_REJECTED -> Color(0xFFF44336)
        NotificationType.SYSTEM_MESSAGE -> Color(0xFF9C27B0)
    }
}

fun getUserDisplayName(userId: String): String {
    // TODO: Get actual user name from repository
    return when {
        userId.contains("admin") -> "مدیر کل • مدیریت"
        userId.contains("cashier") -> "فروشنده • کاشیر"
        userId.contains("customer") -> "مشتری • خریدار"
        else -> "کاربر سیستم"
    }
}

fun formatTime(timestamp: Long): String {
    val now = System.currentTimeMillis()
    val diff = now - timestamp

    return when {
        diff < 60_000 -> "همین الان"
        diff < 3600_000 -> "${diff / 60_000} دقیقه پیش"
        diff < 86400_000 -> "${diff / 3600_000} ساعت پیش"
        diff < 604800_000 -> "${diff / 86400_000} روز پیش"
        else -> {
            val sdf = SimpleDateFormat("yyyy/MM/dd", Locale("fa"))
            sdf.format(Date(timestamp))
        }
    }
}
