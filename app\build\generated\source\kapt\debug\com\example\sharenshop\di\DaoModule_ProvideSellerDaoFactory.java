// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.local.dao.SellerDao;
import com.example.sharenshop.data.local.database.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaoModule_ProvideSellerDaoFactory implements Factory<SellerDao> {
  private final Provider<AppDatabase> appDatabaseProvider;

  public DaoModule_ProvideSellerDaoFactory(Provider<AppDatabase> appDatabaseProvider) {
    this.appDatabaseProvider = appDatabaseProvider;
  }

  @Override
  public SellerDao get() {
    return provideSellerDao(appDatabaseProvider.get());
  }

  public static DaoModule_ProvideSellerDaoFactory create(
      Provider<AppDatabase> appDatabaseProvider) {
    return new DaoModule_ProvideSellerDaoFactory(appDatabaseProvider);
  }

  public static SellerDao provideSellerDao(AppDatabase appDatabase) {
    return Preconditions.checkNotNullFromProvides(DaoModule.INSTANCE.provideSellerDao(appDatabase));
  }
}
