package com.example.sharenshop.domain.use_case;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0011\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0006H\u0086\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/example/sharenshop/domain/use_case/DecryptData;", "", "repository", "Lcom/example/sharenshop/domain/repository/SecurityRepository;", "(Lcom/example/sharenshop/domain/repository/SecurityRepository;)V", "invoke", "", "encryptedData", "app_debug"})
public final class DecryptData {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.repository.SecurityRepository repository = null;
    
    public DecryptData(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.SecurityRepository repository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String invoke(@org.jetbrains.annotations.NotNull()
    java.lang.String encryptedData) {
        return null;
    }
}