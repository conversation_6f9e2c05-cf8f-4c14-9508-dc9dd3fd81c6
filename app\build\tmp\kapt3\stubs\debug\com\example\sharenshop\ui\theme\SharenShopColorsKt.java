package com.example.sharenshop.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0013\"\u0015\u0010\u0000\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\u0003\u0010\u0004\"\u0015\u0010\u0005\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\u0006\u0010\u0004\"\u0015\u0010\u0007\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\b\u0010\u0004\"\u0015\u0010\t\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\n\u0010\u0004\"\u0015\u0010\u000b\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\f\u0010\u0004\"\u0015\u0010\r\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\u000e\u0010\u0004\"\u0015\u0010\u000f\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\u0010\u0010\u0004\"\u0015\u0010\u0011\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\u0012\u0010\u0004\"\u0015\u0010\u0013\u001a\u00020\u0001*\u00020\u00028G\u00a2\u0006\u0006\u001a\u0004\b\u0014\u0010\u0004\u00a8\u0006\u0015"}, d2 = {"cashierColor", "Landroidx/compose/ui/graphics/Color;", "Landroidx/compose/material3/ColorScheme;", "getCashierColor", "(Landroidx/compose/material3/ColorScheme;)J", "customerColor", "getCustomerColor", "info", "getInfo", "lossColor", "getLossColor", "neutralColor", "getNeutralColor", "profitColor", "getProfitColor", "success", "getSuccess", "superAdminColor", "getSuperAdminColor", "warning", "getWarning", "app_debug"})
public final class SharenShopColorsKt {
    
    @androidx.compose.runtime.Composable()
    public static final long getSuccess(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$success) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getWarning(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$warning) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getInfo(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$info) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getSuperAdminColor(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$superAdminColor) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getCashierColor(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$cashierColor) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getCustomerColor(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$customerColor) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getProfitColor(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$profitColor) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getLossColor(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$lossColor) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getNeutralColor(@org.jetbrains.annotations.NotNull()
    androidx.compose.material3.ColorScheme $this$neutralColor) {
        return 0L;
    }
}