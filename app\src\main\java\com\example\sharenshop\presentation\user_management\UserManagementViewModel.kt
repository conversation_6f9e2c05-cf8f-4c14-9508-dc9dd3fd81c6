package com.example.sharenshop.presentation.user_management

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharenshop.data.model.User
import com.example.sharenshop.domain.use_case.UserUseCases
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class UserManagementViewModel @Inject constructor(
    private val userUseCases: UserUseCases
) : ViewModel() {

    private val _users = MutableStateFlow<List<User>>(emptyList())
    val users: StateFlow<List<User>> = _users.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    init {
        loadUsers()
    }

    fun loadUsers() {
        _isLoading.value = true
        userUseCases.getAllUsers()
            .onEach { users ->
                _users.value = users
            }
            .catch { e ->
                _error.value = e.localizedMessage ?: "Unknown error loading users"
            }
            .onEach { _isLoading.value = false }
            .launchIn(viewModelScope)
    }

    fun insertUser(user: User) {
        viewModelScope.launch {
            try {
                userUseCases.insertUser(user)
                loadUsers() // Refresh the list after insertion
            } catch (e: Exception) {
                _error.value = e.localizedMessage ?: "Error inserting user"
            }
        }
    }

    fun updateUser(user: User) {
        viewModelScope.launch {
            try {
                userUseCases.updateUser(user)
                loadUsers() // Refresh the list after update
            } catch (e: Exception) {
                _error.value = e.localizedMessage ?: "Error updating user"
            }
        }
    }

    fun deleteUser(user: User) {
        viewModelScope.launch {
            try {
                userUseCases.deleteUser(user.id)
                loadUsers() // Refresh the list after deletion
            } catch (e: Exception) {
                _error.value = e.localizedMessage ?: "Error deleting user"
            }
        }
    }

    fun getUsersByType(userType: String) {
        viewModelScope.launch {
            _isLoading.value = true
            userUseCases.getUsersByType(userType)
                .onEach { users ->
                    _users.value = users
                }
                .catch { e ->
                    _error.value = e.localizedMessage ?: "Unknown error loading users by type"
                }
                .onEach { _isLoading.value = false }
                .launchIn(viewModelScope)
        }
    }
} 