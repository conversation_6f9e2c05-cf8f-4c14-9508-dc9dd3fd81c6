// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.security;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FileEncryptionService_Factory implements Factory<FileEncryptionService> {
  @Override
  public FileEncryptionService get() {
    return newInstance();
  }

  public static FileEncryptionService_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static FileEncryptionService newInstance() {
    return new FileEncryptionService();
  }

  private static final class InstanceHolder {
    private static final FileEncryptionService_Factory INSTANCE = new FileEncryptionService_Factory();
  }
}
