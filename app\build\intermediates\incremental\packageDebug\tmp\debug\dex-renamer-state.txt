#Fri Jun 06 21:32:29 IRST 2025
path.4=13/classes.dex
path.3=12/classes.dex
path.2=10/classes.dex
path.1=0/classes.dex
path.8=3/classes.dex
path.7=1/classes.dex
path.6=15/classes.dex
path.5=14/classes.dex
path.0=classes.dex
base.4=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\13\\classes.dex
base.3=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\12\\classes.dex
base.2=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\10\\classes.dex
base.1=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.0=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
path.9=4/classes.dex
base.9=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\4\\classes.dex
base.8=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\3\\classes.dex
base.7=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\1\\classes.dex
base.6=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\15\\classes.dex
base.5=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\14\\classes.dex
renamed.9=classes10.dex
renamed.16=classes17.dex
renamed.8=classes9.dex
renamed.15=classes16.dex
renamed.14=classes15.dex
renamed.13=classes14.dex
renamed.12=classes13.dex
renamed.11=classes12.dex
renamed.10=classes11.dex
base.16=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes3.dex
base.15=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
base.14=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\desugar_lib_dex\\debug\\l8DexDesugarLibDebug\\classes1000.dex
renamed.3=classes4.dex
path.12=7/classes.dex
renamed.2=classes3.dex
path.13=9/classes.dex
renamed.1=classes2.dex
path.10=5/classes.dex
renamed.0=classes.dex
path.11=6/classes.dex
renamed.7=classes8.dex
path.16=classes3.dex
renamed.6=classes7.dex
renamed.5=classes6.dex
path.14=classes1000.dex
renamed.4=classes5.dex
path.15=classes2.dex
base.13=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\9\\classes.dex
base.12=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\7\\classes.dex
base.11=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\6\\classes.dex
base.10=B\:\\SHARENAPP\\sharenshop\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\5\\classes.dex
