// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.domain.repository.SettingsRepository;
import com.example.sharenshop.domain.use_case.SettingsUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideSettingsUseCasesFactory implements Factory<SettingsUseCases> {
  private final Provider<SettingsRepository> repositoryProvider;

  public UseCaseModule_ProvideSettingsUseCasesFactory(
      Provider<SettingsRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public SettingsUseCases get() {
    return provideSettingsUseCases(repositoryProvider.get());
  }

  public static UseCaseModule_ProvideSettingsUseCasesFactory create(
      Provider<SettingsRepository> repositoryProvider) {
    return new UseCaseModule_ProvideSettingsUseCasesFactory(repositoryProvider);
  }

  public static SettingsUseCases provideSettingsUseCases(SettingsRepository repository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideSettingsUseCases(repository));
  }
}
