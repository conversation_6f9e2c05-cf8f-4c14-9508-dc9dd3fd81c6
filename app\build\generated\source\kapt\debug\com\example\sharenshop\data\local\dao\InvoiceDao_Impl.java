package com.example.sharenshop.data.local.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.sharenshop.data.local.converter.BigDecimalConverter;
import com.example.sharenshop.data.model.Invoice;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class InvoiceDao_Impl implements InvoiceDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Invoice> __insertionAdapterOfInvoice;

  private final BigDecimalConverter __bigDecimalConverter = new BigDecimalConverter();

  private final EntityDeletionOrUpdateAdapter<Invoice> __updateAdapterOfInvoice;

  private final SharedSQLiteStatement __preparedStmtOfDeleteInvoiceById;

  public InvoiceDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfInvoice = new EntityInsertionAdapter<Invoice>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `invoices` (`id`,`customerId`,`userId`,`invoiceDate`,`totalAmount`,`discountAmount`,`finalAmount`,`paymentStatus`,`paymentType`,`paidAmount`,`remainingAmount`,`notes`,`createdAt`,`updatedAt`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Invoice entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getCustomerId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCustomerId());
        }
        if (entity.getUserId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getUserId());
        }
        statement.bindLong(4, entity.getInvoiceDate());
        final String _tmp = __bigDecimalConverter.fromBigDecimal(entity.getTotalAmount());
        if (_tmp == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp);
        }
        final String _tmp_1 = __bigDecimalConverter.fromBigDecimal(entity.getDiscountAmount());
        if (_tmp_1 == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, _tmp_1);
        }
        final String _tmp_2 = __bigDecimalConverter.fromBigDecimal(entity.getFinalAmount());
        if (_tmp_2 == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp_2);
        }
        if (entity.getPaymentStatus() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getPaymentStatus());
        }
        if (entity.getPaymentType() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getPaymentType());
        }
        final String _tmp_3 = __bigDecimalConverter.fromBigDecimal(entity.getPaidAmount());
        if (_tmp_3 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_3);
        }
        final String _tmp_4 = __bigDecimalConverter.fromBigDecimal(entity.getRemainingAmount());
        if (_tmp_4 == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, _tmp_4);
        }
        if (entity.getNotes() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getNotes());
        }
        statement.bindLong(13, entity.getCreatedAt());
        if (entity.getUpdatedAt() == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, entity.getUpdatedAt());
        }
      }
    };
    this.__updateAdapterOfInvoice = new EntityDeletionOrUpdateAdapter<Invoice>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `invoices` SET `id` = ?,`customerId` = ?,`userId` = ?,`invoiceDate` = ?,`totalAmount` = ?,`discountAmount` = ?,`finalAmount` = ?,`paymentStatus` = ?,`paymentType` = ?,`paidAmount` = ?,`remainingAmount` = ?,`notes` = ?,`createdAt` = ?,`updatedAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Invoice entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getCustomerId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCustomerId());
        }
        if (entity.getUserId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getUserId());
        }
        statement.bindLong(4, entity.getInvoiceDate());
        final String _tmp = __bigDecimalConverter.fromBigDecimal(entity.getTotalAmount());
        if (_tmp == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp);
        }
        final String _tmp_1 = __bigDecimalConverter.fromBigDecimal(entity.getDiscountAmount());
        if (_tmp_1 == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, _tmp_1);
        }
        final String _tmp_2 = __bigDecimalConverter.fromBigDecimal(entity.getFinalAmount());
        if (_tmp_2 == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp_2);
        }
        if (entity.getPaymentStatus() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getPaymentStatus());
        }
        if (entity.getPaymentType() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getPaymentType());
        }
        final String _tmp_3 = __bigDecimalConverter.fromBigDecimal(entity.getPaidAmount());
        if (_tmp_3 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_3);
        }
        final String _tmp_4 = __bigDecimalConverter.fromBigDecimal(entity.getRemainingAmount());
        if (_tmp_4 == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, _tmp_4);
        }
        if (entity.getNotes() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getNotes());
        }
        statement.bindLong(13, entity.getCreatedAt());
        if (entity.getUpdatedAt() == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, entity.getUpdatedAt());
        }
        if (entity.getId() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteInvoiceById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM invoices WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertInvoice(final Invoice invoice, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfInvoice.insert(invoice);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateInvoice(final Invoice invoice, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfInvoice.handle(invoice);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteInvoiceById(final String invoiceId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteInvoiceById.acquire();
        int _argIndex = 1;
        if (invoiceId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, invoiceId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteInvoiceById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<Invoice> getInvoiceById(final String invoiceId) {
    final String _sql = "SELECT * FROM invoices WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (invoiceId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, invoiceId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"invoices"}, new Callable<Invoice>() {
      @Override
      @Nullable
      public Invoice call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCustomerId = CursorUtil.getColumnIndexOrThrow(_cursor, "customerId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfInvoiceDate = CursorUtil.getColumnIndexOrThrow(_cursor, "invoiceDate");
          final int _cursorIndexOfTotalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "totalAmount");
          final int _cursorIndexOfDiscountAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "discountAmount");
          final int _cursorIndexOfFinalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "finalAmount");
          final int _cursorIndexOfPaymentStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentStatus");
          final int _cursorIndexOfPaymentType = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentType");
          final int _cursorIndexOfPaidAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "paidAmount");
          final int _cursorIndexOfRemainingAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingAmount");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final Invoice _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpCustomerId;
            if (_cursor.isNull(_cursorIndexOfCustomerId)) {
              _tmpCustomerId = null;
            } else {
              _tmpCustomerId = _cursor.getString(_cursorIndexOfCustomerId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final long _tmpInvoiceDate;
            _tmpInvoiceDate = _cursor.getLong(_cursorIndexOfInvoiceDate);
            final BigDecimal _tmpTotalAmount;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfTotalAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfTotalAmount);
            }
            _tmpTotalAmount = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpDiscountAmount;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDiscountAmount)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfDiscountAmount);
            }
            _tmpDiscountAmount = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final BigDecimal _tmpFinalAmount;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfFinalAmount)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfFinalAmount);
            }
            _tmpFinalAmount = __bigDecimalConverter.toBigDecimal(_tmp_2);
            final String _tmpPaymentStatus;
            if (_cursor.isNull(_cursorIndexOfPaymentStatus)) {
              _tmpPaymentStatus = null;
            } else {
              _tmpPaymentStatus = _cursor.getString(_cursorIndexOfPaymentStatus);
            }
            final String _tmpPaymentType;
            if (_cursor.isNull(_cursorIndexOfPaymentType)) {
              _tmpPaymentType = null;
            } else {
              _tmpPaymentType = _cursor.getString(_cursorIndexOfPaymentType);
            }
            final BigDecimal _tmpPaidAmount;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfPaidAmount)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfPaidAmount);
            }
            _tmpPaidAmount = __bigDecimalConverter.toBigDecimal(_tmp_3);
            final BigDecimal _tmpRemainingAmount;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfRemainingAmount)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfRemainingAmount);
            }
            _tmpRemainingAmount = __bigDecimalConverter.toBigDecimal(_tmp_4);
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpUpdatedAt;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmpUpdatedAt = null;
            } else {
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _result = new Invoice(_tmpId,_tmpCustomerId,_tmpUserId,_tmpInvoiceDate,_tmpTotalAmount,_tmpDiscountAmount,_tmpFinalAmount,_tmpPaymentStatus,_tmpPaymentType,_tmpPaidAmount,_tmpRemainingAmount,_tmpNotes,_tmpCreatedAt,_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Invoice>> getAllInvoices() {
    final String _sql = "SELECT * FROM invoices";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"invoices"}, new Callable<List<Invoice>>() {
      @Override
      @NonNull
      public List<Invoice> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCustomerId = CursorUtil.getColumnIndexOrThrow(_cursor, "customerId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfInvoiceDate = CursorUtil.getColumnIndexOrThrow(_cursor, "invoiceDate");
          final int _cursorIndexOfTotalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "totalAmount");
          final int _cursorIndexOfDiscountAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "discountAmount");
          final int _cursorIndexOfFinalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "finalAmount");
          final int _cursorIndexOfPaymentStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentStatus");
          final int _cursorIndexOfPaymentType = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentType");
          final int _cursorIndexOfPaidAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "paidAmount");
          final int _cursorIndexOfRemainingAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingAmount");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<Invoice> _result = new ArrayList<Invoice>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Invoice _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpCustomerId;
            if (_cursor.isNull(_cursorIndexOfCustomerId)) {
              _tmpCustomerId = null;
            } else {
              _tmpCustomerId = _cursor.getString(_cursorIndexOfCustomerId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final long _tmpInvoiceDate;
            _tmpInvoiceDate = _cursor.getLong(_cursorIndexOfInvoiceDate);
            final BigDecimal _tmpTotalAmount;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfTotalAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfTotalAmount);
            }
            _tmpTotalAmount = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpDiscountAmount;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDiscountAmount)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfDiscountAmount);
            }
            _tmpDiscountAmount = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final BigDecimal _tmpFinalAmount;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfFinalAmount)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfFinalAmount);
            }
            _tmpFinalAmount = __bigDecimalConverter.toBigDecimal(_tmp_2);
            final String _tmpPaymentStatus;
            if (_cursor.isNull(_cursorIndexOfPaymentStatus)) {
              _tmpPaymentStatus = null;
            } else {
              _tmpPaymentStatus = _cursor.getString(_cursorIndexOfPaymentStatus);
            }
            final String _tmpPaymentType;
            if (_cursor.isNull(_cursorIndexOfPaymentType)) {
              _tmpPaymentType = null;
            } else {
              _tmpPaymentType = _cursor.getString(_cursorIndexOfPaymentType);
            }
            final BigDecimal _tmpPaidAmount;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfPaidAmount)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfPaidAmount);
            }
            _tmpPaidAmount = __bigDecimalConverter.toBigDecimal(_tmp_3);
            final BigDecimal _tmpRemainingAmount;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfRemainingAmount)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfRemainingAmount);
            }
            _tmpRemainingAmount = __bigDecimalConverter.toBigDecimal(_tmp_4);
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpUpdatedAt;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmpUpdatedAt = null;
            } else {
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _item = new Invoice(_tmpId,_tmpCustomerId,_tmpUserId,_tmpInvoiceDate,_tmpTotalAmount,_tmpDiscountAmount,_tmpFinalAmount,_tmpPaymentStatus,_tmpPaymentType,_tmpPaidAmount,_tmpRemainingAmount,_tmpNotes,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Invoice>> getInvoicesByCustomerId(final String customerId) {
    final String _sql = "SELECT * FROM invoices WHERE customerId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (customerId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, customerId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"invoices"}, new Callable<List<Invoice>>() {
      @Override
      @NonNull
      public List<Invoice> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCustomerId = CursorUtil.getColumnIndexOrThrow(_cursor, "customerId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfInvoiceDate = CursorUtil.getColumnIndexOrThrow(_cursor, "invoiceDate");
          final int _cursorIndexOfTotalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "totalAmount");
          final int _cursorIndexOfDiscountAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "discountAmount");
          final int _cursorIndexOfFinalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "finalAmount");
          final int _cursorIndexOfPaymentStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentStatus");
          final int _cursorIndexOfPaymentType = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentType");
          final int _cursorIndexOfPaidAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "paidAmount");
          final int _cursorIndexOfRemainingAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingAmount");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<Invoice> _result = new ArrayList<Invoice>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Invoice _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpCustomerId;
            if (_cursor.isNull(_cursorIndexOfCustomerId)) {
              _tmpCustomerId = null;
            } else {
              _tmpCustomerId = _cursor.getString(_cursorIndexOfCustomerId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final long _tmpInvoiceDate;
            _tmpInvoiceDate = _cursor.getLong(_cursorIndexOfInvoiceDate);
            final BigDecimal _tmpTotalAmount;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfTotalAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfTotalAmount);
            }
            _tmpTotalAmount = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpDiscountAmount;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDiscountAmount)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfDiscountAmount);
            }
            _tmpDiscountAmount = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final BigDecimal _tmpFinalAmount;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfFinalAmount)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfFinalAmount);
            }
            _tmpFinalAmount = __bigDecimalConverter.toBigDecimal(_tmp_2);
            final String _tmpPaymentStatus;
            if (_cursor.isNull(_cursorIndexOfPaymentStatus)) {
              _tmpPaymentStatus = null;
            } else {
              _tmpPaymentStatus = _cursor.getString(_cursorIndexOfPaymentStatus);
            }
            final String _tmpPaymentType;
            if (_cursor.isNull(_cursorIndexOfPaymentType)) {
              _tmpPaymentType = null;
            } else {
              _tmpPaymentType = _cursor.getString(_cursorIndexOfPaymentType);
            }
            final BigDecimal _tmpPaidAmount;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfPaidAmount)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfPaidAmount);
            }
            _tmpPaidAmount = __bigDecimalConverter.toBigDecimal(_tmp_3);
            final BigDecimal _tmpRemainingAmount;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfRemainingAmount)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfRemainingAmount);
            }
            _tmpRemainingAmount = __bigDecimalConverter.toBigDecimal(_tmp_4);
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpUpdatedAt;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmpUpdatedAt = null;
            } else {
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _item = new Invoice(_tmpId,_tmpCustomerId,_tmpUserId,_tmpInvoiceDate,_tmpTotalAmount,_tmpDiscountAmount,_tmpFinalAmount,_tmpPaymentStatus,_tmpPaymentType,_tmpPaidAmount,_tmpRemainingAmount,_tmpNotes,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Invoice>> getInvoicesByUserId(final String userId) {
    final String _sql = "SELECT * FROM invoices WHERE userId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"invoices"}, new Callable<List<Invoice>>() {
      @Override
      @NonNull
      public List<Invoice> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCustomerId = CursorUtil.getColumnIndexOrThrow(_cursor, "customerId");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfInvoiceDate = CursorUtil.getColumnIndexOrThrow(_cursor, "invoiceDate");
          final int _cursorIndexOfTotalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "totalAmount");
          final int _cursorIndexOfDiscountAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "discountAmount");
          final int _cursorIndexOfFinalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "finalAmount");
          final int _cursorIndexOfPaymentStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentStatus");
          final int _cursorIndexOfPaymentType = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentType");
          final int _cursorIndexOfPaidAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "paidAmount");
          final int _cursorIndexOfRemainingAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingAmount");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<Invoice> _result = new ArrayList<Invoice>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Invoice _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpCustomerId;
            if (_cursor.isNull(_cursorIndexOfCustomerId)) {
              _tmpCustomerId = null;
            } else {
              _tmpCustomerId = _cursor.getString(_cursorIndexOfCustomerId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final long _tmpInvoiceDate;
            _tmpInvoiceDate = _cursor.getLong(_cursorIndexOfInvoiceDate);
            final BigDecimal _tmpTotalAmount;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfTotalAmount)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfTotalAmount);
            }
            _tmpTotalAmount = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpDiscountAmount;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDiscountAmount)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfDiscountAmount);
            }
            _tmpDiscountAmount = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final BigDecimal _tmpFinalAmount;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfFinalAmount)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfFinalAmount);
            }
            _tmpFinalAmount = __bigDecimalConverter.toBigDecimal(_tmp_2);
            final String _tmpPaymentStatus;
            if (_cursor.isNull(_cursorIndexOfPaymentStatus)) {
              _tmpPaymentStatus = null;
            } else {
              _tmpPaymentStatus = _cursor.getString(_cursorIndexOfPaymentStatus);
            }
            final String _tmpPaymentType;
            if (_cursor.isNull(_cursorIndexOfPaymentType)) {
              _tmpPaymentType = null;
            } else {
              _tmpPaymentType = _cursor.getString(_cursorIndexOfPaymentType);
            }
            final BigDecimal _tmpPaidAmount;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfPaidAmount)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfPaidAmount);
            }
            _tmpPaidAmount = __bigDecimalConverter.toBigDecimal(_tmp_3);
            final BigDecimal _tmpRemainingAmount;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfRemainingAmount)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfRemainingAmount);
            }
            _tmpRemainingAmount = __bigDecimalConverter.toBigDecimal(_tmp_4);
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpUpdatedAt;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmpUpdatedAt = null;
            } else {
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _item = new Invoice(_tmpId,_tmpCustomerId,_tmpUserId,_tmpInvoiceDate,_tmpTotalAmount,_tmpDiscountAmount,_tmpFinalAmount,_tmpPaymentStatus,_tmpPaymentType,_tmpPaidAmount,_tmpRemainingAmount,_tmpNotes,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
