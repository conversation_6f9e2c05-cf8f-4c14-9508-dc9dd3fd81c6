package com.example.sharenshop.domain.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\n\bf\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a6@\u00a2\u0006\u0002\u0010\u0006J\u0014\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\bH&J\u0018\u0010\u000b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\n0\b2\u0006\u0010\u0004\u001a\u00020\u0005H&J\u001c\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\u0006\u0010\r\u001a\u00020\u0005H&J\u001c\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\u0006\u0010\u000f\u001a\u00020\u0005H&J\u0016\u0010\u0010\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\u0012J\u0016\u0010\u0013\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\u0012\u00a8\u0006\u0014"}, d2 = {"Lcom/example/sharenshop/domain/repository/InvoiceRepository;", "", "deleteInvoiceById", "", "invoiceId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllInvoices", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/example/sharenshop/data/model/Invoice;", "getInvoiceById", "getInvoicesByCustomerId", "customerId", "getInvoicesByUserId", "userId", "insertInvoice", "invoice", "(Lcom/example/sharenshop/data/model/Invoice;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateInvoice", "app_debug"})
public abstract interface InvoiceRepository {
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.example.sharenshop.data.model.Invoice> getInvoiceById(@org.jetbrains.annotations.NotNull()
    java.lang.String invoiceId);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertInvoice(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Invoice invoice, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateInvoice(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Invoice invoice, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteInvoiceById(@org.jetbrains.annotations.NotNull()
    java.lang.String invoiceId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharenshop.data.model.Invoice>> getAllInvoices();
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharenshop.data.model.Invoice>> getInvoicesByCustomerId(@org.jetbrains.annotations.NotNull()
    java.lang.String customerId);
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharenshop.data.model.Invoice>> getInvoicesByUserId(@org.jetbrains.annotations.NotNull()
    java.lang.String userId);
}