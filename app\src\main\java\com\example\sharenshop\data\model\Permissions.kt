package com.example.sharenshop.data.model

/**
 * System permissions for different user roles
 */
enum class Permission(val value: String, val description: String) {
    // User Management
    VIEW_USERS("view_users", "مشاهده کاربران"),
    CREATE_USERS("create_users", "ایجاد کاربران"),
    EDIT_USERS("edit_users", "ویرایش کاربران"),
    DELETE_USERS("delete_users", "حذف کاربران"),
    
    // Product Management
    VIEW_PRODUCTS("view_products", "مشاهده محصولات"),
    CREATE_PRODUCTS("create_products", "ایجاد محصولات"),
    EDIT_PRODUCTS("edit_products", "ویرایش محصولات"),
    DELETE_PRODUCTS("delete_products", "حذف محصولات"),
    EDIT_PRODUCT_PRICES("edit_product_prices", "ویرایش قیمت محصولات"),
    EDIT_PRODUCT_STOCK("edit_product_stock", "ویرایش موجودی محصولات"),
    
    // Customer Management
    VIEW_CUSTOMERS("view_customers", "مشاهده مشتریان"),
    CREATE_CUSTOMERS("create_customers", "ایجاد مشتریان"),
    EDIT_CUSTOMERS("edit_customers", "ویرایش مشتریان"),
    DELETE_CUSTOMERS("delete_customers", "حذف مشتریان"),
    VIEW_CUSTOMER_BALANCE("view_customer_balance", "مشاهده موجودی مشتریان"),
    
    // Seller Management
    VIEW_SELLERS("view_sellers", "مشاهده فروشندگان"),
    CREATE_SELLERS("create_sellers", "ایجاد فروشندگان"),
    EDIT_SELLERS("edit_sellers", "ویرایش فروشندگان"),
    DELETE_SELLERS("delete_sellers", "حذف فروشندگان"),
    
    // Invoice Management
    VIEW_INVOICES("view_invoices", "مشاهده فاکتورها"),
    CREATE_INVOICES("create_invoices", "ایجاد فاکتورها"),
    EDIT_INVOICES("edit_invoices", "ویرایش فاکتورها"),
    DELETE_INVOICES("delete_invoices", "حذف فاکتورها"),
    VIEW_ALL_INVOICES("view_all_invoices", "مشاهده تمام فاکتورها"),
    VIEW_OWN_INVOICES("view_own_invoices", "مشاهده فاکتورهای خود"),
    
    // Statistics & Reports
    VIEW_STATISTICS("view_statistics", "مشاهده آمار"),
    VIEW_DETAILED_REPORTS("view_detailed_reports", "مشاهده گزارشات تفصیلی"),
    VIEW_FINANCIAL_REPORTS("view_financial_reports", "مشاهده گزارشات مالی"),
    VIEW_ALL_SELLERS_STATS("view_all_sellers_stats", "مشاهده آمار تمام فروشندگان"),
    VIEW_CASH_SALES("view_cash_sales", "مشاهده فروش نقدی"),
    VIEW_CREDIT_SALES("view_credit_sales", "مشاهده فروش نسیه"),
    VIEW_INVOICE_COUNT("view_invoice_count", "مشاهده تعداد فاکتورها"),
    VIEW_CUSTOMER_ADDITIONS("view_customer_additions", "مشاهده اضافه شدن مشتریان جدید"),

    // System Settings
    MANAGE_SETTINGS("manage_settings", "مدیریت تنظیمات"),
    MANAGE_SECURITY("manage_security", "مدیریت امنیت"),
    BACKUP_RESTORE("backup_restore", "پشتیبان‌گیری و بازیابی")
}

/**
 * Role-based permissions mapping
 */
object RolePermissions {

    val SUPER_ADMIN_PERMISSIONS = setOf(
        // Full access to everything - مدیر کل
        Permission.VIEW_USERS,
        Permission.CREATE_USERS,
        Permission.EDIT_USERS,
        Permission.DELETE_USERS,

        Permission.VIEW_PRODUCTS,
        Permission.CREATE_PRODUCTS,
        Permission.EDIT_PRODUCTS,
        Permission.DELETE_PRODUCTS,
        Permission.EDIT_PRODUCT_PRICES,
        Permission.EDIT_PRODUCT_STOCK,

        Permission.VIEW_CUSTOMERS,
        Permission.CREATE_CUSTOMERS,
        Permission.EDIT_CUSTOMERS,
        Permission.DELETE_CUSTOMERS,
        Permission.VIEW_CUSTOMER_BALANCE,

        Permission.VIEW_SELLERS,
        Permission.CREATE_SELLERS,
        Permission.EDIT_SELLERS,
        Permission.DELETE_SELLERS,

        Permission.VIEW_INVOICES,
        Permission.CREATE_INVOICES,
        Permission.EDIT_INVOICES,
        Permission.DELETE_INVOICES,
        Permission.VIEW_ALL_INVOICES,

        // آمار کامل تمام فروشندگان
        Permission.VIEW_STATISTICS,
        Permission.VIEW_DETAILED_REPORTS,
        Permission.VIEW_FINANCIAL_REPORTS,
        Permission.VIEW_ALL_SELLERS_STATS,
        Permission.VIEW_CASH_SALES,
        Permission.VIEW_CREDIT_SALES,
        Permission.VIEW_INVOICE_COUNT,
        Permission.VIEW_CUSTOMER_ADDITIONS,

        Permission.MANAGE_SETTINGS,
        Permission.MANAGE_SECURITY,
        Permission.BACKUP_RESTORE
    )
    
    val CASHIER_PERMISSIONS = setOf(
        // Limited product management - فروشنده
        Permission.VIEW_PRODUCTS,
        Permission.EDIT_PRODUCT_STOCK, // فقط موجودی

        // Customer management
        Permission.VIEW_CUSTOMERS,
        Permission.CREATE_CUSTOMERS,
        Permission.EDIT_CUSTOMERS,
        Permission.VIEW_CUSTOMER_BALANCE,

        // Invoice management
        Permission.VIEW_INVOICES,
        Permission.CREATE_INVOICES,
        Permission.EDIT_INVOICES,
        Permission.VIEW_OWN_INVOICES, // فقط فاکتورهای خود

        // آمار محدود - فقط مربوط به خودش
        Permission.VIEW_STATISTICS,
        Permission.VIEW_CASH_SALES, // فروش نقدی خود
        Permission.VIEW_CREDIT_SALES, // فروش نسیه خود
        Permission.VIEW_INVOICE_COUNT, // تعداد فاکتورهای خود
        Permission.VIEW_CUSTOMER_ADDITIONS // مشتریان اضافه شده توسط خود
    )
    
    val CUSTOMER_PERMISSIONS = setOf(
        // Very limited access
        Permission.VIEW_PRODUCTS,
        Permission.VIEW_OWN_INVOICES
    )
    
    /**
     * Get permissions for a specific user role
     */
    fun getPermissionsForRole(role: UserRole): Set<Permission> {
        return when (role) {
            UserRole.SUPER_ADMIN -> SUPER_ADMIN_PERMISSIONS
            UserRole.CASHIER -> CASHIER_PERMISSIONS
            UserRole.CUSTOMER -> CUSTOMER_PERMISSIONS
        }
    }
    
    /**
     * Check if a user role has a specific permission
     */
    fun hasPermission(role: UserRole, permission: Permission): Boolean {
        return getPermissionsForRole(role).contains(permission)
    }
}
