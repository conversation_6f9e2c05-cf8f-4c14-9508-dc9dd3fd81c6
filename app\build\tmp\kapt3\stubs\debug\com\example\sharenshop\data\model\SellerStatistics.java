package com.example.sharenshop.data.model;

/**
 * آمار کامل فروشنده برای مدیر کل
 */
@kotlinx.serialization.Serializable()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b;\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0087\b\u0018\u0000 g2\u00020\u0001:\u0002fgB\u00e5\u0001\b\u0011\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0001\u0010\b\u001a\u0004\u0018\u00010\t\u0012\n\b\u0001\u0010\n\u001a\u0004\u0018\u00010\t\u0012\n\b\u0001\u0010\u000b\u001a\u0004\u0018\u00010\t\u0012\u0006\u0010\f\u001a\u00020\u0003\u0012\u0006\u0010\r\u001a\u00020\u0003\u0012\u0006\u0010\u000e\u001a\u00020\u0003\u0012\u0006\u0010\u000f\u001a\u00020\u0003\u0012\u0006\u0010\u0010\u001a\u00020\u0003\u0012\u0006\u0010\u0011\u001a\u00020\u0003\u0012\u0006\u0010\u0012\u001a\u00020\u0003\u0012\n\b\u0001\u0010\u0013\u001a\u0004\u0018\u00010\t\u0012\n\b\u0001\u0010\u0014\u001a\u0004\u0018\u00010\t\u0012\n\b\u0001\u0010\u0015\u001a\u0004\u0018\u00010\t\u0012\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\t\u0012\b\u0010\u0017\u001a\u0004\u0018\u00010\u0005\u0012\u0006\u0010\u0018\u001a\u00020\u0019\u0012\u0006\u0010\u001a\u001a\u00020\u001b\u0012\u0006\u0010\u001c\u001a\u00020\u001b\u0012\b\u0010\u001d\u001a\u0004\u0018\u00010\u001e\u00a2\u0006\u0002\u0010\u001fB\u00af\u0001\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000b\u001a\u00020\t\u0012\u0006\u0010\f\u001a\u00020\u0003\u0012\u0006\u0010\r\u001a\u00020\u0003\u0012\u0006\u0010\u000e\u001a\u00020\u0003\u0012\u0006\u0010\u000f\u001a\u00020\u0003\u0012\u0006\u0010\u0010\u001a\u00020\u0003\u0012\u0006\u0010\u0011\u001a\u00020\u0003\u0012\u0006\u0010\u0012\u001a\u00020\u0003\u0012\u0006\u0010\u0013\u001a\u00020\t\u0012\u0006\u0010\u0014\u001a\u00020\t\u0012\u0006\u0010\u0015\u001a\u00020\t\u0012\u0006\u0010\u0016\u001a\u00020\t\u0012\b\u0010\u0017\u001a\u0004\u0018\u00010\u0005\u0012\u0006\u0010\u0018\u001a\u00020\u0019\u0012\u0006\u0010\u001a\u001a\u00020\u001b\u0012\u0006\u0010\u001c\u001a\u00020\u001b\u00a2\u0006\u0002\u0010 J\t\u0010C\u001a\u00020\u0005H\u00c6\u0003J\t\u0010D\u001a\u00020\u0003H\u00c6\u0003J\t\u0010E\u001a\u00020\u0003H\u00c6\u0003J\t\u0010F\u001a\u00020\u0003H\u00c6\u0003J\t\u0010G\u001a\u00020\u0003H\u00c6\u0003J\t\u0010H\u001a\u00020\tH\u00c6\u0003J\t\u0010I\u001a\u00020\tH\u00c6\u0003J\t\u0010J\u001a\u00020\tH\u00c6\u0003J\t\u0010K\u001a\u00020\tH\u00c6\u0003J\u000b\u0010L\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010M\u001a\u00020\u0019H\u00c6\u0003J\t\u0010N\u001a\u00020\u0005H\u00c6\u0003J\t\u0010O\u001a\u00020\u001bH\u00c6\u0003J\t\u0010P\u001a\u00020\u001bH\u00c6\u0003J\t\u0010Q\u001a\u00020\u0005H\u00c6\u0003J\t\u0010R\u001a\u00020\tH\u00c6\u0003J\t\u0010S\u001a\u00020\tH\u00c6\u0003J\t\u0010T\u001a\u00020\tH\u00c6\u0003J\t\u0010U\u001a\u00020\u0003H\u00c6\u0003J\t\u0010V\u001a\u00020\u0003H\u00c6\u0003J\t\u0010W\u001a\u00020\u0003H\u00c6\u0003J\u00dd\u0001\u0010X\u001a\u00020\u00002\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\t2\b\b\u0002\u0010\f\u001a\u00020\u00032\b\b\u0002\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u000e\u001a\u00020\u00032\b\b\u0002\u0010\u000f\u001a\u00020\u00032\b\b\u0002\u0010\u0010\u001a\u00020\u00032\b\b\u0002\u0010\u0011\u001a\u00020\u00032\b\b\u0002\u0010\u0012\u001a\u00020\u00032\b\b\u0002\u0010\u0013\u001a\u00020\t2\b\b\u0002\u0010\u0014\u001a\u00020\t2\b\b\u0002\u0010\u0015\u001a\u00020\t2\b\b\u0002\u0010\u0016\u001a\u00020\t2\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0018\u001a\u00020\u00192\b\b\u0002\u0010\u001a\u001a\u00020\u001b2\b\b\u0002\u0010\u001c\u001a\u00020\u001bH\u00c6\u0001J\u0013\u0010Y\u001a\u00020Z2\b\u0010[\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\\\u001a\u00020\u0003H\u00d6\u0001J\t\u0010]\u001a\u00020\u0005H\u00d6\u0001J&\u0010^\u001a\u00020_2\u0006\u0010`\u001a\u00020\u00002\u0006\u0010a\u001a\u00020b2\u0006\u0010c\u001a\u00020dH\u00c1\u0001\u00a2\u0006\u0002\beR\u0011\u0010\u0012\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\"R\u001c\u0010\u0016\u001a\u00020\t8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b#\u0010$\u001a\u0004\b%\u0010&R\u0013\u0010\u0017\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010(R\u001c\u0010\n\u001a\u00020\t8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b)\u0010$\u001a\u0004\b*\u0010&R\u0011\u0010\u001c\u001a\u00020\u001b\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010,R\u001c\u0010\u000b\u001a\u00020\t8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b-\u0010$\u001a\u0004\b.\u0010&R\u0011\u0010\u001a\u001a\u00020\u001b\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010,R\u0011\u0010\u0011\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010\"R\u0011\u0010\u000f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010\"R\u0011\u0010\r\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010\"R\u0011\u0010\u000e\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010\"R\u0011\u0010\u0018\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u00105R\u001c\u0010\u0014\u001a\u00020\t8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b6\u0010$\u001a\u0004\b7\u0010&R\u001c\u0010\u0013\u001a\u00020\t8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b8\u0010$\u001a\u0004\b9\u0010&R\u001c\u0010\u0015\u001a\u00020\t8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b:\u0010$\u001a\u0004\b;\u0010&R\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010(R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010(R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u0010(R\u0011\u0010\u0010\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010\"R\u0011\u0010\f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u0010\"R\u001c\u0010\b\u001a\u00020\t8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\bA\u0010$\u001a\u0004\bB\u0010&\u00a8\u0006h"}, d2 = {"Lcom/example/sharenshop/data/model/SellerStatistics;", "", "seen1", "", "sellerId", "", "sellerName", "sellerEmail", "totalSales", "Ljava/math/BigDecimal;", "cashSales", "creditSales", "totalInvoices", "paidInvoices", "pendingInvoices", "overdueInvoices", "totalCustomers", "newCustomersThisMonth", "activeCustomers", "salesThisMonth", "salesLastMonth", "salesThisYear", "averageInvoiceValue", "bestSellingDay", "performanceScore", "", "lastActivityDate", "", "createdAt", "serializationConstructorMarker", "Lkotlinx/serialization/internal/SerializationConstructorMarker;", "(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;IIIIIIILjava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/lang/String;DJJLkotlinx/serialization/internal/SerializationConstructorMarker;)V", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;IIIIIIILjava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/lang/String;DJJ)V", "getActiveCustomers", "()I", "getAverageInvoiceValue$annotations", "()V", "getAverageInvoiceValue", "()Ljava/math/BigDecimal;", "getBestSellingDay", "()Ljava/lang/String;", "getCashSales$annotations", "getCashSales", "getCreatedAt", "()J", "getCreditSales$annotations", "getCreditSales", "getLastActivityDate", "getNewCustomersThisMonth", "getOverdueInvoices", "getPaidInvoices", "getPendingInvoices", "getPerformanceScore", "()D", "getSalesLastMonth$annotations", "getSalesLastMonth", "getSalesThisMonth$annotations", "getSalesThisMonth", "getSalesThisYear$annotations", "getSalesThisYear", "getSellerEmail", "getSellerId", "getSellerName", "getTotalCustomers", "getTotalInvoices", "getTotalSales$annotations", "getTotalSales", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "toString", "write$Self", "", "self", "output", "Lkotlinx/serialization/encoding/CompositeEncoder;", "serialDesc", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "write$Self$app_debug", "$serializer", "Companion", "app_debug"})
public final class SellerStatistics {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String sellerId = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String sellerName = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String sellerEmail = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal totalSales = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal cashSales = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal creditSales = null;
    private final int totalInvoices = 0;
    private final int paidInvoices = 0;
    private final int pendingInvoices = 0;
    private final int overdueInvoices = 0;
    private final int totalCustomers = 0;
    private final int newCustomersThisMonth = 0;
    private final int activeCustomers = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal salesThisMonth = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal salesLastMonth = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal salesThisYear = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal averageInvoiceValue = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String bestSellingDay = null;
    private final double performanceScore = 0.0;
    private final long lastActivityDate = 0L;
    private final long createdAt = 0L;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.sharenshop.data.model.SellerStatistics.Companion Companion = null;
    
    public SellerStatistics(@org.jetbrains.annotations.NotNull()
    java.lang.String sellerId, @org.jetbrains.annotations.NotNull()
    java.lang.String sellerName, @org.jetbrains.annotations.NotNull()
    java.lang.String sellerEmail, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalSales, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal cashSales, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal creditSales, int totalInvoices, int paidInvoices, int pendingInvoices, int overdueInvoices, int totalCustomers, int newCustomersThisMonth, int activeCustomers, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal salesThisMonth, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal salesLastMonth, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal salesThisYear, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal averageInvoiceValue, @org.jetbrains.annotations.Nullable()
    java.lang.String bestSellingDay, double performanceScore, long lastActivityDate, long createdAt) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSellerId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSellerName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSellerEmail() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getTotalSales() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getTotalSales$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getCashSales() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getCashSales$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getCreditSales() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getCreditSales$annotations() {
    }
    
    public final int getTotalInvoices() {
        return 0;
    }
    
    public final int getPaidInvoices() {
        return 0;
    }
    
    public final int getPendingInvoices() {
        return 0;
    }
    
    public final int getOverdueInvoices() {
        return 0;
    }
    
    public final int getTotalCustomers() {
        return 0;
    }
    
    public final int getNewCustomersThisMonth() {
        return 0;
    }
    
    public final int getActiveCustomers() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getSalesThisMonth() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getSalesThisMonth$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getSalesLastMonth() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getSalesLastMonth$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getSalesThisYear() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getSalesThisYear$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getAverageInvoiceValue() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getAverageInvoiceValue$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getBestSellingDay() {
        return null;
    }
    
    public final double getPerformanceScore() {
        return 0.0;
    }
    
    public final long getLastActivityDate() {
        return 0L;
    }
    
    public final long getCreatedAt() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final int component10() {
        return 0;
    }
    
    public final int component11() {
        return 0;
    }
    
    public final int component12() {
        return 0;
    }
    
    public final int component13() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component14() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component15() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component16() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component17() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component18() {
        return null;
    }
    
    public final double component19() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final long component20() {
        return 0L;
    }
    
    public final long component21() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component6() {
        return null;
    }
    
    public final int component7() {
        return 0;
    }
    
    public final int component8() {
        return 0;
    }
    
    public final int component9() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.model.SellerStatistics copy(@org.jetbrains.annotations.NotNull()
    java.lang.String sellerId, @org.jetbrains.annotations.NotNull()
    java.lang.String sellerName, @org.jetbrains.annotations.NotNull()
    java.lang.String sellerEmail, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalSales, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal cashSales, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal creditSales, int totalInvoices, int paidInvoices, int pendingInvoices, int overdueInvoices, int totalCustomers, int newCustomersThisMonth, int activeCustomers, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal salesThisMonth, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal salesLastMonth, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal salesThisYear, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal averageInvoiceValue, @org.jetbrains.annotations.Nullable()
    java.lang.String bestSellingDay, double performanceScore, long lastActivityDate, long createdAt) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.jvm.JvmStatic()
    public static final void write$Self$app_debug(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.SellerStatistics self, @org.jetbrains.annotations.NotNull()
    kotlinx.serialization.encoding.CompositeEncoder output, @org.jetbrains.annotations.NotNull()
    kotlinx.serialization.descriptors.SerialDescriptor serialDesc) {
    }
    
    /**
     * آمار کامل فروشنده برای مدیر کل
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0003J\u0018\u0010\b\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\n0\tH\u00d6\u0001\u00a2\u0006\u0002\u0010\u000bJ\u0011\u0010\f\u001a\u00020\u00022\u0006\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\u0019\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0002H\u00d6\u0001R\u0014\u0010\u0004\u001a\u00020\u00058VX\u00d6\u0005\u00a2\u0006\u0006\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\u0014"}, d2 = {"com/example/sharenshop/data/model/SellerStatistics.$serializer", "Lkotlinx/serialization/internal/GeneratedSerializer;", "Lcom/example/sharenshop/data/model/SellerStatistics;", "()V", "descriptor", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "getDescriptor", "()Lkotlinx/serialization/descriptors/SerialDescriptor;", "childSerializers", "", "Lkotlinx/serialization/KSerializer;", "()[Lkotlinx/serialization/KSerializer;", "deserialize", "decoder", "Lkotlinx/serialization/encoding/Decoder;", "serialize", "", "encoder", "Lkotlinx/serialization/encoding/Encoder;", "value", "app_debug"})
    @java.lang.Deprecated()
    public static final class $serializer implements kotlinx.serialization.internal.GeneratedSerializer<com.example.sharenshop.data.model.SellerStatistics> {
        @org.jetbrains.annotations.NotNull()
        public static final com.example.sharenshop.data.model.SellerStatistics.$serializer INSTANCE = null;
        
        private $serializer() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public kotlinx.serialization.KSerializer<?>[] childSerializers() {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public com.example.sharenshop.data.model.SellerStatistics deserialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Decoder decoder) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public kotlinx.serialization.descriptors.SerialDescriptor getDescriptor() {
            return null;
        }
        
        @java.lang.Override()
        public void serialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Encoder encoder, @org.jetbrains.annotations.NotNull()
        com.example.sharenshop.data.model.SellerStatistics value) {
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public kotlinx.serialization.KSerializer<?>[] typeParametersSerializers() {
            return null;
        }
    }
    
    /**
     * آمار کامل فروشنده برای مدیر کل
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004H\u00c6\u0001\u00a8\u0006\u0006"}, d2 = {"Lcom/example/sharenshop/data/model/SellerStatistics$Companion;", "", "()V", "serializer", "Lkotlinx/serialization/KSerializer;", "Lcom/example/sharenshop/data/model/SellerStatistics;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<com.example.sharenshop.data.model.SellerStatistics> serializer() {
            return null;
        }
    }
}