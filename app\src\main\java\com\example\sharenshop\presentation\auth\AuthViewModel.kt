package com.example.sharenshop.presentation.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharenshop.data.local.SessionManager
import com.example.sharenshop.data.model.User // اطمینان از وجود این ایمپورت اگر User استفاده می‌شود
import com.example.sharenshop.data.model.UserRole
import com.example.sharenshop.data.remote.SupabaseClient
import com.example.sharenshop.domain.use_case.UserUseCases
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
// import java.util.UUID // اگر در بخش کامنت شده User استفاده می‌شود
import javax.inject.Inject

@HiltViewModel
class AuthViewModel @Inject constructor(
    private val userUseCases: UserUseCases,
    private val sessionManager: SessionManager
//    private val supabaseManager: SupabaseManager // این همچنان کامنت است
) : ViewModel() {

    private val _email = MutableStateFlow("")
    val email: StateFlow<String> = _email.asStateFlow()

    private val _password = MutableStateFlow("")
    val password: StateFlow<String> = _password.asStateFlow()

    private val _name = MutableStateFlow("")
    val name: StateFlow<String> = _name.asStateFlow()

    private val _phone = MutableStateFlow("")
    val phone: StateFlow<String> = _phone.asStateFlow()

    private val _role = MutableStateFlow(UserRole.CUSTOMER.value) // Default role for signup
    val role: StateFlow<String> = _role.asStateFlow()

    private val _referrerCode = MutableStateFlow("") // کد معرف فروشنده
    val referrerCode: StateFlow<String> = _referrerCode.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _authError = MutableStateFlow<String?>(null)
    val authError: StateFlow<String?> = _authError.asStateFlow()

    private val _isAuthenticated = MutableStateFlow(false)
    val isAuthenticated: StateFlow<Boolean> = _isAuthenticated.asStateFlow()

    private val _isLoginMode = MutableStateFlow(true) // true for login, false for signup
    val isLoginMode: StateFlow<Boolean> = _isLoginMode.asStateFlow()

    private val _isCustomerSignupMode = MutableStateFlow(false) // حالت ثبت نام مشتری
    val isCustomerSignupMode: StateFlow<Boolean> = _isCustomerSignupMode.asStateFlow()

    private val _pendingApproval = MutableStateFlow(false) // حالت انتظار تایید
    val pendingApproval: StateFlow<Boolean> = _pendingApproval.asStateFlow()

    init {
        // تست اتصال Supabase در شروع
        testSupabaseConnection()

        // بررسی session موجود
        checkExistingSession()
    }

    fun onEmailChange(newEmail: String) {
        _email.value = newEmail
    }

    fun onPasswordChange(newPassword: String) {
        _password.value = newPassword
    }

    fun onNameChange(newName: String) {
        _name.value = newName
    }

    fun onPhoneChange(newPhone: String) {
        _phone.value = newPhone
    }

    fun onRoleChange(newRole: String) {
        _role.value = newRole
    }

    fun onReferrerCodeChange(newCode: String) {
        _referrerCode.value = newCode
    }

    fun toggleAuthMode() {
        when {
            _isLoginMode.value -> {
                // از ورود به ثبت نام مشتری
                _isLoginMode.value = false
                _isCustomerSignupMode.value = true
            }
            _isCustomerSignupMode.value -> {
                // از ثبت نام مشتری به ورود
                _isLoginMode.value = true
                _isCustomerSignupMode.value = false
                clearSignupFields()
            }
            else -> {
                // حالت پیش‌فرض: ورود
                _isLoginMode.value = true
                _isCustomerSignupMode.value = false
                clearSignupFields()
            }
        }
        _authError.value = null
    }

    private fun clearSignupFields() {
        _name.value = ""
        _phone.value = ""
        _referrerCode.value = ""
        _role.value = UserRole.CUSTOMER.value
    }

    fun authenticate() {
        viewModelScope.launch {
            _isLoading.value = true
            _authError.value = null

            try {
                android.util.Log.d("AuthViewModel", "Starting authentication...")
                android.util.Log.d("AuthViewModel", "Email: ${_email.value}")
                android.util.Log.d("AuthViewModel", "IsLoginMode: ${_isLoginMode.value}")
                android.util.Log.d("AuthViewModel", "IsCustomerSignupMode: ${_isCustomerSignupMode.value}")

                when {
                    _isLoginMode.value -> {
                        // ورود مدیر/فروشنده/مشتری تایید شده
                        authenticateUser()
                    }
                    _isCustomerSignupMode.value -> {
                        // ثبت نام مشتری با درخواست تایید
                        submitCustomerApprovalRequest()
                    }
                    else -> {
                        _authError.value = "حالت نامعتبر"
                    }
                }

            } catch (e: Exception) {
                android.util.Log.e("AuthViewModel", "Authentication failed", e)
                _authError.value = e.localizedMessage ?: "خطای احراز هویت"
            }
            _isLoading.value = false
        }
    }

    private suspend fun authenticateUser() {
        android.util.Log.d("AuthViewModel", "Authenticating user with Supabase...")

        try {
            // ورود با Supabase
            val result = SupabaseClient.auth.signInWith(
                io.github.jan.supabase.gotrue.providers.builtin.Email
            ) {
                this.email = _email.value
                this.password = _password.value
            }

            android.util.Log.d("AuthViewModel", "Supabase login successful")

            // TODO: دریافت اطلاعات کاربر از پایگاه داده
            android.util.Log.d("AuthViewModel", "Login completed")

            // ذخیره session کاربر
            saveUserSession()

            _isAuthenticated.value = true

        } catch (e: Exception) {
            android.util.Log.e("AuthViewModel", "Supabase login failed: ${e.message}")

            when {
                e.message?.contains("Invalid login credentials") == true -> {
                    _authError.value = "ایمیل یا رمز عبور اشتباه است"
                }
                e.message?.contains("Email not confirmed") == true -> {
                    _authError.value = "ایمیل تایید نشده است"
                }
                e.message?.contains("Account pending approval") == true -> {
                    // هدایت به صفحه انتظار تایید
                    _authError.value = "حساب شما در انتظار تایید است"
                    _pendingApproval.value = true
                }
                e.message?.contains("network") == true -> {
                    _authError.value = "مشکل در اتصال به اینترنت"
                }
                else -> {
                    _authError.value = "خطا در ورود: ${e.message}"
                }
            }
        }
    }

    private suspend fun submitCustomerApprovalRequest() {
        android.util.Log.d("AuthViewModel", "Submitting customer approval request...")

        // اعتبارسنجی فیلدها
        if (_name.value.isBlank()) {
            _authError.value = "نام الزامی است"
            return
        }
        if (_phone.value.isBlank()) {
            _authError.value = "شماره تلفن الزامی است"
            return
        }
        if (_referrerCode.value.isBlank()) {
            _authError.value = "کد معرف الزامی است"
            return
        }

        // TODO: پیاده‌سازی ارسال درخواست تایید
        delay(1500) // شبیه‌سازی فراخوانی شبکه

        android.util.Log.d("AuthViewModel", "Customer approval request submitted successfully!")
        _authError.value = "✅ درخواست شما ارسال شد. منتظر تایید فروشنده باشید."
    }

    private fun testSupabaseConnection() {
        android.util.Log.d("AuthViewModel", "🔄 تست اتصال Supabase...")

        try {
            val isConfigured = SupabaseClient.isConfigured()
            android.util.Log.d("AuthViewModel", "✅ Supabase configured: $isConfigured")

            if (!isConfigured) {
                android.util.Log.e("AuthViewModel", "❌ Supabase URL یا Key خالی است!")
            }

        } catch (e: Exception) {
            android.util.Log.e("AuthViewModel", "❌ خطای تست Supabase: ${e.message}")
        }
    }

    // بررسی session موجود
    private fun checkExistingSession() {
        viewModelScope.launch {
            try {
                android.util.Log.d("AuthViewModel", "🔄 بررسی session موجود...")

                if (sessionManager.isValidSession()) {
                    android.util.Log.d("AuthViewModel", "✅ Session معتبر پیدا شد")
                    _isAuthenticated.value = true
                } else {
                    android.util.Log.d("AuthViewModel", "❌ Session معتبر پیدا نشد")
                    sessionManager.clearSession() // پاک کردن session نامعتبر
                }

            } catch (e: Exception) {
                android.util.Log.e("AuthViewModel", "❌ خطا در بررسی session: ${e.message}")
                sessionManager.clearSession()
            }
        }
    }

    // ذخیره session کاربر
    private suspend fun saveUserSession() {
        try {
            // TODO: دریافت اطلاعات کاربر از Supabase
            // فعلاً از اطلاعات وارد شده استفاده می‌کنم

            val userRole = when {
                _email.value.contains("admin") ||
                _email.value.contains("manager") ||
                _email.value == "<EMAIL>" -> UserRole.SUPER_ADMIN
                _email.value.contains("cashier") ||
                _email.value.contains("seller") -> UserRole.CASHIER
                else -> UserRole.CUSTOMER
            }

            android.util.Log.d("AuthViewModel", "🔍 Email: ${_email.value}")
            android.util.Log.d("AuthViewModel", "🔍 Detected Role: $userRole")

            sessionManager.saveUserSession(
                email = _email.value,
                name = _name.value.ifBlank { _email.value.substringBefore("@") },
                role = userRole,
                userId = "temp_${System.currentTimeMillis()}", // TODO: دریافت از Supabase
                accessToken = null, // TODO: دریافت از Supabase
                refreshToken = null // TODO: دریافت از Supabase
            )

            android.util.Log.d("AuthViewModel", "✅ Session ذخیره شد با نقش: $userRole")

        } catch (e: Exception) {
            android.util.Log.e("AuthViewModel", "❌ خطا در ذخیره session: ${e.message}")
        }
    }

    // خروج از حساب
    fun logout() {
        viewModelScope.launch {
            try {
                android.util.Log.d("AuthViewModel", "🔄 خروج از حساب...")

                // خروج از Supabase
                SupabaseClient.auth.signOut()

                // پاک کردن session محلی
                sessionManager.clearSession()

                // ریست کردن state ها
                _isAuthenticated.value = false
                _email.value = ""
                _password.value = ""
                _name.value = ""
                _phone.value = ""
                _referrerCode.value = ""
                _authError.value = null

                android.util.Log.d("AuthViewModel", "✅ خروج موفق")

            } catch (e: Exception) {
                android.util.Log.e("AuthViewModel", "❌ خطا در خروج: ${e.message}")
                // حتی اگر خطا باشه، session محلی رو پاک کن
                sessionManager.clearSession()
                _isAuthenticated.value = false
            }
        }
    }

    // دریافت نقش کاربر فعلی
    fun getCurrentUserRole(): UserRole? {
        val role = sessionManager.getUserRole()
        android.util.Log.d("AuthViewModel", "🔍 Current User Role: $role")
        android.util.Log.d("AuthViewModel", "🔍 Current User Email: ${sessionManager.getUserEmail()}")
        return role
    }

    // Force logout برای تست
    fun forceLogout() {
        android.util.Log.d("AuthViewModel", "🔄 Force logout...")
        sessionManager.clearSession()
        _isAuthenticated.value = false
        _email.value = ""
        _password.value = ""
        android.util.Log.d("AuthViewModel", "✅ Force logout completed")
    }
}