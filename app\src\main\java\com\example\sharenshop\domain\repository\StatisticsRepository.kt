package com.example.sharenshop.domain.repository

import kotlinx.coroutines.flow.Flow
import java.math.BigDecimal

interface StatisticsRepository {
    fun getTotalSalesAmount(): Flow<BigDecimal>
    fun getTotalProductsSold(): Flow<Int>
    fun getTopSellingProducts(limit: Int): Flow<Map<String, Int>> // Map of Product ID to Quantity
    fun getCustomerSpending(customerId: String): Flow<BigDecimal>
    fun getSalesByPaymentStatus(status: String): Flow<BigDecimal>
    fun getSalesByTimePeriod(startTime: Long, endTime: Long): Flow<BigDecimal>
    // Add more statistics methods as needed
} 