-- 🚀 بروزرسانی Supabase برای سیستم پرداخت و تصویه حساب
-- این اسکریپت را در SQL Editor پنل Supabase اجرا کنید

-- 1. ایجاد جدول پرداخت‌ها
CREATE TABLE IF NOT EXISTS public.payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID REFERENCES public.customers(id),
    receiver_id UUID REFERENCES public.users(id),
    amount DECIMAL(15,2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL CHECK (payment_method IN ('cash', 'card_to_card')),
    tracking_number VARCHAR(100),
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'rejected')),
    payment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    confirmation_date TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. ایجاد جدول تصویه حساب
CREATE TABLE IF NOT EXISTS public.settlements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    seller_id UUID REFERENCES public.users(id),
    manager_id UUID REFERENCES public.users(id),
    amount DECIMAL(15,2) NOT NULL,
    settlement_type VARCHAR(50) NOT NULL CHECK (settlement_type IN ('cash_received', 'credit_collection', 'commission_settlement', 'other')),
    description TEXT,
    tracking_number VARCHAR(100),
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'partially_approved')),
    request_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    response_date TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. ایجاد جدول تراکنش‌های مالی
CREATE TABLE IF NOT EXISTS public.transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    reference_id UUID NOT NULL,
    reference_type VARCHAR(50) NOT NULL CHECK (reference_type IN ('invoice', 'payment', 'settlement', 'adjustment')),
    from_account_id UUID,
    to_account_id UUID,
    amount DECIMAL(15,2) NOT NULL,
    transaction_category VARCHAR(50) NOT NULL CHECK (transaction_category IN ('sales_revenue', 'cash_receipt', 'credit_receipt', 'settlement_out', 'settlement_in', 'commission', 'discount', 'adjustment')),
    description TEXT NOT NULL,
    transaction_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. ایجاد جدول حساب‌های مالی
CREATE TABLE IF NOT EXISTS public.accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID NOT NULL,
    owner_type VARCHAR(50) NOT NULL CHECK (owner_type IN ('customer', 'seller', 'manager', 'company')),
    account_type VARCHAR(50) NOT NULL CHECK (account_type IN ('receivable', 'payable', 'cash', 'commission')),
    balance DECIMAL(15,2) DEFAULT 0,
    credit_limit DECIMAL(15,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. بروزرسانی جدول فاکتورها
ALTER TABLE public.invoices 
ADD COLUMN IF NOT EXISTS paid_amount DECIMAL(15,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS remaining_amount DECIMAL(15,2);

-- محاسبه مانده برای فاکتورهای موجود
UPDATE public.invoices 
SET remaining_amount = final_amount - paid_amount 
WHERE remaining_amount IS NULL;

-- 6. ایجاد اندیس‌ها برای بهبود عملکرد
CREATE INDEX IF NOT EXISTS idx_payments_customer_id ON public.payments(customer_id);
CREATE INDEX IF NOT EXISTS idx_payments_receiver_id ON public.payments(receiver_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON public.payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_payment_date ON public.payments(payment_date);

CREATE INDEX IF NOT EXISTS idx_settlements_seller_id ON public.settlements(seller_id);
CREATE INDEX IF NOT EXISTS idx_settlements_manager_id ON public.settlements(manager_id);
CREATE INDEX IF NOT EXISTS idx_settlements_status ON public.settlements(status);
CREATE INDEX IF NOT EXISTS idx_settlements_request_date ON public.settlements(request_date);

CREATE INDEX IF NOT EXISTS idx_transactions_reference_id ON public.transactions(reference_id);
CREATE INDEX IF NOT EXISTS idx_transactions_reference_type ON public.transactions(reference_type);
CREATE INDEX IF NOT EXISTS idx_transactions_transaction_date ON public.transactions(transaction_date);
CREATE INDEX IF NOT EXISTS idx_transactions_created_by ON public.transactions(created_by);

CREATE INDEX IF NOT EXISTS idx_accounts_owner_id ON public.accounts(owner_id);
CREATE INDEX IF NOT EXISTS idx_accounts_owner_type ON public.accounts(owner_type);
CREATE INDEX IF NOT EXISTS idx_accounts_account_type ON public.accounts(account_type);

-- 7. ایجاد تریگرهای updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON public.payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_settlements_updated_at BEFORE UPDATE ON public.settlements FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_accounts_updated_at BEFORE UPDATE ON public.accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 8. ایجاد داده‌های نمونه برای پرداخت‌ها
INSERT INTO public.payments (customer_id, receiver_id, amount, payment_method, description, status) 
SELECT 
    c.id,
    u.id,
    500000,
    'cash',
    'پرداخت نقدی بابت فاکتور',
    'confirmed'
FROM public.customers c, public.users u 
WHERE c.email = '<EMAIL>' AND u.username = 'ahmad_seller'
LIMIT 1;

INSERT INTO public.payments (customer_id, receiver_id, amount, payment_method, tracking_number, description, status) 
SELECT 
    c.id,
    u.id,
    300000,
    'card_to_card',
    '*********',
    'پرداخت کارت به کارت',
    'pending'
FROM public.customers c, public.users u 
WHERE c.email = '<EMAIL>' AND u.username = 'ali_seller'
LIMIT 1;

-- 9. ایجاد داده‌های نمونه برای تصویه حساب
INSERT INTO public.settlements (seller_id, manager_id, amount, settlement_type, description, status) 
SELECT 
    s.id,
    m.id,
    2000000,
    'cash_received',
    'تصویه پول نقد دریافتی از مشتریان',
    'pending'
FROM public.users s, public.users m 
WHERE s.username = 'ahmad_seller' AND m.user_type = 'super_admin'
LIMIT 1;

-- 10. ایجاد حساب‌های پیش‌فرض
-- حساب دریافتنی برای مشتریان
INSERT INTO public.accounts (owner_id, owner_type, account_type, balance)
SELECT id, 'customer', 'receivable', balance
FROM public.customers;

-- حساب نقدی برای فروشندگان
INSERT INTO public.accounts (owner_id, owner_type, account_type, balance)
SELECT id, 'seller', 'cash', 0
FROM public.users WHERE user_type = 'cashier';

-- حساب کمیسیون برای فروشندگان
INSERT INTO public.accounts (owner_id, owner_type, account_type, balance)
SELECT id, 'seller', 'commission', 0
FROM public.users WHERE user_type = 'cashier';

-- 11. تنظیم RLS برای جداول جدید
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.settlements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.accounts ENABLE ROW LEVEL SECURITY;

-- Policy برای payments
CREATE POLICY "Super admin full access to payments" ON public.payments FOR ALL USING (
    EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND user_type = 'super_admin')
);

CREATE POLICY "Cashiers can manage related payments" ON public.payments FOR ALL USING (
    receiver_id = auth.uid() OR 
    EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND user_type = 'super_admin')
);

CREATE POLICY "Customers can view own payments" ON public.payments FOR SELECT USING (
    customer_id IN (
        SELECT id FROM public.customers 
        WHERE email = (SELECT email FROM auth.users WHERE id = auth.uid())
    )
);

-- Policy برای settlements
CREATE POLICY "Super admin full access to settlements" ON public.settlements FOR ALL USING (
    EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND user_type = 'super_admin')
);

CREATE POLICY "Cashiers can manage own settlements" ON public.settlements FOR ALL USING (
    seller_id = auth.uid() OR 
    EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND user_type = 'super_admin')
);

-- Policy برای transactions
CREATE POLICY "Super admin full access to transactions" ON public.transactions FOR ALL USING (
    EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND user_type = 'super_admin')
);

CREATE POLICY "Users can view related transactions" ON public.transactions FOR SELECT USING (
    created_by = auth.uid() OR 
    from_account_id::text = auth.uid()::text OR 
    to_account_id::text = auth.uid()::text OR
    EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND user_type = 'super_admin')
);

-- Policy برای accounts
CREATE POLICY "Super admin full access to accounts" ON public.accounts FOR ALL USING (
    EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND user_type = 'super_admin')
);

CREATE POLICY "Users can view own accounts" ON public.accounts FOR SELECT USING (
    owner_id::text = auth.uid()::text OR
    EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND user_type = 'super_admin')
);

-- پیام موفقیت
SELECT 'Payment and Settlement system setup completed successfully! 💰🏦' as message;
