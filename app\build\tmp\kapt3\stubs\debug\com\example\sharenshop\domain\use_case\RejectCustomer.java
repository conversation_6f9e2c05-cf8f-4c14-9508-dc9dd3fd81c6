package com.example.sharenshop.domain.use_case;

/**
 * رد درخواست مشتری
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J8\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\tH\u0086B\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\f\u0010\rR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u000e"}, d2 = {"Lcom/example/sharenshop/domain/use_case/RejectCustomer;", "", "customerApprovalRepository", "Lcom/example/sharenshop/domain/repository/CustomerApprovalRepository;", "(Lcom/example/sharenshop/domain/repository/CustomerApprovalRepository;)V", "invoke", "Lkotlin/Result;", "", "requestId", "", "rejecterId", "reason", "invoke-BWLJW6A", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class RejectCustomer {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.repository.CustomerApprovalRepository customerApprovalRepository = null;
    
    @javax.inject.Inject()
    public RejectCustomer(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.CustomerApprovalRepository customerApprovalRepository) {
        super();
    }
}