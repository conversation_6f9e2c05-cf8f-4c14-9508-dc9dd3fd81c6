// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.domain.repository.StatisticsRepository;
import com.example.sharenshop.domain.use_case.StatisticsUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideStatisticsUseCasesFactory implements Factory<StatisticsUseCases> {
  private final Provider<StatisticsRepository> repositoryProvider;

  public UseCaseModule_ProvideStatisticsUseCasesFactory(
      Provider<StatisticsRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public StatisticsUseCases get() {
    return provideStatisticsUseCases(repositoryProvider.get());
  }

  public static UseCaseModule_ProvideStatisticsUseCasesFactory create(
      Provider<StatisticsRepository> repositoryProvider) {
    return new UseCaseModule_ProvideStatisticsUseCasesFactory(repositoryProvider);
  }

  public static StatisticsUseCases provideStatisticsUseCases(StatisticsRepository repository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideStatisticsUseCases(repository));
  }
}
