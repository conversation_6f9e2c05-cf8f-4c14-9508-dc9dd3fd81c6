kotlin.Enum2kotlinx.serialization.internal.GeneratedSerializer6com.example.sharenshop.data.security.EncryptionServiceandroidx.lifecycle.ViewModel+com.example.sharenshop.ui.navigation.Screen#androidx.activity.ComponentActivityandroid.app.Application"androidx.datastore.core.Serializerandroidx.room.RoomDatabase;com.example.sharenshop.domain.repository.CustomerRepository:com.example.sharenshop.domain.repository.InvoiceRepository:com.example.sharenshop.domain.repository.ProductRepository;com.example.sharenshop.domain.repository.SecurityRepository9com.example.sharenshop.domain.repository.SellerRepository;com.example.sharenshop.domain.repository.SettingsRepository=com.example.sharenshop.domain.repository.StatisticsRepository7com.example.sharenshop.domain.repository.UserRepository?com.example.sharenshop.domain.repository.NotificationRepository                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             