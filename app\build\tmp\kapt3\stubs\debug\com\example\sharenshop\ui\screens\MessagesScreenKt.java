package com.example.sharenshop.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000j\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\u0018\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a2\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\rH\u0007\u001a\u0018\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\u0010\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u0011H\u0007\u001a\u0018\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\u0018\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\u0018\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\u0018\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\u0018\u0010\u0016\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a,\u0010\u0017\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00112\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00010\u00192\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00010\u0019H\u0007\u001a,\u0010\u001b\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00112\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\u00192\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00010\u0019H\u0007\u001a\u001e\u0010\u001e\u001a\u00020\u00012\u0006\u0010\u001f\u001a\u00020 2\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00010\u0019H\u0007\u001a\b\u0010\"\u001a\u00020\u0001H\u0007\u001a\u001a\u0010#\u001a\u00020\u00012\u0006\u0010$\u001a\u00020%2\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\u0018\u0010&\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\u0018\u0010\'\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\u000e\u0010(\u001a\u00020 2\u0006\u0010)\u001a\u00020*\u001a\u0013\u0010+\u001a\u00020,2\u0006\u0010-\u001a\u00020.\u00a2\u0006\u0002\u0010/\u001a\u000e\u00100\u001a\u0002012\u0006\u0010-\u001a\u00020.\u001a\u000e\u00102\u001a\u00020 2\u0006\u00103\u001a\u00020 \u00a8\u00064"}, d2 = {"AllMessagesTab", "", "viewModel", "Lcom/example/sharenshop/presentation/notification/NotificationViewModel;", "uiState", "Lcom/example/sharenshop/presentation/notification/NotificationUiState;", "AnimatedTabRow", "selectedTabIndex", "", "tabs", "", "Lcom/example/sharenshop/ui/screens/TabInfo;", "onTabSelected", "Lkotlin/Function1;", "ApprovalRequestsTab", "ApprovalStatusCard", "notification", "Lcom/example/sharenshop/data/model/Notification;", "CashierApprovalRequestsTab", "CashierMessagesTab", "CustomerApprovalStatusTab", "CustomerMessagesTab", "CustomerNotificationsTab", "EnhancedApprovalRequestCard", "onApprove", "Lkotlin/Function0;", "onReject", "EnhancedMessageCard", "onMarkAsRead", "onDelete", "ErrorCard", "error", "", "onDismiss", "LoadingIndicator", "MessagesScreen", "userRole", "Lcom/example/sharenshop/data/model/UserRole;", "NotificationsTab", "SystemMessagesTab", "formatTime", "timestamp", "", "getNotificationColor", "Landroidx/compose/ui/graphics/Color;", "type", "Lcom/example/sharenshop/data/model/NotificationType;", "(Lcom/example/sharenshop/data/model/NotificationType;)J", "getNotificationIcon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "getUserDisplayName", "userId", "app_debug"})
public final class MessagesScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void MessagesScreen(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AnimatedTabRow(int selectedTabIndex, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.sharenshop.ui.screens.TabInfo> tabs, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onTabSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ErrorCard(@org.jetbrains.annotations.NotNull()
    java.lang.String error, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AllMessagesTab(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationUiState uiState) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ApprovalRequestsTab(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationUiState uiState) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SystemMessagesTab(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationUiState uiState) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CashierMessagesTab(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationUiState uiState) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CashierApprovalRequestsTab(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationUiState uiState) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void NotificationsTab(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationUiState uiState) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CustomerMessagesTab(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationUiState uiState) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CustomerApprovalStatusTab(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationUiState uiState) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CustomerNotificationsTab(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.presentation.notification.NotificationUiState uiState) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void EnhancedMessageCard(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Notification notification, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onMarkAsRead, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDelete) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EnhancedApprovalRequestCard(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Notification notification, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onApprove, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onReject) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ApprovalStatusCard(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.Notification notification) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void LoadingIndicator() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.ui.graphics.vector.ImageVector getNotificationIcon(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.NotificationType type) {
        return null;
    }
    
    public static final long getNotificationColor(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.NotificationType type) {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getUserDisplayName(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatTime(long timestamp) {
        return null;
    }
}