{"logs": [{"outputFile": "com.example.sharenshop.app-mergeDebugResources-2:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\07904e3b96b1871d1573b4ff52afdf7d\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "98,117", "startColumns": "4,4", "startOffsets": "5930,6939", "endColumns": "41,59", "endOffsets": "5967,6994"}}, {"source": "B:\\SHARENAPP\\sharenshop\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "47", "endOffsets": "100"}, "to": {"startLines": "124", "startColumns": "4", "startOffsets": "7373", "endColumns": "47", "endOffsets": "7416"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cb5659120cd5b84eb209142f16865734\\transformed\\navigation-common-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "367,380,386,392,401", "startColumns": "4,4,4,4,4", "startOffsets": "19540,20179,20423,20670,21033", "endLines": "379,385,391,394,405", "endColumns": "24,24,24,24,24", "endOffsets": "20174,20418,20665,20798,21210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3f3cdab62cfcc338805420ad3e7c8126\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "119", "startColumns": "4", "startOffsets": "7053", "endColumns": "49", "endOffsets": "7098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88c123eabfc6d4b207c77cc8294f818f\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "143,144,145,146,147,148,149,150,151,152,155,156,157,158,159,160,161,162,163,164,165,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8671,8759,8845,8926,9010,9079,9144,9227,9333,9419,9539,9593,9662,9723,9792,9881,9976,10050,10147,10240,10338,10487,10578,10666,10762,10860,10924,10992,11079,11173,11240,11312,11384,11485,11594,11670,11739,11787,11853,11917,11974,12031,12103,12153,12207,12278,12349,12419,12488,12546,12622,12693,12767,12853,12903,12973", "endLines": "143,144,145,146,147,148,149,150,151,154,155,156,157,158,159,160,161,162,163,164,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "8754,8840,8921,9005,9074,9139,9222,9328,9414,9534,9588,9657,9718,9787,9876,9971,10045,10142,10235,10333,10482,10573,10661,10757,10855,10919,10987,11074,11168,11235,11307,11379,11480,11589,11665,11734,11782,11848,11912,11969,12026,12098,12148,12202,12273,12344,12414,12483,12541,12617,12688,12762,12848,12898,12968,13033"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\872be39328562689a0cb04e5ae73da72\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "90,99,120,319,324", "startColumns": "4,4,4,4,4", "startOffsets": "5523,5972,7103,18372,18542", "endLines": "90,99,120,323,327", "endColumns": "56,64,63,24,24", "endOffsets": "5575,6032,7162,18537,18686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3798808f819c35dff1b7afc99e522a63\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "123", "startColumns": "4", "startOffsets": "7290", "endColumns": "82", "endOffsets": "7368"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8bf4b3fb7fecc46740c95cb34de6afa1\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,10,11,22,23,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,94,95,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,122,125,126,127,128,129,130,131,210,226,227,231,232,236,240,241,249,255,265,298,328,361", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,661,726,1287,1356,1747,1817,1885,1957,2027,2088,2162,2235,2296,2357,2419,2483,2545,2606,2674,2774,2834,2900,2973,3042,3099,3151,3213,3285,3361,5740,5775,6037,6092,6155,6210,6268,6326,6387,6450,6507,6558,6608,6669,6726,6792,6826,6861,7220,7421,7488,7560,7629,7698,7772,7844,13374,14147,14264,14465,14575,14776,15013,15085,15456,15659,15960,17691,18691,19373", "endLines": "2,3,4,10,11,22,23,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,94,95,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,122,125,126,127,128,129,130,131,210,226,230,231,235,236,240,241,254,264,297,318,360,366", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,721,787,1351,1414,1812,1880,1952,2022,2083,2157,2230,2291,2352,2414,2478,2540,2601,2669,2769,2829,2895,2968,3037,3094,3146,3208,3280,3356,3421,5770,5805,6087,6150,6205,6263,6321,6382,6445,6502,6553,6603,6664,6721,6787,6821,6856,6891,7285,7483,7555,7624,7693,7767,7839,7927,13440,14259,14460,14570,14771,14900,15080,15147,15654,15955,17686,18367,19368,19535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b4a2163848cd7c6eb1b4d89b537b4df1\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "93,97", "startColumns": "4,4", "startOffsets": "5686,5863", "endColumns": "53,66", "endOffsets": "5735,5925"}}, {"source": "B:\\SHARENAPP\\sharenshop\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "59", "endLines": "5", "endColumns": "12", "endOffsets": "199"}, "to": {"startLines": "237", "startColumns": "4", "startOffsets": "14905", "endLines": "239", "endColumns": "12", "endOffsets": "15008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\beb880474af4802d615c3712a7fe5cf4\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "6,7,8,9,28,29,134,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "412,470,536,599,1604,1675,8051,8344,8411,8490", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "465,531,594,656,1670,1742,8114,8406,8485,8554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\859f969b136d5ecad99a6025b175074b\\transformed\\navigation-runtime-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "96,242,395,398", "startColumns": "4,4,4,4", "startOffsets": "5810,15152,20803,20918", "endLines": "96,248,397,400", "endColumns": "52,24,24,24", "endOffsets": "5858,15451,20913,21028"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88b5b0acc1ef090d93ba86ccb1fae5cf\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "214,215", "startColumns": "4,4", "startOffsets": "13618,13674", "endColumns": "55,54", "endOffsets": "13669,13724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6a7ef59679f6cb291960d80ac8238d7d\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "118", "startColumns": "4", "startOffsets": "6999", "endColumns": "53", "endOffsets": "7048"}}, {"source": "B:\\SHARENAPP\\sharenshop\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "24,19,21,20,9,11,10,4,6,5,26,14,16,15,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "902,723,821,769,334,430,379,132,232,179,988,527,623,572,945", "endColumns": "41,44,49,50,43,48,49,45,50,51,56,43,48,49,41", "endOffsets": "939,763,866,815,373,474,424,173,278,226,1040,566,667,617,982"}, "to": {"startLines": "5,12,13,14,15,16,17,18,19,20,21,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,792,837,887,938,982,1031,1081,1127,1178,1230,1419,1463,1512,1562", "endColumns": "41,44,49,50,43,48,49,45,50,51,56,43,48,49,41", "endOffsets": "407,832,882,933,977,1026,1076,1122,1173,1225,1282,1458,1507,1557,1599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\61acd70f2f91261ed7f816e4fc4161c1\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,92,121,132,133,135,136,137,141,142,203,204,205,206,207,208,209,211,212,213,216,219,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3426,3485,3544,3604,3664,3724,3784,3844,3904,3964,4024,4084,4144,4203,4263,4323,4383,4443,4503,4563,4623,4683,4743,4803,4862,4922,4982,5041,5100,5159,5218,5277,5336,5410,5468,5580,5631,7167,7932,7997,8119,8185,8286,8559,8611,13038,13100,13154,13190,13224,13274,13328,13445,13492,13528,13729,13841,13952", "endLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,92,121,132,133,135,136,137,141,142,203,204,205,206,207,208,209,211,212,213,218,221,225", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "3480,3539,3599,3659,3719,3779,3839,3899,3959,4019,4079,4139,4198,4258,4318,4378,4438,4498,4558,4618,4678,4738,4798,4857,4917,4977,5036,5095,5154,5213,5272,5331,5405,5463,5518,5626,5681,7215,7992,8046,8180,8281,8339,8606,8666,13095,13149,13185,13219,13269,13323,13369,13487,13523,13613,13836,13947,14142"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2a1a0651a6f7e40fd9a97078d1359fac\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "116", "startColumns": "4", "startOffsets": "6896", "endColumns": "42", "endOffsets": "6934"}}]}]}