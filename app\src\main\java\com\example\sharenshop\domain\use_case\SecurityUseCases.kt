package com.example.sharenshop.domain.use_case

import com.example.sharenshop.data.model.EncryptionSettings
import com.example.sharenshop.domain.repository.SecurityRepository
import kotlinx.coroutines.flow.Flow

// Grouping all security-related use cases
data class SecurityUseCases(
    val getEncryptionSettings: GetEncryptionSettings,
    val saveEncryptionSettings: SaveEncryptionSettings,
    val encryptData: EncryptData,
    val decryptData: DecryptData
)

class GetEncryptionSettings(private val repository: SecurityRepository) {
    operator fun invoke(): Flow<EncryptionSettings> {
        return repository.getEncryptionSettings()
    }
}

class SaveEncryptionSettings(private val repository: SecurityRepository) {
    suspend operator fun invoke(settings: EncryptionSettings) {
        repository.saveEncryptionSettings(settings)
    }
}

class EncryptData(private val repository: SecurityRepository) {
    operator fun invoke(data: String): String {
        return repository.encryptData(data)
    }
}

class DecryptData(private val repository: SecurityRepository) {
    operator fun invoke(encryptedData: String): String {
        return repository.decryptData(encryptedData)
    }
} 