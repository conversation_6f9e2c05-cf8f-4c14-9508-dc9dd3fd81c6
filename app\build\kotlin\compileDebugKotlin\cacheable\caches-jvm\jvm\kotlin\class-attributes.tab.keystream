#com.example.sharenshop.MainActivity,com.example.sharenshop.SharenShopApplication;com.example.sharenshop.data.datastore.AppSettingsSerializer0com.example.sharenshop.data.local.SessionManager:com.example.sharenshop.data.local.SessionManager.Companion?com.example.sharenshop.data.local.converter.BigDecimalConverter9com.example.sharenshop.data.local.dao.CustomerApprovalDao1com.example.sharenshop.data.local.dao.CustomerDao0com.example.sharenshop.data.local.dao.InvoiceDao4com.example.sharenshop.data.local.dao.InvoiceItemDao5com.example.sharenshop.data.local.dao.NotificationDao0com.example.sharenshop.data.local.dao.ProductDao/com.example.sharenshop.data.local.dao.SellerDao-com.example.sharenshop.data.local.dao.UserDao6com.example.sharenshop.data.local.database.AppDatabase-com.example.sharenshop.data.model.AppSettings7com.example.sharenshop.data.model.AppSettings.Companion9com.example.sharenshop.data.model.AppSettings.$serializer*com.example.sharenshop.data.model.Customer4com.example.sharenshop.data.model.Customer.Companion6com.example.sharenshop.data.model.Customer.$serializer9com.example.sharenshop.data.model.CustomerApprovalRequestCcom.example.sharenshop.data.model.CustomerApprovalRequest.CompanionEcom.example.sharenshop.data.model.CustomerApprovalRequest.$serializer0com.example.sharenshop.data.model.ApprovalStatus:com.example.sharenshop.data.model.ApprovalStatus.Companion.com.example.sharenshop.data.model.Notification8com.example.sharenshop.data.model.Notification.Companion:com.example.sharenshop.data.model.Notification.$serializer2com.example.sharenshop.data.model.NotificationType<<EMAIL>><EMAIL>.$serializer)com.example.sharenshop.data.model.Invoice3com.example.sharenshop.data.model.Invoice.Companion5com.example.sharenshop.data.model.Invoice.$<EMAIL>>com.example.sharenshop.data.model.InvoicePaymentType.Companion-com.example.sharenshop.data.model.InvoiceItem7com.example.sharenshop.data.model.InvoiceItem.Companion9com.example.sharenshop.data.model.InvoiceItem.$serializer)com.example.sharenshop.data.model.Payment3com.example.sharenshop.data.model.Payment.Companion5com.example.sharenshop.data.model.Payment.$serializer/com.example.sharenshop.data.model.PaymentMethod9com.example.sharenshop.data.model.PaymentMethod.Companion/com.example.sharenshop.data.model.PaymentStatus9com.example.sharenshop.data.model.PaymentStatus.Companion0com.example.sharenshop.data.model.PaymentDetails,com.example.sharenshop.data.model.Permission1com.example.sharenshop.data.model.RolePermissions)com.example.sharenshop.data.model.Product3com.example.sharenshop.data.model.Product.Companion5com.example.sharenshop.data.model.Product.$serializer(com.example.sharenshop.data.model.Seller2com.example.sharenshop.data.model.Seller.Companion4com.example.sharenshop.data.model.Seller.$serializer2com.example.sharenshop.data.model.SellerStatistics<com.example.sharenshop.data.model.SellerStatistics.Companion>com.example.sharenshop.data.model.SellerStatistics.$serializer3com.example.sharenshop.data.model.OverallStatistics=com.example.sharenshop.data.model.OverallStatistics.Companion?com.example.sharenshop.data.model.OverallStatistics.$serializer2com.example.sharenshop.data.model.SellerComparison<com.example.sharenshop.data.model.SellerComparison.Companion>com.example.sharenshop.data.model.SellerComparison.$<EMAIL>.$serializer8com.example.sharenshop.data.model.MonthlySalesStatisticsBcom.example.sharenshop.data.model.MonthlySalesStatistics.CompanionDcom.example.sharenshop.data.model.MonthlySalesStatistics.$serializer3com.example.sharenshop.data.model.TopSellingProduct=com.example.sharenshop.data.model.TopSellingProduct.Companion?com.example.sharenshop.data.model.TopSellingProduct.$serializer-com.example.sharenshop.data.model.TopCustomer7com.example.sharenshop.data.model.TopCustomer.Companion9com.example.sharenshop.data.model.TopCustomer.$serializer,com.example.sharenshop.data.model.Settlement6com.example.sharenshop.data.model.Settlement.Companion8com.example.sharenshop.data.model.Settlement.$serializer0com.example.sharenshop.data.model.SettlementType:com.example.sharenshop.data.model.SettlementType.Companion2com.example.sharenshop.data.model.SettlementStatus<com.example.sharenshop.data.model.SettlementStatus.Companion9com.example.sharenshop.data.model.SellerSettlementSummary3com.example.sharenshop.data.model.SettlementDetails-com.example.sharenshop.data.model.Transaction7com.example.sharenshop.data.model.Transaction.Companion9com.example.sharenshop.data.model.Transaction.$serializer1com.example.sharenshop.data.model.TransactionType;com.example.sharenshop.data.model.TransactionType.Companion5com.example.sharenshop.data.model.TransactionCategory?com.example.sharenshop.data.model.TransactionCategory.Companion)com.example.sharenshop.data.model.Account3com.example.sharenshop.data.model.Account.Companion5com.example.sharenshop.data.model.Account.$serializer2com.example.sharenshop.data.model.AccountOwnerType<com.example.sharenshop.data.model.AccountOwnerType.Companion-com.example.sharenshop.data.model.AccountType7com.example.sharenshop.data.model.AccountType.Companion1com.example.sharenshop.data.model.FinancialReport*com.example.sharenshop.data.model.UserRole4com.example.sharenshop.data.model.UserRole.Companion&com.example.sharenshop.data.model.User0com.example.sharenshop.data.model.User.Companion2com.example.sharenshop.data.model.User.$serializer1com.example.sharenshop.data.remote.SupabaseClient4com.example.sharenshop.data.remote.SupabaseConstants=com.example.sharenshop.data.repository.CustomerRepositoryImpl<com.example.sharenshop.data.repository.InvoiceRepositoryImplAcom.example.sharenshop.data.repository.NotificationRepositoryImpl<com.example.sharenshop.data.repository.ProductRepositoryImpl=com.example.sharenshop.data.repository.SecurityRepositoryImpl;com.example.sharenshop.data.repository.SellerRepositoryImpl=com.example.sharenshop.data.repository.SettingsRepositoryImpl?com.example.sharenshop.data.repository.StatisticsRepositoryImpl9com.example.sharenshop.data.repository.UserRepositoryImpl>com.example.sharenshop.data.security.DatabaseEncryptionService6com.example.sharenshop.data.security.EncryptionService:com.example.sharenshop.data.security.FileEncryptionServiceGcom.example.sharenshop.data.security.SharedPreferencesEncryptionService#com.example.sharenshop.di.DaoModule(com.example.sharenshop.di.DatabaseModule'com.example.sharenshop.di.NetworkModule*com.example.sharenshop.di.RepositoryModule(com.example.sharenshop.di.SecurityModule2com.example.sharenshop.di.SecurityModule.Companion'com.example.sharenshop.di.UseCaseModuleCcom.example.sharenshop.domain.repository.CustomerApprovalRepository;com.example.sharenshop.domain.repository.CustomerRepository:com.example.sharenshop.domain.repository.InvoiceRepository?com.example.sharenshop.domain.repository.NotificationRepository:com.example.sharenshop.domain.repository.ProductRepository;com.example.sharenshop.domain.repository.SecurityRepository9com.example.sharenshop.domain.repository.SellerRepository;com.example.sharenshop.domain.repository.SettingsRepository=com.example.sharenshop.domain.repository.StatisticsRepository7com.example.sharenshop.domain.repository.UserRepository?com.example.sharenshop.domain.use_case.CustomerApprovalUseCases<com.example.sharenshop.domain.use_case.SubmitApprovalRequest6com.example.sharenshop.domain.use_case.ApproveCustomer5com.example.sharenshop.domain.use_case.RejectCustomer9com.example.sharenshop.domain.use_case.GetPendingRequests<com.example.sharenshop.domain.use_case.GetRequestsByReferrer;com.example.sharenshop.domain.use_case.ValidateReferrerCode7com.example.sharenshop.domain.use_case.CustomerUseCases2com.example.sharenshop.domain.use_case.GetCustomer5com.example.sharenshop.domain.use_case.InsertCustomer5com.example.sharenshop.domain.use_case.UpdateCustomer5com.example.sharenshop.domain.use_case.DeleteCustomer6com.example.sharenshop.domain.use_case.GetAllCustomers6com.example.sharenshop.domain.use_case.SearchCustomers6com.example.sharenshop.domain.use_case.InvoiceUseCases1com.example.sharenshop.domain.use_case.GetInvoice4com.example.sharenshop.domain.use_case.InsertInvoice4com.example.sharenshop.domain.use_case.UpdateInvoice4com.example.sharenshop.domain.use_case.DeleteInvoice5com.example.sharenshop.domain.use_case.GetAllInvoices<com.example.sharenshop.domain.use_case.GetInvoicesByCustomer8com.example.sharenshop.domain.use_case.GetInvoicesByUser6com.example.sharenshop.domain.use_case.ProductUseCases1com.example.sharenshop.domain.use_case.GetProduct4com.example.sharenshop.domain.use_case.InsertProduct4com.example.sharenshop.domain.use_case.UpdateProduct4com.example.sharenshop.domain.use_case.DeleteProduct5com.example.sharenshop.domain.use_case.GetAllProducts5com.example.sharenshop.domain.use_case.SearchProducts7com.example.sharenshop.domain.use_case.SecurityUseCases<com.example.sharenshop.domain.use_case.GetEncryptionSettings=com.example.sharenshop.domain.use_case.SaveEncryptionSettings2com.example.sharenshop.domain.use_case.EncryptData2com.example.sharenshop.domain.use_case.DecryptData5com.example.sharenshop.domain.use_case.SellerUseCases0com.example.sharenshop.domain.use_case.GetSeller3com.example.sharenshop.domain.use_case.InsertSeller3com.example.sharenshop.domain.use_case.UpdateSeller3com.example.sharenshop.domain.use_case.DeleteSeller4com.example.sharenshop.domain.use_case.GetAllSellers4com.example.sharenshop.domain.use_case.SearchSellers7com.example.sharenshop.domain.use_case.SettingsUseCases5com.example.sharenshop.domain.use_case.GetAppSettings6com.example.sharenshop.domain.use_case.SaveAppSettings9com.example.sharenshop.domain.use_case.StatisticsUseCases:com.example.sharenshop.domain.use_case.GetTotalSalesAmount;com.example.sharenshop.domain.use_case.GetTotalProductsSold<com.example.sharenshop.domain.use_case.GetTopSellingProducts:com.example.sharenshop.domain.use_case.GetCustomerSpending>com.example.sharenshop.domain.use_case.GetSalesByPaymentStatus;com.example.sharenshop.domain.use_case.GetSalesByTimePeriod3com.example.sharenshop.domain.use_case.UserUseCases.com.example.sharenshop.domain.use_case.GetUser1com.example.sharenshop.domain.use_case.InsertUser1com.example.sharenshop.domain.use_case.UpdateUser1com.example.sharenshop.domain.use_case.DeleteUser2com.example.sharenshop.domain.use_case.GetAllUsers5com.example.sharenshop.domain.use_case.GetUsersByType6com.example.sharenshop.presentation.auth.AuthViewModel6com.example.sharenshop.presentation.home.HomeViewModel<com.example.sharenshop.presentation.invoice.InvoiceViewModel6com.example.sharenshop.presentation.main.MainViewModelFcom.example.sharenshop.presentation.notification.NotificationViewModelDcom.example.sharenshop.presentation.notification.NotificationUiStateCcom.example.sharenshop.presentation.notification.NotificationHelper<com.example.sharenshop.presentation.product.ProductViewModel:com.example.sharenshop.presentation.seller.SellerViewModelBcom.example.sharenshop.presentation.statistics.StatisticsViewModelKcom.example.sharenshop.presentation.user_management.UserManagementViewModel+com.example.sharenshop.ui.navigation.Screen0com.example.sharenshop.ui.navigation.Screen.Auth0com.example.sharenshop.ui.navigation.Screen.Main0com.example.sharenshop.ui.navigation.Screen.Home3com.example.sharenshop.ui.navigation.Screen.Invoice3com.example.sharenshop.ui.navigation.Screen.Product2com.example.sharenshop.ui.navigation.Screen.Seller6com.example.sharenshop.ui.navigation.Screen.Statistics:com.example.sharenshop.ui.navigation.Screen.UserManagement3com.example.sharenshop.ui.navigation.Screen.Payment6com.example.sharenshop.ui.navigation.Screen.Settlement6com.example.sharenshop.ui.navigation.Screen.Accounting;com.example.sharenshop.ui.navigation.Screen.PendingApproval*com.example.sharenshop.ui.screens.MenuItem+com.example.sharenshop.ui.screens.QuickStat)com.example.sharenshop.ui.screens.TabInfo0com.example.sharenshop.ui.theme.SharenShopColors+com.example.sharenshop.ui.theme.ColorHelper-com.example.sharenshop.utils.PermissionHelper/com.example.sharenshop.utils.SupabaseTestHelper"com.example.sharenshop.BuildConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           