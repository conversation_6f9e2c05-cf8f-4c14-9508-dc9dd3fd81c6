// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.domain.repository.SellerRepository;
import com.example.sharenshop.domain.use_case.SellerUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideSellerUseCasesFactory implements Factory<SellerUseCases> {
  private final Provider<SellerRepository> repositoryProvider;

  public UseCaseModule_ProvideSellerUseCasesFactory(Provider<SellerRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public SellerUseCases get() {
    return provideSellerUseCases(repositoryProvider.get());
  }

  public static UseCaseModule_ProvideSellerUseCasesFactory create(
      Provider<SellerRepository> repositoryProvider) {
    return new UseCaseModule_ProvideSellerUseCasesFactory(repositoryProvider);
  }

  public static SellerUseCases provideSellerUseCases(SellerRepository repository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideSellerUseCases(repository));
  }
}
