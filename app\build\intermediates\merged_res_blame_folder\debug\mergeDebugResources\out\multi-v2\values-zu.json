{"logs": [{"outputFile": "com.example.sharenshop.app-mergeDebugResources-2:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88b5b0acc1ef090d93ba86ccb1fae5cf\\transformed\\foundation-release\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,146", "endColumns": "90,91", "endOffsets": "141,233"}, "to": {"startLines": "87,88", "startColumns": "4,4", "startOffsets": "9085,9176", "endColumns": "90,91", "endOffsets": "9171,9263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\61acd70f2f91261ed7f816e4fc4161c1\\transformed\\ui-release\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,282,387,492,582,664,753,846,929,997,1065,1153,1241,1317,1396,1466", "endColumns": "94,81,104,104,89,81,88,92,82,67,67,87,87,75,78,69,123", "endOffsets": "195,277,382,487,577,659,748,841,924,992,1060,1148,1236,1312,1391,1461,1585"}, "to": {"startLines": "9,10,12,13,14,18,19,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "835,930,1124,1229,1334,1756,1838,8147,8240,8323,8391,8459,8547,8635,8812,8891,8961", "endColumns": "94,81,104,104,89,81,88,92,82,67,67,87,87,75,78,69,123", "endOffsets": "925,1007,1224,1329,1419,1833,1922,8235,8318,8386,8454,8542,8630,8706,8886,8956,9080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\beb880474af4802d615c3712a7fe5cf4\\transformed\\browser-1.8.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,275,387", "endColumns": "111,107,111,111", "endOffsets": "162,270,382,494"}, "to": {"startLines": "11,15,16,17", "startColumns": "4,4,4,4", "startOffsets": "1012,1424,1532,1644", "endColumns": "111,107,111,111", "endOffsets": "1119,1527,1639,1751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8bf4b3fb7fecc46740c95cb34de6afa1\\transformed\\core-1.12.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "2,3,4,5,6,7,8,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,307,406,509,615,722,8711", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "198,302,401,504,610,717,830,8807"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88c123eabfc6d4b207c77cc8294f818f\\transformed\\material3-release\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,292,407,524,627,725,840,977,1094,1249,1334,1434,1526,1627,1747,1869,1974,2118,2253,2390,2562,2694,2820,2945,3073,3166,3266,3394,3536,3635,3737,3846,3986,4127,4237,4339,4417,4512,4609,4695,4781,4887,4967,5052,5160,5262,5366,5464,5552,5658,5764,5866,5988,6068,6175", "endColumns": "117,118,114,116,102,97,114,136,116,154,84,99,91,100,119,121,104,143,134,136,171,131,125,124,127,92,99,127,141,98,101,108,139,140,109,101,77,94,96,85,85,105,79,84,107,101,103,97,87,105,105,101,121,79,106,99", "endOffsets": "168,287,402,519,622,720,835,972,1089,1244,1329,1429,1521,1622,1742,1864,1969,2113,2248,2385,2557,2689,2815,2940,3068,3161,3261,3389,3531,3630,3732,3841,3981,4122,4232,4334,4412,4507,4604,4690,4776,4882,4962,5047,5155,5257,5361,5459,5547,5653,5759,5861,5983,6063,6170,6270"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1927,2045,2164,2279,2396,2499,2597,2712,2849,2966,3121,3206,3306,3398,3499,3619,3741,3846,3990,4125,4262,4434,4566,4692,4817,4945,5038,5138,5266,5408,5507,5609,5718,5858,5999,6109,6211,6289,6384,6481,6567,6653,6759,6839,6924,7032,7134,7238,7336,7424,7530,7636,7738,7860,7940,8047", "endColumns": "117,118,114,116,102,97,114,136,116,154,84,99,91,100,119,121,104,143,134,136,171,131,125,124,127,92,99,127,141,98,101,108,139,140,109,101,77,94,96,85,85,105,79,84,107,101,103,97,87,105,105,101,121,79,106,99", "endOffsets": "2040,2159,2274,2391,2494,2592,2707,2844,2961,3116,3201,3301,3393,3494,3614,3736,3841,3985,4120,4257,4429,4561,4687,4812,4940,5033,5133,5261,5403,5502,5604,5713,5853,5994,6104,6206,6284,6379,6476,6562,6648,6754,6834,6919,7027,7129,7233,7331,7419,7525,7631,7733,7855,7935,8042,8142"}}]}]}