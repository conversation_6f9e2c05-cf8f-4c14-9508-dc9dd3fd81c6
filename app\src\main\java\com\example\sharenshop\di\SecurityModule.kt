package com.example.sharenshop.di

import com.example.sharenshop.data.repository.SecurityRepositoryImpl
import com.example.sharenshop.data.security.DatabaseEncryptionService
import com.example.sharenshop.data.security.FileEncryptionService
import com.example.sharenshop.data.security.SharedPreferencesEncryptionService
import com.example.sharenshop.domain.repository.SecurityRepository
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class SecurityModule {

    @Binds
    @Singleton
    abstract fun bindSecurityRepository(securityRepositoryImpl: SecurityRepositoryImpl): SecurityRepository

    companion object {
        @Provides
        @Singleton
        fun provideDatabaseEncryptionService(): DatabaseEncryptionService {
            return DatabaseEncryptionService()
        }

        @Provides
        @Singleton
        fun provideFileEncryptionService(): FileEncryptionService {
            return FileEncryptionService()
        }

        @Provides
        @Singleton
        fun provideSharedPreferencesEncryptionService(): SharedPreferencesEncryptionService {
            return SharedPreferencesEncryptionService()
        }
    }
} 