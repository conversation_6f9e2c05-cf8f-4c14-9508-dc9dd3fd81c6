package com.example.sharenshop.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Contextual // ایمپورت اضافه شده
import kotlinx.serialization.Serializable
import java.math.BigDecimal

@Serializable
@Entity(tableName = "customers")
data class Customer(
    @PrimaryKey(autoGenerate = false)
    val id: String,
    val name: String,
    val phone: String? = null,
    val email: String? = null,
    val address: String? = null,
    @Contextual // انوتیشن اضافه شده
    val balance: BigDecimal = BigDecimal.ZERO, // Use BigDecimal for monetary values
    val createdAt: Long,
    val updatedAt: Long? = null
)