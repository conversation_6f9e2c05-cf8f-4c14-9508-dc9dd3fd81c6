package com.example.sharenshop.domain.use_case

import com.example.sharenshop.data.model.*
import com.example.sharenshop.domain.repository.CustomerApprovalRepository
import com.example.sharenshop.domain.repository.NotificationRepository
import com.example.sharenshop.domain.repository.UserRepository
import kotlinx.coroutines.flow.Flow
import java.util.*
import javax.inject.Inject

/**
 * Use Cases برای مدیریت درخواست‌های تایید مشتری
 */
data class CustomerApprovalUseCases @Inject constructor(
    val submitApprovalRequest: SubmitApprovalRequest,
    val approveCustomer: ApproveCustomer,
    val rejectCustomer: RejectCustomer,
    val getPendingRequests: GetPendingRequests,
    val getRequestsByReferrer: GetRequestsByReferrer,
    val validateReferrerCode: ValidateReferrerCode
)

/**
 * ارسال درخواست تایید مشتری
 */
class SubmitApprovalRequest @Inject constructor(
    private val customerApprovalRepository: CustomerApprovalRepository,
    private val userRepository: UserRepository,
    private val notificationRepository: NotificationRepository
) {
    suspend operator fun invoke(
        customerEmail: String,
        customerName: String,
        customerPhone: String,
        referrerCode: String
    ): Result<String> {
        return try {
            // بررسی وجود فروشنده با کد معرف
            val referrer = userRepository.getUserByUsername(referrerCode)
                ?: return Result.failure(Exception("فروشنده با این کد معرف یافت نشد"))
            
            // بررسی نقش فروشنده
            if (referrer.role != UserRole.CASHIER && referrer.role != UserRole.SUPER_ADMIN) {
                return Result.failure(Exception("کد معرف نامعتبر است"))
            }
            
            // بررسی عدم وجود درخواست قبلی
            val existingRequest = customerApprovalRepository.getRequestByCustomerEmail(customerEmail)
            if (existingRequest != null && existingRequest.status == ApprovalStatus.PENDING) {
                return Result.failure(Exception("درخواست شما قبلاً ارسال شده و در انتظار تایید است"))
            }
            
            // ایجاد درخواست جدید
            val requestId = UUID.randomUUID().toString()
            val request = CustomerApprovalRequest(
                id = requestId,
                customerEmail = customerEmail,
                customerName = customerName,
                customerPhone = customerPhone,
                referrerCode = referrerCode,
                referrerId = referrer.id,
                status = ApprovalStatus.PENDING,
                requestDate = System.currentTimeMillis()
            )
            
            // ذخیره درخواست
            customerApprovalRepository.insertRequest(request)
            
            // ارسال نوتیفیکیشن به فروشنده
            val notification = Notification(
                id = UUID.randomUUID().toString(),
                userId = referrer.id,
                type = NotificationType.CUSTOMER_APPROVAL_REQUEST,
                title = "درخواست تایید مشتری جدید",
                message = "مشتری $customerName درخواست تایید کرده است",
                data = requestId,
                createdAt = System.currentTimeMillis()
            )
            notificationRepository.insertNotification(notification)
            
            Result.success(requestId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

/**
 * تایید مشتری توسط فروشنده
 */
class ApproveCustomer @Inject constructor(
    private val customerApprovalRepository: CustomerApprovalRepository,
    private val userRepository: UserRepository,
    private val notificationRepository: NotificationRepository
) {
    suspend operator fun invoke(requestId: String, approverId: String): Result<Unit> {
        return try {
            val request = customerApprovalRepository.getRequestById(requestId)
                ?: return Result.failure(Exception("درخواست یافت نشد"))
            
            // بررسی مجوز تایید
            if (request.referrerId != approverId) {
                return Result.failure(Exception("شما مجوز تایید این درخواست را ندارید"))
            }
            
            // بروزرسانی وضعیت درخواست
            customerApprovalRepository.updateRequestStatus(
                requestId = requestId,
                status = ApprovalStatus.APPROVED,
                responseDate = System.currentTimeMillis()
            )
            
            // ایجاد کاربر مشتری
            val customerId = UUID.randomUUID().toString()
            val customer = User(
                id = customerId,
                email = request.customerEmail,
                username = request.customerEmail, // استفاده از ایمیل به عنوان نام کاربری
                userType = UserRole.CUSTOMER.value,
                createdAt = System.currentTimeMillis()
            )
            userRepository.insertUser(customer)
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

/**
 * رد درخواست مشتری
 */
class RejectCustomer @Inject constructor(
    private val customerApprovalRepository: CustomerApprovalRepository
) {
    suspend operator fun invoke(requestId: String, rejecterId: String, reason: String? = null): Result<Unit> {
        return try {
            val request = customerApprovalRepository.getRequestById(requestId)
                ?: return Result.failure(Exception("درخواست یافت نشد"))
            
            // بررسی مجوز رد
            if (request.referrerId != rejecterId) {
                return Result.failure(Exception("شما مجوز رد این درخواست را ندارید"))
            }
            
            // بروزرسانی وضعیت درخواست
            val updatedRequest = request.copy(
                status = ApprovalStatus.REJECTED,
                responseDate = System.currentTimeMillis(),
                notes = reason
            )
            customerApprovalRepository.updateRequest(updatedRequest)
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

/**
 * دریافت درخواست‌های در انتظار
 */
class GetPendingRequests @Inject constructor(
    private val customerApprovalRepository: CustomerApprovalRepository
) {
    operator fun invoke(): Flow<List<CustomerApprovalRequest>> {
        return customerApprovalRepository.getRequestsByStatus(ApprovalStatus.PENDING)
    }
}

/**
 * دریافت درخواست‌های یک فروشنده
 */
class GetRequestsByReferrer @Inject constructor(
    private val customerApprovalRepository: CustomerApprovalRepository
) {
    operator fun invoke(referrerId: String): Flow<List<CustomerApprovalRequest>> {
        return customerApprovalRepository.getRequestsByReferrer(referrerId)
    }
}

/**
 * اعتبارسنجی کد معرف
 */
class ValidateReferrerCode @Inject constructor(
    private val userRepository: UserRepository
) {
    suspend operator fun invoke(referrerCode: String): Result<User> {
        return try {
            val user = userRepository.getUserByUsername(referrerCode)
                ?: return Result.failure(Exception("کد معرف یافت نشد"))
            
            if (user.role != UserRole.CASHIER && user.role != UserRole.SUPER_ADMIN) {
                return Result.failure(Exception("کد معرف نامعتبر است"))
            }
            
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
