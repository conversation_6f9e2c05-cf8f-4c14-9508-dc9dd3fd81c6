// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.security;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SharedPreferencesEncryptionService_Factory implements Factory<SharedPreferencesEncryptionService> {
  @Override
  public SharedPreferencesEncryptionService get() {
    return newInstance();
  }

  public static SharedPreferencesEncryptionService_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SharedPreferencesEncryptionService newInstance() {
    return new SharedPreferencesEncryptionService();
  }

  private static final class InstanceHolder {
    private static final SharedPreferencesEncryptionService_Factory INSTANCE = new SharedPreferencesEncryptionService_Factory();
  }
}
