{"logs": [{"outputFile": "com.example.sharenshop.app-mergeDebugResources-2:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8bf4b3fb7fecc46740c95cb34de6afa1\\transformed\\core-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,199,301,398,497,605,711,8436", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "194,296,393,492,600,706,826,8532"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\beb880474af4802d615c3712a7fe5cf4\\transformed\\browser-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "11,15,16,17", "startColumns": "4,4,4,4", "startOffsets": "1005,1400,1501,1613", "endColumns": "109,100,111,96", "endOffsets": "1110,1496,1608,1705"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88b5b0acc1ef090d93ba86ccb1fae5cf\\transformed\\foundation-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,87", "endOffsets": "140,228"}, "to": {"startLines": "87,88", "startColumns": "4,4", "startOffsets": "8801,8891", "endColumns": "89,87", "endOffsets": "8886,8974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\61acd70f2f91261ed7f816e4fc4161c1\\transformed\\ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,963,1027,1107,1189,1259,1336,1403", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,958,1022,1102,1184,1254,1331,1398,1518"}, "to": {"startLines": "9,10,12,13,14,18,19,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "831,924,1115,1212,1312,1710,1786,7905,7994,8076,8140,8204,8284,8366,8537,8614,8681", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "919,1000,1207,1307,1395,1781,1869,7989,8071,8135,8199,8279,8361,8431,8609,8676,8796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88c123eabfc6d4b207c77cc8294f818f\\transformed\\material3-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,394,507,606,700,811,955,1077,1227,1311,1411,1500,1594,1701,1819,1924,2051,2173,2306,2473,2600,2716,2837,2958,3048,3146,3265,3396,3497,3607,3710,3844,3985,4090,4188,4268,4362,4453,4537,4621,4732,4812,4896,4997,5096,5187,5287,5375,5480,5582,5687,5804,5884,5987", "endColumns": "116,114,106,112,98,93,110,143,121,149,83,99,88,93,106,117,104,126,121,132,166,126,115,120,120,89,97,118,130,100,109,102,133,140,104,97,79,93,90,83,83,110,79,83,100,98,90,99,87,104,101,104,116,79,102,98", "endOffsets": "167,282,389,502,601,695,806,950,1072,1222,1306,1406,1495,1589,1696,1814,1919,2046,2168,2301,2468,2595,2711,2832,2953,3043,3141,3260,3391,3492,3602,3705,3839,3980,4085,4183,4263,4357,4448,4532,4616,4727,4807,4891,4992,5091,5182,5282,5370,5475,5577,5682,5799,5879,5982,6081"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1874,1991,2106,2213,2326,2425,2519,2630,2774,2896,3046,3130,3230,3319,3413,3520,3638,3743,3870,3992,4125,4292,4419,4535,4656,4777,4867,4965,5084,5215,5316,5426,5529,5663,5804,5909,6007,6087,6181,6272,6356,6440,6551,6631,6715,6816,6915,7006,7106,7194,7299,7401,7506,7623,7703,7806", "endColumns": "116,114,106,112,98,93,110,143,121,149,83,99,88,93,106,117,104,126,121,132,166,126,115,120,120,89,97,118,130,100,109,102,133,140,104,97,79,93,90,83,83,110,79,83,100,98,90,99,87,104,101,104,116,79,102,98", "endOffsets": "1986,2101,2208,2321,2420,2514,2625,2769,2891,3041,3125,3225,3314,3408,3515,3633,3738,3865,3987,4120,4287,4414,4530,4651,4772,4862,4960,5079,5210,5311,5421,5524,5658,5799,5904,6002,6082,6176,6267,6351,6435,6546,6626,6710,6811,6910,7001,7101,7189,7294,7396,7501,7618,7698,7801,7900"}}]}]}