// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.domain.repository.InvoiceRepository;
import com.example.sharenshop.domain.use_case.InvoiceUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UseCaseModule_ProvideInvoiceUseCasesFactory implements Factory<InvoiceUseCases> {
  private final Provider<InvoiceRepository> repositoryProvider;

  public UseCaseModule_ProvideInvoiceUseCasesFactory(
      Provider<InvoiceRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public InvoiceUseCases get() {
    return provideInvoiceUseCases(repositoryProvider.get());
  }

  public static UseCaseModule_ProvideInvoiceUseCasesFactory create(
      Provider<InvoiceRepository> repositoryProvider) {
    return new UseCaseModule_ProvideInvoiceUseCasesFactory(repositoryProvider);
  }

  public static InvoiceUseCases provideInvoiceUseCases(InvoiceRepository repository) {
    return Preconditions.checkNotNullFromProvides(UseCaseModule.INSTANCE.provideInvoiceUseCases(repository));
  }
}
