package com.example.sharenshop.di

import com.example.sharenshop.data.repository.CustomerRepositoryImpl
import com.example.sharenshop.data.repository.InvoiceRepositoryImpl
import com.example.sharenshop.data.repository.NotificationRepositoryImpl
import com.example.sharenshop.data.repository.ProductRepositoryImpl
import com.example.sharenshop.data.repository.SellerRepositoryImpl
import com.example.sharenshop.data.repository.SettingsRepositoryImpl
import com.example.sharenshop.data.repository.StatisticsRepositoryImpl
import com.example.sharenshop.data.repository.UserRepositoryImpl
import com.example.sharenshop.domain.repository.CustomerRepository
import com.example.sharenshop.domain.repository.InvoiceRepository
import com.example.sharenshop.domain.repository.NotificationRepository
import com.example.sharenshop.domain.repository.ProductRepository
import com.example.sharenshop.domain.repository.SellerRepository
import com.example.sharenshop.domain.repository.SettingsRepository
import com.example.sharenshop.domain.repository.StatisticsRepository
import com.example.sharenshop.domain.repository.UserRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {

    @Binds
    @Singleton
    abstract fun bindUserRepository(userRepositoryImpl: UserRepositoryImpl): UserRepository

    @Binds
    @Singleton
    abstract fun bindProductRepository(productRepositoryImpl: ProductRepositoryImpl): ProductRepository

    @Binds
    @Singleton
    abstract fun bindCustomerRepository(customerRepositoryImpl: CustomerRepositoryImpl): CustomerRepository

    @Binds
    @Singleton
    abstract fun bindSellerRepository(sellerRepositoryImpl: SellerRepositoryImpl): SellerRepository

    @Binds
    @Singleton
    abstract fun bindInvoiceRepository(invoiceRepositoryImpl: InvoiceRepositoryImpl): InvoiceRepository

    @Binds
    @Singleton
    abstract fun bindSettingsRepository(settingsRepositoryImpl: SettingsRepositoryImpl): SettingsRepository

    @Binds
    @Singleton
    abstract fun bindStatisticsRepository(statisticsRepositoryImpl: StatisticsRepositoryImpl): StatisticsRepository

    @Binds
    @Singleton
    abstract fun bindNotificationRepository(notificationRepositoryImpl: NotificationRepositoryImpl): NotificationRepository
}