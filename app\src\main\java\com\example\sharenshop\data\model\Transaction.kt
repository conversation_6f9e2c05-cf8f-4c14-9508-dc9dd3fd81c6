package com.example.sharenshop.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import java.math.BigDecimal

/**
 * مدل تراکنش مالی - برای ردیابی کامل جریان نقدینگی
 */
@Serializable
@Entity(tableName = "transactions")
data class Transaction(
    @PrimaryKey(autoGenerate = false)
    val id: String,
    val referenceId: String, // شناسه مرجع (فاکتور، پرداخت، تصویه)
    val referenceType: TransactionType, // نوع مرجع
    val fromAccountId: String? = null, // حساب مبدا
    val toAccountId: String? = null, // حساب مقصد
    @Contextual
    val amount: BigDecimal, // مبلغ تراکنش
    val transactionCategory: TransactionCategory, // دسته‌بندی تراکنش
    val description: String, // شرح تراکنش
    val transactionDate: Long, // تاریخ تراکنش
    val createdBy: String, // ایجاد کننده
    val createdAt: Long
)

/**
 * نوع تراکنش
 */
@Serializable
enum class TransactionType(val value: String, val displayName: String) {
    INVOICE("invoice", "فاکتور فروش"),
    PAYMENT("payment", "دریافت پرداخت"),
    SETTLEMENT("settlement", "تصویه حساب"),
    ADJUSTMENT("adjustment", "تعدیل حساب");
    
    companion object {
        fun fromString(value: String): TransactionType {
            return values().find { it.value == value } ?: INVOICE
        }
    }
}

/**
 * دسته‌بندی تراکنش
 */
@Serializable
enum class TransactionCategory(val value: String, val displayName: String) {
    SALES_REVENUE("sales_revenue", "درآمد فروش"),
    CASH_RECEIPT("cash_receipt", "دریافت نقدی"),
    CREDIT_RECEIPT("credit_receipt", "دریافت نسیه"),
    SETTLEMENT_OUT("settlement_out", "پرداخت تصویه"),
    SETTLEMENT_IN("settlement_in", "دریافت تصویه"),
    COMMISSION("commission", "کمیسیون"),
    DISCOUNT("discount", "تخفیف"),
    ADJUSTMENT("adjustment", "تعدیل");
    
    companion object {
        fun fromString(value: String): TransactionCategory {
            return values().find { it.value == value } ?: SALES_REVENUE
        }
    }
}

/**
 * حساب مالی (برای ردیابی موجودی)
 */
@Serializable
@Entity(tableName = "accounts")
data class Account(
    @PrimaryKey(autoGenerate = false)
    val id: String,
    val ownerId: String, // صاحب حساب (مشتری، فروشنده، مدیر)
    val ownerType: AccountOwnerType, // نوع صاحب حساب
    val accountType: AccountType, // نوع حساب
    @Contextual
    val balance: BigDecimal, // موجودی فعلی
    @Contextual
    val creditLimit: BigDecimal = BigDecimal.ZERO, // حد اعتبار
    val isActive: Boolean = true,
    val createdAt: Long,
    val updatedAt: Long? = null
)

/**
 * نوع صاحب حساب
 */
@Serializable
enum class AccountOwnerType(val value: String, val displayName: String) {
    CUSTOMER("customer", "مشتری"),
    SELLER("seller", "فروشنده"),
    MANAGER("manager", "مدیر"),
    COMPANY("company", "شرکت");
    
    companion object {
        fun fromString(value: String): AccountOwnerType {
            return values().find { it.value == value } ?: CUSTOMER
        }
    }
}

/**
 * نوع حساب
 */
@Serializable
enum class AccountType(val value: String, val displayName: String) {
    RECEIVABLE("receivable", "حساب دریافتنی"), // بدهی مشتری
    PAYABLE("payable", "حساب پرداختنی"), // بدهی به فروشنده
    CASH("cash", "حساب نقدی"),
    COMMISSION("commission", "حساب کمیسیون");
    
    companion object {
        fun fromString(value: String): AccountType {
            return values().find { it.value == value } ?: RECEIVABLE
        }
    }
}

/**
 * گزارش مالی
 */
data class FinancialReport(
    @Contextual
    val totalSales: BigDecimal, // کل فروش
    @Contextual
    val totalCashReceived: BigDecimal, // کل دریافت نقدی
    @Contextual
    val totalCreditReceived: BigDecimal, // کل دریافت نسیه
    @Contextual
    val totalOutstandingDebt: BigDecimal, // کل بدهی معوق
    @Contextual
    val totalSettlementPending: BigDecimal, // کل تصویه در انتظار
    @Contextual
    val totalCommissionOwed: BigDecimal, // کل کمیسیون بدهکار
    val reportDate: Long
)
