// Generated by Dagger (https://dagger.dev).
package com.example.sharenshop.data.repository;

import com.example.sharenshop.data.local.dao.CustomerDao;
import com.example.sharenshop.data.local.dao.InvoiceDao;
import com.example.sharenshop.data.local.dao.InvoiceItemDao;
import com.example.sharenshop.data.local.dao.ProductDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class StatisticsRepositoryImpl_Factory implements Factory<StatisticsRepositoryImpl> {
  private final Provider<InvoiceDao> invoiceDaoProvider;

  private final Provider<InvoiceItemDao> invoiceItemDaoProvider;

  private final Provider<ProductDao> productDaoProvider;

  private final Provider<CustomerDao> customerDaoProvider;

  public StatisticsRepositoryImpl_Factory(Provider<InvoiceDao> invoiceDaoProvider,
      Provider<InvoiceItemDao> invoiceItemDaoProvider, Provider<ProductDao> productDaoProvider,
      Provider<CustomerDao> customerDaoProvider) {
    this.invoiceDaoProvider = invoiceDaoProvider;
    this.invoiceItemDaoProvider = invoiceItemDaoProvider;
    this.productDaoProvider = productDaoProvider;
    this.customerDaoProvider = customerDaoProvider;
  }

  @Override
  public StatisticsRepositoryImpl get() {
    return newInstance(invoiceDaoProvider.get(), invoiceItemDaoProvider.get(), productDaoProvider.get(), customerDaoProvider.get());
  }

  public static StatisticsRepositoryImpl_Factory create(Provider<InvoiceDao> invoiceDaoProvider,
      Provider<InvoiceItemDao> invoiceItemDaoProvider, Provider<ProductDao> productDaoProvider,
      Provider<CustomerDao> customerDaoProvider) {
    return new StatisticsRepositoryImpl_Factory(invoiceDaoProvider, invoiceItemDaoProvider, productDaoProvider, customerDaoProvider);
  }

  public static StatisticsRepositoryImpl newInstance(InvoiceDao invoiceDao,
      InvoiceItemDao invoiceItemDao, ProductDao productDao, CustomerDao customerDao) {
    return new StatisticsRepositoryImpl(invoiceDao, invoiceItemDao, productDao, customerDao);
  }
}
