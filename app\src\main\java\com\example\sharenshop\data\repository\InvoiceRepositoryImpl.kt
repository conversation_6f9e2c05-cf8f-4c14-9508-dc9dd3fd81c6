package com.example.sharenshop.data.repository

import com.example.sharenshop.data.local.dao.InvoiceDao
import com.example.sharenshop.data.local.dao.InvoiceItemDao
import com.example.sharenshop.data.model.Invoice
import com.example.sharenshop.data.model.InvoiceItem
// import com.example.sharenshop.data.remote.SupabaseConstants
// import com.example.sharenshop.data.remote.SupabaseManager
import com.example.sharenshop.domain.repository.InvoiceRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class InvoiceRepositoryImpl @Inject constructor(
    private val invoiceDao: InvoiceDao,
    private val invoiceItemDao: InvoiceItemDao,
//    private val supabaseManager: SupabaseManager
) : InvoiceRepository {

    override fun getInvoiceById(invoiceId: String): Flow<Invoice?> = flow {
        invoiceDao.getInvoiceById(invoiceId).collect { localInvoice ->
            if (localInvoice != null) {
                emit(localInvoice)
            }
            try {
                // val response = supabaseManager.postgrest
                //     .from(SupabaseConstants.INVOICES_TABLE)
                //     .select()
                //     .eq("id", invoiceId)
                //     .limit(1)
                //     .single()
                //     .execute()
                // TODO: Deserialize response.data to Invoice object
                val remoteInvoice: Invoice? = null // Placeholder for deserialized invoice

                if (remoteInvoice != null) {
                    invoiceDao.insertInvoice(remoteInvoice)
                    emit(remoteInvoice)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override suspend fun insertInvoice(invoice: Invoice) {
        invoiceDao.insertInvoice(invoice)
        try {
            // val supabaseManager.postgrest
            //     .from(SupabaseConstants.INVOICES_TABLE)
            //     .insert(invoice)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override suspend fun updateInvoice(invoice: Invoice) {
        invoiceDao.updateInvoice(invoice)
        try {
            // val supabaseManager.postgrest
            //     .from(SupabaseConstants.INVOICES_TABLE)
            //     .update(invoice)
            //     .eq("id", invoice.id)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override suspend fun deleteInvoiceById(invoiceId: String) {
        invoiceDao.deleteInvoiceById(invoiceId)
        try {
            // val supabaseManager.postgrest
            //     .from(SupabaseConstants.INVOICES_TABLE)
            //     .delete()
            //     .eq("id", invoiceId)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun getAllInvoices(): Flow<List<Invoice>> = flow {
        invoiceDao.getAllInvoices().collect { emit(it) }
    }

    override fun getInvoicesByCustomerId(customerId: String): Flow<List<Invoice>> = flow {
        invoiceDao.getInvoicesByCustomerId(customerId).collect { emit(it) }
    }

    override fun getInvoicesByUserId(userId: String): Flow<List<Invoice>> = flow {
        invoiceDao.getInvoicesByUserId(userId).collect { emit(it) }
    }

    // You might also need methods to manage InvoiceItems related to an Invoice
    fun getInvoiceItemsByInvoiceId(invoiceId: String): Flow<List<InvoiceItem>> = flow {
        invoiceItemDao.getInvoiceItemsByInvoiceId(invoiceId).collect { emit(it) }
        // TODO: Sync with remote if needed
    }

    suspend fun insertInvoiceItem(invoiceItem: InvoiceItem) {
        invoiceItemDao.insertInvoiceItem(invoiceItem)
        try {
            // val supabaseManager.postgrest
            //     .from(SupabaseConstants.INVOICE_ITEMS_TABLE)
            //     .insert(invoiceItem)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    suspend fun insertAllInvoiceItems(invoiceItems: List<InvoiceItem>) {
        invoiceItemDao.insertAllInvoiceItems(invoiceItems)
        try {
            // val supabaseManager.postgrest
            //     .from(SupabaseConstants.INVOICE_ITEMS_TABLE)
            //     .insert(invoiceItems)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    suspend fun updateInvoiceItem(invoiceItem: InvoiceItem) {
        invoiceItemDao.updateInvoiceItem(invoiceItem)
        try {
            // val supabaseManager.postgrest
            //     .from(SupabaseConstants.INVOICE_ITEMS_TABLE)
            //     .update(invoiceItem)
            //     .eq("id", invoiceItem.id)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    suspend fun deleteInvoiceItemById(invoiceItemId: String) {
        invoiceItemDao.deleteInvoiceItemById(invoiceItemId)
        try {
            // val supabaseManager.postgrest
            //     .from(SupabaseConstants.INVOICE_ITEMS_TABLE)
            //     .delete()
            //     .eq("id", invoiceItemId)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

} 