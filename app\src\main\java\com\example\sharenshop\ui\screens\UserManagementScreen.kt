package com.example.sharenshop.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
// import androidx.navigation.NavHostController // ایمپورت حذف شد
import androidx.compose.ui.tooling.preview.Preview // ایمپورت اضافه شد برای Preview

@Composable
fun UserManagementScreen() { // پارامتر navController حذف شد
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(text = "User Management Screen")
        // TODO: Implement user listing, add/edit user forms with role management
    }
}

@Preview(showBackground = true) // از کامنت خارج شد
@Composable
fun PreviewUserManagementScreen() { // از کامنت خارج شد
    UserManagementScreen() // فراخوانی بدون navController و از کامنت خارج شد
}