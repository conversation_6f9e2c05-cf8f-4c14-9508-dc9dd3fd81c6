package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.sharenshop.SharenShopApplication",
    rootPackage = "com.example.sharenshop",
    originatingRoot = "com.example.sharenshop.SharenShopApplication",
    originatingRootPackage = "com.example.sharenshop",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "SharenShopApplication",
    originatingRootSimpleNames = "SharenShopApplication"
)
public class _com_example_sharenshop_SharenShopApplication {
}
