package com.example.sharenshop.domain.use_case

import com.example.sharenshop.data.model.Product
import com.example.sharenshop.domain.repository.ProductRepository
import kotlinx.coroutines.flow.Flow

// Grouping all product-related use cases
data class ProductUseCases(
    val getProduct: GetProduct,
    val insertProduct: InsertProduct,
    val updateProduct: UpdateProduct,
    val deleteProduct: DeleteProduct,
    val getAllProducts: GetAllProducts,
    val searchProducts: SearchProducts
)

class GetProduct(private val repository: ProductRepository) {
    operator fun invoke(productId: String): Flow<Product?> {
        return repository.getProductById(productId)
    }
}

class InsertProduct(private val repository: ProductRepository) {
    suspend operator fun invoke(product: Product) {
        repository.insertProduct(product)
    }
}

class UpdateProduct(private val repository: ProductRepository) {
    suspend operator fun invoke(product: Product) {
        repository.updateProduct(product)
    }
}

class DeleteProduct(private val repository: ProductRepository) {
    suspend operator fun invoke(productId: String) {
        repository.deleteProductById(productId)
    }
}

class GetAllProducts(private val repository: ProductRepository) {
    operator fun invoke(): Flow<List<Product>> {
        return repository.getAllProducts()
    }
}

class SearchProducts(private val repository: ProductRepository) {
    operator fun invoke(query: String): Flow<List<Product>> {
        return repository.searchProducts(query)
    }
} 