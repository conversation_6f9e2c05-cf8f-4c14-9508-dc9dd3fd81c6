package com.example.sharenshop.data.model;

/**
 * خلاصه تصویه حساب فروشنده
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\t\n\u0002\b\u001e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BI\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\b\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0006\u0012\u0006\u0010\n\u001a\u00020\u0006\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\u0002\u0010\rJ\t\u0010 \u001a\u00020\u0003H\u00c6\u0003J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0006H\u00c6\u0003J\t\u0010#\u001a\u00020\u0006H\u00c6\u0003J\t\u0010$\u001a\u00020\u0006H\u00c6\u0003J\t\u0010%\u001a\u00020\u0006H\u00c6\u0003J\t\u0010&\u001a\u00020\u0006H\u00c6\u0003J\u0010\u0010\'\u001a\u0004\u0018\u00010\fH\u00c6\u0003\u00a2\u0006\u0002\u0010\u000fJ`\u0010(\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\u00062\b\b\u0002\u0010\t\u001a\u00020\u00062\b\b\u0002\u0010\n\u001a\u00020\u00062\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00c6\u0001\u00a2\u0006\u0002\u0010)J\u0013\u0010*\u001a\u00020+2\b\u0010,\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010-\u001a\u00020.H\u00d6\u0001J\t\u0010/\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\n\n\u0002\u0010\u0010\u001a\u0004\b\u000e\u0010\u000fR\u001c\u0010\t\u001a\u00020\u00068\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0011\u0010\u0012\u001a\u0004\b\u0013\u0010\u0014R\u001c\u0010\n\u001a\u00020\u00068\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0015\u0010\u0012\u001a\u0004\b\u0016\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0018R\u001c\u0010\u0005\u001a\u00020\u00068\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001a\u0010\u0012\u001a\u0004\b\u001b\u0010\u0014R\u001c\u0010\u0007\u001a\u00020\u00068\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001c\u0010\u0012\u001a\u0004\b\u001d\u0010\u0014R\u001c\u0010\b\u001a\u00020\u00068\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u001e\u0010\u0012\u001a\u0004\b\u001f\u0010\u0014\u00a8\u00060"}, d2 = {"Lcom/example/sharenshop/data/model/SellerSettlementSummary;", "", "sellerId", "", "sellerName", "totalCashReceived", "Ljava/math/BigDecimal;", "totalCreditCollected", "totalSettled", "pendingSettlement", "remainingBalance", "lastSettlementDate", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/lang/Long;)V", "getLastSettlementDate", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getPendingSettlement$annotations", "()V", "getPendingSettlement", "()Ljava/math/BigDecimal;", "getRemainingBalance$annotations", "getRemainingBalance", "getSellerId", "()Ljava/lang/String;", "getSellerName", "getTotalCashReceived$annotations", "getTotalCashReceived", "getTotalCreditCollected$annotations", "getTotalCreditCollected", "getTotalSettled$annotations", "getTotalSettled", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/lang/Long;)Lcom/example/sharenshop/data/model/SellerSettlementSummary;", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class SellerSettlementSummary {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String sellerId = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String sellerName = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal totalCashReceived = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal totalCreditCollected = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal totalSettled = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal pendingSettlement = null;
    @org.jetbrains.annotations.NotNull()
    private final java.math.BigDecimal remainingBalance = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long lastSettlementDate = null;
    
    public SellerSettlementSummary(@org.jetbrains.annotations.NotNull()
    java.lang.String sellerId, @org.jetbrains.annotations.NotNull()
    java.lang.String sellerName, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalCashReceived, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalCreditCollected, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalSettled, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal pendingSettlement, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal remainingBalance, @org.jetbrains.annotations.Nullable()
    java.lang.Long lastSettlementDate) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSellerId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSellerName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getTotalCashReceived() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getTotalCashReceived$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getTotalCreditCollected() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getTotalCreditCollected$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getTotalSettled() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getTotalSettled$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getPendingSettlement() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getPendingSettlement$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal getRemainingBalance() {
        return null;
    }
    
    @kotlinx.serialization.Contextual()
    @java.lang.Deprecated()
    public static void getRemainingBalance$annotations() {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getLastSettlementDate() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.math.BigDecimal component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.data.model.SellerSettlementSummary copy(@org.jetbrains.annotations.NotNull()
    java.lang.String sellerId, @org.jetbrains.annotations.NotNull()
    java.lang.String sellerName, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalCashReceived, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalCreditCollected, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal totalSettled, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal pendingSettlement, @org.jetbrains.annotations.NotNull()
    java.math.BigDecimal remainingBalance, @org.jetbrains.annotations.Nullable()
    java.lang.Long lastSettlementDate) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}