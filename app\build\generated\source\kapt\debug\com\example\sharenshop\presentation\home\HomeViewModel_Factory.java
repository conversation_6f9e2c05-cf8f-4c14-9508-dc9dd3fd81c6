// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.presentation.home;

import com.example.sharenshop.domain.use_case.StatisticsUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HomeViewModel_Factory implements Factory<HomeViewModel> {
  private final Provider<StatisticsUseCases> statisticsUseCasesProvider;

  public HomeViewModel_Factory(Provider<StatisticsUseCases> statisticsUseCasesProvider) {
    this.statisticsUseCasesProvider = statisticsUseCasesProvider;
  }

  @Override
  public HomeViewModel get() {
    return newInstance(statisticsUseCasesProvider.get());
  }

  public static HomeViewModel_Factory create(
      Provider<StatisticsUseCases> statisticsUseCasesProvider) {
    return new HomeViewModel_Factory(statisticsUseCasesProvider);
  }

  public static HomeViewModel newInstance(StatisticsUseCases statisticsUseCases) {
    return new HomeViewModel(statisticsUseCases);
  }
}
