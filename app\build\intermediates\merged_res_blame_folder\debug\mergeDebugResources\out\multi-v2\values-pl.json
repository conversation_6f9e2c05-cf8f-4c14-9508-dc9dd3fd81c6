{"logs": [{"outputFile": "com.example.sharenshop.app-mergeDebugResources-2:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8bf4b3fb7fecc46740c95cb34de6afa1\\transformed\\core-1.12.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,402,501,615,720,8604", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "197,299,397,496,610,715,837,8700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\61acd70f2f91261ed7f816e4fc4161c1\\transformed\\ui-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,394,499,576,653,746,836,919,990,1060,1143,1230,1302,1384,1452", "endColumns": "94,84,108,104,76,76,92,89,82,70,69,82,86,71,81,67,119", "endOffsets": "195,280,389,494,571,648,741,831,914,985,1055,1138,1225,1297,1379,1447,1567"}, "to": {"startLines": "9,10,12,13,14,18,19,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,937,1122,1231,1336,1731,1808,8048,8138,8221,8292,8362,8445,8532,8705,8787,8855", "endColumns": "94,84,108,104,76,76,92,89,82,70,69,82,86,71,81,67,119", "endOffsets": "932,1017,1226,1331,1408,1803,1896,8133,8216,8287,8357,8440,8527,8599,8782,8850,8970"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\beb880474af4802d615c3712a7fe5cf4\\transformed\\browser-1.8.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "11,15,16,17", "startColumns": "4,4,4,4", "startOffsets": "1022,1413,1512,1627", "endColumns": "99,98,114,103", "endOffsets": "1117,1507,1622,1726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88c123eabfc6d4b207c77cc8294f818f\\transformed\\material3-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,409,524,624,723,839,977,1099,1241,1325,1424,1516,1612,1729,1853,1957,2097,2233,2377,2538,2670,2791,2916,3037,3130,3230,3350,3474,3573,3677,3783,3924,4071,4182,4281,4355,4450,4546,4633,4720,4832,4912,4999,5094,5199,5290,5399,5487,5593,5694,5804,5922,6002,6105", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "165,282,404,519,619,718,834,972,1094,1236,1320,1419,1511,1607,1724,1848,1952,2092,2228,2372,2533,2665,2786,2911,3032,3125,3225,3345,3469,3568,3672,3778,3919,4066,4177,4276,4350,4445,4541,4628,4715,4827,4907,4994,5089,5194,5285,5394,5482,5588,5689,5799,5917,5997,6100,6197"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1901,2016,2133,2255,2370,2470,2569,2685,2823,2945,3087,3171,3270,3362,3458,3575,3699,3803,3943,4079,4223,4384,4516,4637,4762,4883,4976,5076,5196,5320,5419,5523,5629,5770,5917,6028,6127,6201,6296,6392,6479,6566,6678,6758,6845,6940,7045,7136,7245,7333,7439,7540,7650,7768,7848,7951", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "2011,2128,2250,2365,2465,2564,2680,2818,2940,3082,3166,3265,3357,3453,3570,3694,3798,3938,4074,4218,4379,4511,4632,4757,4878,4971,5071,5191,5315,5414,5518,5624,5765,5912,6023,6122,6196,6291,6387,6474,6561,6673,6753,6840,6935,7040,7131,7240,7328,7434,7535,7645,7763,7843,7946,8043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88b5b0acc1ef090d93ba86ccb1fae5cf\\transformed\\foundation-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,87", "endOffsets": "138,226"}, "to": {"startLines": "87,88", "startColumns": "4,4", "startOffsets": "8975,9063", "endColumns": "87,87", "endOffsets": "9058,9146"}}]}]}