// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.security.FileEncryptionService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SecurityModule_Companion_ProvideFileEncryptionServiceFactory implements Factory<FileEncryptionService> {
  @Override
  public FileEncryptionService get() {
    return provideFileEncryptionService();
  }

  public static SecurityModule_Companion_ProvideFileEncryptionServiceFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static FileEncryptionService provideFileEncryptionService() {
    return Preconditions.checkNotNullFromProvides(SecurityModule.Companion.provideFileEncryptionService());
  }

  private static final class InstanceHolder {
    private static final SecurityModule_Companion_ProvideFileEncryptionServiceFactory INSTANCE = new SecurityModule_Companion_ProvideFileEncryptionServiceFactory();
  }
}
