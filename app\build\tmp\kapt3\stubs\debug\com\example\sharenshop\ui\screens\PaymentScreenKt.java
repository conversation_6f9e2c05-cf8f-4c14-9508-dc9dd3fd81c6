package com.example.sharenshop.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000 \n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0002\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0003\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0004\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0005\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0006\u001a\u00020\u0001H\u0007\u001a\u0010\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\tH\u0007\u001a\u0012\u0010\n\u001a\u00020\u00012\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u001a\u0010\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000fH\u0007\u00a8\u0006\u0010"}, d2 = {"AdminAllPaymentsTab", "", "AdminPaymentStatsTab", "CashierPaymentHistoryTab", "CashierPendingPaymentsTab", "CustomerNewPaymentTab", "CustomerPaymentHistoryTab", "PaymentHistoryCard", "paymentDetail", "Lcom/example/sharenshop/data/model/PaymentDetails;", "PaymentScreen", "userRole", "Lcom/example/sharenshop/data/model/UserRole;", "StatusChip", "status", "Lcom/example/sharenshop/data/model/PaymentStatus;", "app_debug"})
public final class PaymentScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void PaymentScreen(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CustomerNewPaymentTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CustomerPaymentHistoryTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PaymentHistoryCard(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.PaymentDetails paymentDetail) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void StatusChip(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.PaymentStatus status) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CashierPendingPaymentsTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CashierPaymentHistoryTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AdminAllPaymentsTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AdminPaymentStatsTab() {
    }
}