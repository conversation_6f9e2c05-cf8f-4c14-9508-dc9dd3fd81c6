package com.example.sharenshop.domain.use_case;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B5\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\tH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u000bH\u00c6\u0003J\t\u0010 \u001a\u00020\rH\u00c6\u0003JE\u0010!\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u00c6\u0001J\u0013\u0010\"\u001a\u00020#2\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020&H\u00d6\u0001J\t\u0010\'\u001a\u00020(H\u00d6\u0001R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001a\u00a8\u0006)"}, d2 = {"Lcom/example/sharenshop/domain/use_case/SellerUseCases;", "", "getSeller", "Lcom/example/sharenshop/domain/use_case/GetSeller;", "insertSeller", "Lcom/example/sharenshop/domain/use_case/InsertSeller;", "updateSeller", "Lcom/example/sharenshop/domain/use_case/UpdateSeller;", "deleteSeller", "Lcom/example/sharenshop/domain/use_case/DeleteSeller;", "getAllSellers", "Lcom/example/sharenshop/domain/use_case/GetAllSellers;", "searchSellers", "Lcom/example/sharenshop/domain/use_case/SearchSellers;", "(Lcom/example/sharenshop/domain/use_case/GetSeller;Lcom/example/sharenshop/domain/use_case/InsertSeller;Lcom/example/sharenshop/domain/use_case/UpdateSeller;Lcom/example/sharenshop/domain/use_case/DeleteSeller;Lcom/example/sharenshop/domain/use_case/GetAllSellers;Lcom/example/sharenshop/domain/use_case/SearchSellers;)V", "getDeleteSeller", "()Lcom/example/sharenshop/domain/use_case/DeleteSeller;", "getGetAllSellers", "()Lcom/example/sharenshop/domain/use_case/GetAllSellers;", "getGetSeller", "()Lcom/example/sharenshop/domain/use_case/GetSeller;", "getInsertSeller", "()Lcom/example/sharenshop/domain/use_case/InsertSeller;", "getSearchSellers", "()Lcom/example/sharenshop/domain/use_case/SearchSellers;", "getUpdateSeller", "()Lcom/example/sharenshop/domain/use_case/UpdateSeller;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class SellerUseCases {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.GetSeller getSeller = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.InsertSeller insertSeller = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.UpdateSeller updateSeller = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.DeleteSeller deleteSeller = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.GetAllSellers getAllSellers = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.SearchSellers searchSellers = null;
    
    public SellerUseCases(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetSeller getSeller, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.InsertSeller insertSeller, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.UpdateSeller updateSeller, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.DeleteSeller deleteSeller, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetAllSellers getAllSellers, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.SearchSellers searchSellers) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetSeller getGetSeller() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.InsertSeller getInsertSeller() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.UpdateSeller getUpdateSeller() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.DeleteSeller getDeleteSeller() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetAllSellers getGetAllSellers() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.SearchSellers getSearchSellers() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetSeller component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.InsertSeller component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.UpdateSeller component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.DeleteSeller component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetAllSellers component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.SearchSellers component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.SellerUseCases copy(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetSeller getSeller, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.InsertSeller insertSeller, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.UpdateSeller updateSeller, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.DeleteSeller deleteSeller, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetAllSellers getAllSellers, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.SearchSellers searchSellers) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}