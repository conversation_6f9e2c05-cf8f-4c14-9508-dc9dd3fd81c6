1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.sharenshop"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:5:5-67
11-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:6:5-79
12-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:6:22-76
13
14    <permission
14-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.example.sharenshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.example.sharenshop.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:8:5-29:19
21        android:name="com.example.sharenshop.SharenShopApplication"
21-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:9:9-46
22        android:allowBackup="true"
22-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:10:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bf4b3fb7fecc46740c95cb34de6afa1\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
24        android:dataExtractionRules="@xml/data_extraction_rules"
24-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:11:9-65
25        android:debuggable="true"
26        android:extractNativeLibs="false"
27        android:fullBackupContent="@xml/backup_rules"
27-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:12:9-54
28        android:icon="@mipmap/ic_launcher"
28-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:13:9-43
29        android:label="@string/app_name"
29-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:14:9-41
30        android:roundIcon="@mipmap/ic_launcher_round"
30-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:15:9-54
31        android:supportsRtl="true"
31-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:16:9-35
32        android:testOnly="true"
33        android:theme="@style/Theme.SharenShop" >
33-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:17:9-48
34        <activity
34-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:19:9-28:20
35            android:name="com.example.sharenshop.MainActivity"
35-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:20:13-41
36            android:exported="true"
36-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:21:13-36
37            android:theme="@style/Theme.SharenShop" >
37-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:22:13-52
38            <intent-filter>
38-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:23:13-27:29
39                <action android:name="android.intent.action.MAIN" />
39-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:24:17-69
39-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:24:25-66
40
41                <category android:name="android.intent.category.LAUNCHER" />
41-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:26:17-77
41-->B:\SHARENAPP\sharenshop\app\src\main\AndroidManifest.xml:26:27-74
42            </intent-filter>
43        </activity>
44
45        <provider
45-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:9:9-17:20
46            android:name="androidx.startup.InitializationProvider"
46-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:10:13-67
47            android:authorities="com.example.sharenshop.androidx-startup"
47-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:11:13-68
48            android:exported="false" >
48-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:12:13-37
49            <meta-data
49-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:14:13-16:52
50                android:name="io.github.jan.supabase.gotrue.SupabaseInitializer"
50-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:15:17-81
51                android:value="androidx.startup" />
51-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b77f6f34e82c16de59c23ca4a8accfed\transformed\gotrue-kt-debug\AndroidManifest.xml:16:17-49
52            <meta-data
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376240fbcd90d22d84174e96a1cb38d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
53                android:name="androidx.emoji2.text.EmojiCompatInitializer"
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376240fbcd90d22d84174e96a1cb38d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
54                android:value="androidx.startup" />
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376240fbcd90d22d84174e96a1cb38d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
55            <meta-data
55-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abe55d1e7e0cce7ec0aad498f97406f2\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
56-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abe55d1e7e0cce7ec0aad498f97406f2\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
57                android:value="androidx.startup" />
57-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abe55d1e7e0cce7ec0aad498f97406f2\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
58            <meta-data
58-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd68e0396aa6771b95db1eb7c16d35d0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
59                android:name="com.russhwolf.settings.SettingsInitializer"
59-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd68e0396aa6771b95db1eb7c16d35d0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
60                android:value="androidx.startup" />
60-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd68e0396aa6771b95db1eb7c16d35d0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
63                android:value="androidx.startup" />
63-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
64        </provider>
65
66        <activity
66-->[androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b1c9864de71495f04abaf0a87c4145f\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
67            android:name="androidx.compose.ui.tooling.PreviewActivity"
67-->[androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b1c9864de71495f04abaf0a87c4145f\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
68            android:exported="true" />
68-->[androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b1c9864de71495f04abaf0a87c4145f\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
69        <activity
69-->[androidx.compose.ui:ui-test-manifest:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\058d441810fd34f0246fdef2e8a1c5d4\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:23:9-25:39
70            android:name="androidx.activity.ComponentActivity"
70-->[androidx.compose.ui:ui-test-manifest:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\058d441810fd34f0246fdef2e8a1c5d4\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:24:13-63
71            android:exported="true" />
71-->[androidx.compose.ui:ui-test-manifest:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\058d441810fd34f0246fdef2e8a1c5d4\transformed\ui-test-manifest-1.6.0\AndroidManifest.xml:25:13-36
72
73        <service
73-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab1ead17130749a790fd159a99372a4c\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
74            android:name="androidx.room.MultiInstanceInvalidationService"
74-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab1ead17130749a790fd159a99372a4c\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
75            android:directBootAware="true"
75-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab1ead17130749a790fd159a99372a4c\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
76            android:exported="false" />
76-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab1ead17130749a790fd159a99372a4c\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
77
78        <receiver
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
79            android:name="androidx.profileinstaller.ProfileInstallReceiver"
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
80            android:directBootAware="false"
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
81            android:enabled="true"
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
82            android:exported="true"
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
83            android:permission="android.permission.DUMP" >
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
85                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
86            </intent-filter>
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
88                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
89            </intent-filter>
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
91                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
92            </intent-filter>
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
94                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\922406ffae7cee4e60e10e32c5f584af\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
95            </intent-filter>
96        </receiver>
97    </application>
98
99</manifest>
