package com.example.sharenshop.presentation.seller

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharenshop.data.model.Seller
import com.example.sharenshop.domain.use_case.SellerUseCases
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SellerViewModel @Inject constructor(
    private val sellerUseCases: SellerUseCases
) : ViewModel() {

    private val _sellers = MutableStateFlow<List<Seller>>(emptyList())
    val sellers: StateFlow<List<Seller>> = _sellers.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    init {
        loadSellers()
    }

    fun loadSellers() {
        _isLoading.value = true
        sellerUseCases.getAllSellers()
            .onEach { sellers ->
                _sellers.value = sellers
            }
            .catch { e ->
                _error.value = e.localizedMessage ?: "Unknown error loading sellers"
            }
            .onEach { _isLoading.value = false }
            .launchIn(viewModelScope)
    }

    fun insertSeller(seller: Seller) {
        viewModelScope.launch {
            try {
                sellerUseCases.insertSeller(seller)
                loadSellers() // Refresh the list after insertion
            } catch (e: Exception) {
                _error.value = e.localizedMessage ?: "Error inserting seller"
            }
        }
    }

    fun updateSeller(seller: Seller) {
        viewModelScope.launch {
            try {
                sellerUseCases.updateSeller(seller)
                loadSellers() // Refresh the list after update
            } catch (e: Exception) {
                _error.value = e.localizedMessage ?: "Error updating seller"
            }
        }
    }

    fun deleteSeller(seller: Seller) {
        viewModelScope.launch {
            try {
                sellerUseCases.deleteSeller(seller.id)
                loadSellers() // Refresh the list after deletion
            } catch (e: Exception) {
                _error.value = e.localizedMessage ?: "Error deleting seller"
            }
        }
    }

    fun searchSellers(query: String) {
        viewModelScope.launch {
            _isLoading.value = true
            sellerUseCases.searchSellers(query)
                .onEach { sellers ->
                    _sellers.value = sellers
                }
                .catch { e ->
                    _error.value = e.localizedMessage ?: "Unknown error searching sellers"
                }
                .onEach { _isLoading.value = false }
                .launchIn(viewModelScope)
        }
    }
} 