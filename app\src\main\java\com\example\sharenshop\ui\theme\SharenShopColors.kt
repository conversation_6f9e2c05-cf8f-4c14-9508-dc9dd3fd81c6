package com.example.sharenshop.ui.theme

import androidx.compose.material3.ColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

/**
 * SharenShop Custom Color Extensions
 * رنگ‌های سفارشی برای عناصر خاص اپلیکیشن
 */

// رنگ‌های اضافی برای عناصر خاص
object SharenShopColors {

    // رنگ‌های پایه طراحی
    val FutureDusk = Color(0xFFF8FAFC) // پس‌زمینه اصلی
    val DeepBlue = Color(0xFF1E3A8A) // آبی عمیق
    val SoftGray = Color(0xFF9CA3AF) // خاکستری ملایم
    val DarkBrown = Color(0xFF7C2D12) // قهوه‌ای تیره

    // رنگ‌های وضعیت
    val Success = Color(0xFF10B981) // سبز موفقیت
    val Warning = Color(0xFFF59E0B) // زرد هشدار
    val Info = Color(0xFF3B82F6) // آبی اطلاعات
    
    // رنگ‌های مخصوص نقش‌ها
    val SuperAdminColor = Color(0xFF7C2D12) // قهوه‌ای تیره برای مدیر کل
    val CashierColor = DeepBlue // آبی عمیق برای فروشنده
    val CustomerColor = SoftGray // خاکستری ملایم برای مشتری
    
    // رنگ‌های مخصوص آمار
    val ProfitColor = Color(0xFF059669) // سبز سود
    val LossColor = Color(0xFFDC2626) // قرمز ضرر
    val NeutralColor = SoftGray // خاکستری خنثی
    
    // رنگ‌های مخصوص فاکتور
    val PaidInvoice = Color(0xFF10B981) // سبز پرداخت شده
    val PendingInvoice = Color(0xFFF59E0B) // زرد در انتظار
    val OverdueInvoice = Color(0xFFDC2626) // قرمز معوقه
    
    // رنگ‌های مخصوص موجودی
    val InStock = Color(0xFF10B981) // سبز موجود
    val LowStock = Color(0xFFF59E0B) // زرد کم موجود
    val OutOfStock = Color(0xFFDC2626) // قرمز ناموجود
}

/**
 * Extension functions برای دسترسی آسان به رنگ‌های سفارشی
 */
val ColorScheme.success: Color
    @Composable get() = SharenShopColors.Success

val ColorScheme.warning: Color
    @Composable get() = SharenShopColors.Warning

val ColorScheme.info: Color
    @Composable get() = SharenShopColors.Info

val ColorScheme.superAdminColor: Color
    @Composable get() = SharenShopColors.SuperAdminColor

val ColorScheme.cashierColor: Color
    @Composable get() = SharenShopColors.CashierColor

val ColorScheme.customerColor: Color
    @Composable get() = SharenShopColors.CustomerColor

val ColorScheme.profitColor: Color
    @Composable get() = SharenShopColors.ProfitColor

val ColorScheme.lossColor: Color
    @Composable get() = SharenShopColors.LossColor

val ColorScheme.neutralColor: Color
    @Composable get() = SharenShopColors.NeutralColor

/**
 * Helper functions برای انتخاب رنگ بر اساس وضعیت
 */
object ColorHelper {
    
    @Composable
    fun getInvoiceStatusColor(status: String): Color {
        return when (status.lowercase()) {
            "paid", "پرداخت شده" -> SharenShopColors.PaidInvoice
            "pending", "در انتظار" -> SharenShopColors.PendingInvoice
            "overdue", "معوقه" -> SharenShopColors.OverdueInvoice
            else -> SharenShopColors.NeutralColor
        }
    }
    
    @Composable
    fun getStockStatusColor(stock: Int, minStock: Int = 10): Color {
        return when {
            stock <= 0 -> SharenShopColors.OutOfStock
            stock <= minStock -> SharenShopColors.LowStock
            else -> SharenShopColors.InStock
        }
    }
    
    @Composable
    fun getRoleColor(userType: String): Color {
        return when (userType.lowercase()) {
            "super_admin", "مدیر کل" -> SharenShopColors.SuperAdminColor
            "cashier", "فروشنده" -> SharenShopColors.CashierColor
            "customer", "مشتری" -> SharenShopColors.CustomerColor
            else -> SharenShopColors.NeutralColor
        }
    }
}
