// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.repository;

import com.example.sharenshop.data.security.DatabaseEncryptionService;
import com.example.sharenshop.data.security.FileEncryptionService;
import com.example.sharenshop.data.security.SharedPreferencesEncryptionService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SecurityRepositoryImpl_Factory implements Factory<SecurityRepositoryImpl> {
  private final Provider<DatabaseEncryptionService> databaseEncryptionServiceProvider;

  private final Provider<FileEncryptionService> fileEncryptionServiceProvider;

  private final Provider<SharedPreferencesEncryptionService> sharedPreferencesEncryptionServiceProvider;

  public SecurityRepositoryImpl_Factory(
      Provider<DatabaseEncryptionService> databaseEncryptionServiceProvider,
      Provider<FileEncryptionService> fileEncryptionServiceProvider,
      Provider<SharedPreferencesEncryptionService> sharedPreferencesEncryptionServiceProvider) {
    this.databaseEncryptionServiceProvider = databaseEncryptionServiceProvider;
    this.fileEncryptionServiceProvider = fileEncryptionServiceProvider;
    this.sharedPreferencesEncryptionServiceProvider = sharedPreferencesEncryptionServiceProvider;
  }

  @Override
  public SecurityRepositoryImpl get() {
    return newInstance(databaseEncryptionServiceProvider.get(), fileEncryptionServiceProvider.get(), sharedPreferencesEncryptionServiceProvider.get());
  }

  public static SecurityRepositoryImpl_Factory create(
      Provider<DatabaseEncryptionService> databaseEncryptionServiceProvider,
      Provider<FileEncryptionService> fileEncryptionServiceProvider,
      Provider<SharedPreferencesEncryptionService> sharedPreferencesEncryptionServiceProvider) {
    return new SecurityRepositoryImpl_Factory(databaseEncryptionServiceProvider, fileEncryptionServiceProvider, sharedPreferencesEncryptionServiceProvider);
  }

  public static SecurityRepositoryImpl newInstance(
      DatabaseEncryptionService databaseEncryptionService,
      FileEncryptionService fileEncryptionService,
      SharedPreferencesEncryptionService sharedPreferencesEncryptionService) {
    return new SecurityRepositoryImpl(databaseEncryptionService, fileEncryptionService, sharedPreferencesEncryptionService);
  }
}
