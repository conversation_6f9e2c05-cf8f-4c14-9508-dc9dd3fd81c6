// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.di;

import com.example.sharenshop.data.local.dao.CustomerDao;
import com.example.sharenshop.data.local.database.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaoModule_ProvideCustomerDaoFactory implements Factory<CustomerDao> {
  private final Provider<AppDatabase> appDatabaseProvider;

  public DaoModule_ProvideCustomerDaoFactory(Provider<AppDatabase> appDatabaseProvider) {
    this.appDatabaseProvider = appDatabaseProvider;
  }

  @Override
  public CustomerDao get() {
    return provideCustomerDao(appDatabaseProvider.get());
  }

  public static DaoModule_ProvideCustomerDaoFactory create(
      Provider<AppDatabase> appDatabaseProvider) {
    return new DaoModule_ProvideCustomerDaoFactory(appDatabaseProvider);
  }

  public static CustomerDao provideCustomerDao(AppDatabase appDatabase) {
    return Preconditions.checkNotNullFromProvides(DaoModule.INSTANCE.provideCustomerDao(appDatabase));
  }
}
