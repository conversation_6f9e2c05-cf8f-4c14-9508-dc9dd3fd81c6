package com.example.sharenshop.di

import com.example.sharenshop.data.local.dao.CustomerDao
import com.example.sharenshop.data.local.dao.InvoiceDao
import com.example.sharenshop.data.local.dao.InvoiceItemDao
import com.example.sharenshop.data.local.dao.NotificationDao
import com.example.sharenshop.data.local.dao.ProductDao
import com.example.sharenshop.data.local.dao.SellerDao
import com.example.sharenshop.data.local.dao.UserDao
import com.example.sharenshop.data.local.database.AppDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DaoModule {

    @Provides
    @Singleton
    fun provideUserDao(appDatabase: AppDatabase): UserDao {
        return appDatabase.userDao()
    }

    @Provides
    @Singleton
    fun provideProductDao(appDatabase: AppDatabase): ProductDao {
        return appDatabase.productDao()
    }

    @Provides
    @Singleton
    fun provideCustomerDao(appDatabase: AppDatabase): CustomerDao {
        return appDatabase.customerDao()
    }

    @Provides
    @Singleton
    fun provideSellerDao(appDatabase: AppDatabase): SellerDao {
        return appDatabase.sellerDao()
    }

    @Provides
    @Singleton
    fun provideInvoiceDao(appDatabase: AppDatabase): InvoiceDao {
        return appDatabase.invoiceDao()
    }

    @Provides
    @Singleton
    fun provideInvoiceItemDao(appDatabase: AppDatabase): InvoiceItemDao {
        return appDatabase.invoiceItemDao()
    }

    @Provides
    @Singleton
    fun provideNotificationDao(appDatabase: AppDatabase): NotificationDao {
        return appDatabase.notificationDao()
    }
}