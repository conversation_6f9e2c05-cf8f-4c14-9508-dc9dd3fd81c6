// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.data.security;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseEncryptionService_Factory implements Factory<DatabaseEncryptionService> {
  @Override
  public DatabaseEncryptionService get() {
    return newInstance();
  }

  public static DatabaseEncryptionService_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static DatabaseEncryptionService newInstance() {
    return new DatabaseEncryptionService();
  }

  private static final class InstanceHolder {
    private static final DatabaseEncryptionService_Factory INSTANCE = new DatabaseEncryptionService_Factory();
  }
}
