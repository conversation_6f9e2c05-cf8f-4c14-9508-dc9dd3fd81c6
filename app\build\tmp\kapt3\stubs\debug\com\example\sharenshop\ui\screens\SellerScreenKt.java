package com.example.sharenshop.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\n\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0002\u001a\u00020\u0001H\u0007\u00a8\u0006\u0003"}, d2 = {"PreviewSellerScreen", "", "SellerScreen", "app_debug"})
public final class SellerScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void SellerScreen() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(showBackground = true)
    @androidx.compose.runtime.Composable()
    public static final void PreviewSellerScreen() {
    }
}