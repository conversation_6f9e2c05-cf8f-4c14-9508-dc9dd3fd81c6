// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import com.example.sharenshop.domain.repository.CustomerApprovalRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetRequestsByReferrer_Factory implements Factory<GetRequestsByReferrer> {
  private final Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider;

  public GetRequestsByReferrer_Factory(
      Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider) {
    this.customerApprovalRepositoryProvider = customerApprovalRepositoryProvider;
  }

  @Override
  public GetRequestsByReferrer get() {
    return newInstance(customerApprovalRepositoryProvider.get());
  }

  public static GetRequestsByReferrer_Factory create(
      Provider<CustomerApprovalRepository> customerApprovalRepositoryProvider) {
    return new GetRequestsByReferrer_Factory(customerApprovalRepositoryProvider);
  }

  public static GetRequestsByReferrer newInstance(
      CustomerApprovalRepository customerApprovalRepository) {
    return new GetRequestsByReferrer(customerApprovalRepository);
  }
}
