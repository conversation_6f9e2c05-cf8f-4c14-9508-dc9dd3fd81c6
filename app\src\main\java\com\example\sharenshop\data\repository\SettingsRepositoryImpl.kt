package com.example.sharenshop.data.repository

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.dataStore
import com.example.sharenshop.data.datastore.AppSettingsSerializer
import com.example.sharenshop.data.model.AppSettings
import com.example.sharenshop.domain.repository.SettingsRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

private val Context.appSettingsDataStore: DataStore<AppSettings> by dataStore(
    fileName = "app_settings.json",
    serializer = AppSettingsSerializer
)

@Singleton
class SettingsRepositoryImpl @Inject constructor(
    private val context: Context
) : SettingsRepository {

    override fun getAppSettings(): Flow<AppSettings> {
        return context.appSettingsDataStore.data
    }

    override suspend fun saveAppSettings(settings: AppSettings) {
        context.appSettingsDataStore.updateData { settings }
    }
} 