package com.example.sharenshop.domain.use_case;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B5\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\tH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u000bH\u00c6\u0003J\t\u0010 \u001a\u00020\rH\u00c6\u0003JE\u0010!\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u00c6\u0001J\u0013\u0010\"\u001a\u00020#2\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020&H\u00d6\u0001J\t\u0010\'\u001a\u00020(H\u00d6\u0001R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001a\u00a8\u0006)"}, d2 = {"Lcom/example/sharenshop/domain/use_case/UserUseCases;", "", "getUser", "Lcom/example/sharenshop/domain/use_case/GetUser;", "insertUser", "Lcom/example/sharenshop/domain/use_case/InsertUser;", "updateUser", "Lcom/example/sharenshop/domain/use_case/UpdateUser;", "deleteUser", "Lcom/example/sharenshop/domain/use_case/DeleteUser;", "getAllUsers", "Lcom/example/sharenshop/domain/use_case/GetAllUsers;", "getUsersByType", "Lcom/example/sharenshop/domain/use_case/GetUsersByType;", "(Lcom/example/sharenshop/domain/use_case/GetUser;Lcom/example/sharenshop/domain/use_case/InsertUser;Lcom/example/sharenshop/domain/use_case/UpdateUser;Lcom/example/sharenshop/domain/use_case/DeleteUser;Lcom/example/sharenshop/domain/use_case/GetAllUsers;Lcom/example/sharenshop/domain/use_case/GetUsersByType;)V", "getDeleteUser", "()Lcom/example/sharenshop/domain/use_case/DeleteUser;", "getGetAllUsers", "()Lcom/example/sharenshop/domain/use_case/GetAllUsers;", "getGetUser", "()Lcom/example/sharenshop/domain/use_case/GetUser;", "getGetUsersByType", "()Lcom/example/sharenshop/domain/use_case/GetUsersByType;", "getInsertUser", "()Lcom/example/sharenshop/domain/use_case/InsertUser;", "getUpdateUser", "()Lcom/example/sharenshop/domain/use_case/UpdateUser;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class UserUseCases {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.GetUser getUser = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.InsertUser insertUser = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.UpdateUser updateUser = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.DeleteUser deleteUser = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.GetAllUsers getAllUsers = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.GetUsersByType getUsersByType = null;
    
    public UserUseCases(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetUser getUser, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.InsertUser insertUser, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.UpdateUser updateUser, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.DeleteUser deleteUser, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetAllUsers getAllUsers, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetUsersByType getUsersByType) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetUser getGetUser() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.InsertUser getInsertUser() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.UpdateUser getUpdateUser() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.DeleteUser getDeleteUser() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetAllUsers getGetAllUsers() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetUsersByType getGetUsersByType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetUser component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.InsertUser component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.UpdateUser component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.DeleteUser component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetAllUsers component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetUsersByType component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.UserUseCases copy(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetUser getUser, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.InsertUser insertUser, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.UpdateUser updateUser, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.DeleteUser deleteUser, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetAllUsers getAllUsers, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetUsersByType getUsersByType) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}