package com.example.sharenshop.data.repository

import com.example.sharenshop.data.local.dao.SellerDao
// import com.example.sharenshop.data.remote.SupabaseConstants
// import com.example.sharenshop.data.remote.SupabaseManager
import com.example.sharenshop.domain.repository.SellerRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import com.example.sharenshop.data.model.Seller

class SellerRepositoryImpl @Inject constructor(
    private val sellerDao: SellerDao,
//    private val supabaseManager: SupabaseManager
) : SellerRepository {

    override fun getSellerById(sellerId: String): Flow<Seller?> = flow {
        sellerDao.getSellerById(sellerId).collect { localSeller ->
            if (localSeller != null) {
                emit(localSeller)
            }
            try {
                // val response = supabaseManager.postgrest
                //     .from(SupabaseConstants.SELLERS_TABLE)
                //     .select()
                //     .eq("id", sellerId)
                //     .limit(1)
                //     .single()
                //     .execute()
                // TODO: Deserialize response.data to Seller object
                val remoteSeller: Seller? = null // Placeholder for deserialized seller

                if (remoteSeller != null) {
                    sellerDao.insertSeller(remoteSeller)
                    emit(remoteSeller)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override suspend fun insertSeller(seller: Seller) {
        sellerDao.insertSeller(seller)
        try {
            // val supabaseManager = SupabaseManager()
            // supabaseManager.postgrest
            //     .from(SupabaseConstants.SELLERS_TABLE)
            //     .insert(seller)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override suspend fun updateSeller(seller: Seller) {
        sellerDao.updateSeller(seller)
        try {
            // val supabaseManager = SupabaseManager()
            // supabaseManager.postgrest
            //     .from(SupabaseConstants.SELLERS_TABLE)
            //     .update(seller)
            //     .eq("id", seller.id)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override suspend fun deleteSellerById(sellerId: String) {
        sellerDao.deleteSellerById(sellerId)
        try {
            // val supabaseManager = SupabaseManager()
            // supabaseManager.postgrest
            //     .from(SupabaseConstants.SELLERS_TABLE)
            //     .delete()
            //     .eq("id", sellerId)
            //     .execute()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun getAllSellers(): Flow<List<Seller>> = flow {
        sellerDao.getAllSellers().collect { emit(it) }
    }

    override fun searchSellers(query: String): Flow<List<Seller>> = flow {
        sellerDao.searchSellers(query).collect { emit(it) }
    }
} 