// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.domain.use_case;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CustomerApprovalUseCases_Factory implements Factory<CustomerApprovalUseCases> {
  private final Provider<SubmitApprovalRequest> submitApprovalRequestProvider;

  private final Provider<ApproveCustomer> approveCustomerProvider;

  private final Provider<RejectCustomer> rejectCustomerProvider;

  private final Provider<GetPendingRequests> getPendingRequestsProvider;

  private final Provider<GetRequestsByReferrer> getRequestsByReferrerProvider;

  private final Provider<ValidateReferrerCode> validateReferrerCodeProvider;

  public CustomerApprovalUseCases_Factory(
      Provider<SubmitApprovalRequest> submitApprovalRequestProvider,
      Provider<ApproveCustomer> approveCustomerProvider,
      Provider<RejectCustomer> rejectCustomerProvider,
      Provider<GetPendingRequests> getPendingRequestsProvider,
      Provider<GetRequestsByReferrer> getRequestsByReferrerProvider,
      Provider<ValidateReferrerCode> validateReferrerCodeProvider) {
    this.submitApprovalRequestProvider = submitApprovalRequestProvider;
    this.approveCustomerProvider = approveCustomerProvider;
    this.rejectCustomerProvider = rejectCustomerProvider;
    this.getPendingRequestsProvider = getPendingRequestsProvider;
    this.getRequestsByReferrerProvider = getRequestsByReferrerProvider;
    this.validateReferrerCodeProvider = validateReferrerCodeProvider;
  }

  @Override
  public CustomerApprovalUseCases get() {
    return newInstance(submitApprovalRequestProvider.get(), approveCustomerProvider.get(), rejectCustomerProvider.get(), getPendingRequestsProvider.get(), getRequestsByReferrerProvider.get(), validateReferrerCodeProvider.get());
  }

  public static CustomerApprovalUseCases_Factory create(
      Provider<SubmitApprovalRequest> submitApprovalRequestProvider,
      Provider<ApproveCustomer> approveCustomerProvider,
      Provider<RejectCustomer> rejectCustomerProvider,
      Provider<GetPendingRequests> getPendingRequestsProvider,
      Provider<GetRequestsByReferrer> getRequestsByReferrerProvider,
      Provider<ValidateReferrerCode> validateReferrerCodeProvider) {
    return new CustomerApprovalUseCases_Factory(submitApprovalRequestProvider, approveCustomerProvider, rejectCustomerProvider, getPendingRequestsProvider, getRequestsByReferrerProvider, validateReferrerCodeProvider);
  }

  public static CustomerApprovalUseCases newInstance(SubmitApprovalRequest submitApprovalRequest,
      ApproveCustomer approveCustomer, RejectCustomer rejectCustomer,
      GetPendingRequests getPendingRequests, GetRequestsByReferrer getRequestsByReferrer,
      ValidateReferrerCode validateReferrerCode) {
    return new CustomerApprovalUseCases(submitApprovalRequest, approveCustomer, rejectCustomer, getPendingRequests, getRequestsByReferrer, validateReferrerCode);
  }
}
