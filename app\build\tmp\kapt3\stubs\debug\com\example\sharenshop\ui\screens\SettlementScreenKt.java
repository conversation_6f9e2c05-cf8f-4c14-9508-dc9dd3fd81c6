package com.example.sharenshop.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000 \n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0002\u001a\u00020\u0001H\u0007\u001a\u0010\u0010\u0003\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\b\u0010\u0006\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0007\u001a\u00020\u0001H\u0007\u001a\b\u0010\b\u001a\u00020\u0001H\u0007\u001a\b\u0010\t\u001a\u00020\u0001H\u0007\u001a\u0010\u0010\n\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\u0012\u0010\u000b\u001a\u00020\u00012\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a\u0010\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u0010H\u0007\u00a8\u0006\u0011"}, d2 = {"AdminPendingSettlementsTab", "", "AdminSettlementHistoryTab", "AdminSettlementReviewCard", "settlementDetail", "Lcom/example/sharenshop/data/model/SettlementDetails;", "AdminSettlementStatsTab", "CashierMyRequestsTab", "CashierNewSettlementTab", "CashierSettlementHistoryTab", "SettlementRequestCard", "SettlementScreen", "userRole", "Lcom/example/sharenshop/data/model/UserRole;", "SettlementStatusChip", "status", "Lcom/example/sharenshop/data/model/SettlementStatus;", "app_debug"})
public final class SettlementScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SettlementScreen(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.UserRole userRole) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CashierNewSettlementTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CashierMyRequestsTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SettlementRequestCard(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.SettlementDetails settlementDetail) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SettlementStatusChip(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.SettlementStatus status) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CashierSettlementHistoryTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AdminPendingSettlementsTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AdminSettlementReviewCard(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.data.model.SettlementDetails settlementDetail) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AdminSettlementHistoryTab() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AdminSettlementStatsTab() {
    }
}