{"logs": [{"outputFile": "com.example.sharenshop.app-mergeDebugResources-2:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\beb880474af4802d615c3712a7fe5cf4\\transformed\\browser-1.8.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,372", "endColumns": "105,101,108,105", "endOffsets": "156,258,367,473"}, "to": {"startLines": "11,15,16,17", "startColumns": "4,4,4,4", "startOffsets": "1012,1392,1494,1603", "endColumns": "105,101,108,105", "endOffsets": "1113,1489,1598,1704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8bf4b3fb7fecc46740c95cb34de6afa1\\transformed\\core-1.12.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "2,3,4,5,6,7,8,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,204,306,408,511,612,714,8706", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "199,301,403,506,607,709,829,8802"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\61acd70f2f91261ed7f816e4fc4161c1\\transformed\\ui-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,283,373,471,557,636,742,829,918,988,1058,1136,1217,1300,1375,1443", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "194,278,368,466,552,631,737,824,913,983,1053,1131,1212,1295,1370,1438,1556"}, "to": {"startLines": "9,10,12,13,14,18,19,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "834,928,1118,1208,1306,1709,1788,8148,8235,8324,8394,8464,8542,8623,8807,8882,8950", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "923,1007,1203,1301,1387,1783,1889,8230,8319,8389,8459,8537,8618,8701,8877,8945,9063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88b5b0acc1ef090d93ba86ccb1fae5cf\\transformed\\foundation-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "87,88", "startColumns": "4,4", "startOffsets": "9068,9153", "endColumns": "84,84", "endOffsets": "9148,9233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88c123eabfc6d4b207c77cc8294f818f\\transformed\\material3-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,193,330,449,583,700,799,915,1057,1178,1320,1405,1511,1605,1706,1835,1964,2075,2204,2331,2461,2641,2763,2883,3005,3136,3231,3326,3459,3606,3703,3808,3918,4045,4177,4284,4385,4462,4565,4665,4756,4846,4949,5029,5114,5215,5319,5412,5517,5604,5710,5809,5917,6035,6115,6215", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "188,325,444,578,695,794,910,1052,1173,1315,1400,1506,1600,1701,1830,1959,2070,2199,2326,2456,2636,2758,2878,3000,3131,3226,3321,3454,3601,3698,3803,3913,4040,4172,4279,4380,4457,4560,4660,4751,4841,4944,5024,5109,5210,5314,5407,5512,5599,5705,5804,5912,6030,6110,6210,6304"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1894,2032,2169,2288,2422,2539,2638,2754,2896,3017,3159,3244,3350,3444,3545,3674,3803,3914,4043,4170,4300,4480,4602,4722,4844,4975,5070,5165,5298,5445,5542,5647,5757,5884,6016,6123,6224,6301,6404,6504,6595,6685,6788,6868,6953,7054,7158,7251,7356,7443,7549,7648,7756,7874,7954,8054", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "2027,2164,2283,2417,2534,2633,2749,2891,3012,3154,3239,3345,3439,3540,3669,3798,3909,4038,4165,4295,4475,4597,4717,4839,4970,5065,5160,5293,5440,5537,5642,5752,5879,6011,6118,6219,6296,6399,6499,6590,6680,6783,6863,6948,7049,7153,7246,7351,7438,7544,7643,7751,7869,7949,8049,8143"}}]}]}