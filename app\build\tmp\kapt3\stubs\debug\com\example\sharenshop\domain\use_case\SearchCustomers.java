package com.example.sharenshop.domain.use_case;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001d\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u00062\u0006\u0010\t\u001a\u00020\nH\u0086\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/example/sharenshop/domain/use_case/SearchCustomers;", "", "repository", "Lcom/example/sharenshop/domain/repository/CustomerRepository;", "(Lcom/example/sharenshop/domain/repository/CustomerRepository;)V", "invoke", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/example/sharenshop/data/model/Customer;", "query", "", "app_debug"})
public final class SearchCustomers {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.repository.CustomerRepository repository = null;
    
    public SearchCustomers(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.repository.CustomerRepository repository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharenshop.data.model.Customer>> invoke(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
        return null;
    }
}