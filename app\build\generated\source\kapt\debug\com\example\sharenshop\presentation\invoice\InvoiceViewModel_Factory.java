// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.presentation.invoice;

import com.example.sharenshop.domain.use_case.InvoiceUseCases;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class InvoiceViewModel_Factory implements Factory<InvoiceViewModel> {
  private final Provider<InvoiceUseCases> invoiceUseCasesProvider;

  public InvoiceViewModel_Factory(Provider<InvoiceUseCases> invoiceUseCasesProvider) {
    this.invoiceUseCasesProvider = invoiceUseCasesProvider;
  }

  @Override
  public InvoiceViewModel get() {
    return newInstance(invoiceUseCasesProvider.get());
  }

  public static InvoiceViewModel_Factory create(Provider<InvoiceUseCases> invoiceUseCasesProvider) {
    return new InvoiceViewModel_Factory(invoiceUseCasesProvider);
  }

  public static InvoiceViewModel newInstance(InvoiceUseCases invoiceUseCases) {
    return new InvoiceViewModel(invoiceUseCases);
  }
}
