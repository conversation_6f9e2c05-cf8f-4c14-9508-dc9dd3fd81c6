package com.example.sharenshop.domain.use_case

import com.example.sharenshop.data.model.User
import com.example.sharenshop.domain.repository.UserRepository
import kotlinx.coroutines.flow.Flow

// Grouping all user-related use cases
data class UserUseCases(
    val getUser: GetUser,
    val insertUser: InsertUser,
    val updateUser: UpdateUser,
    val deleteUser: DeleteUser,
    val getAllUsers: GetAllUsers,
    val getUsersByType: GetUsersByType
)

class GetUser(private val repository: UserRepository) {
    operator fun invoke(userId: String): Flow<User?> {
        return repository.getUserById(userId)
    }
}

class InsertUser(private val repository: UserRepository) {
    suspend operator fun invoke(user: User) {
        repository.insertUser(user)
    }
}

class UpdateUser(private val repository: UserRepository) {
    suspend operator fun invoke(user: User) {
        repository.updateUser(user)
    }
}

class DeleteUser(private val repository: UserRepository) {
    suspend operator fun invoke(userId: String) {
        repository.deleteUserById(userId)
    }
}

class GetAllUsers(private val repository: UserRepository) {
    operator fun invoke(): Flow<List<User>> {
        return repository.getAllUsers()
    }
}

class GetUsersByType(private val repository: UserRepository) {
    operator fun invoke(userType: String): Flow<List<User>> {
        return repository.getUsersByUserType(userType)
    }
} 