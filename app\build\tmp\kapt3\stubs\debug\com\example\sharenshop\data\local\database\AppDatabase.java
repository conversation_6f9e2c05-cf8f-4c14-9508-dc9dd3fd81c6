package com.example.sharenshop.data.local.database;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\'\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&J\b\u0010\u0007\u001a\u00020\bH&J\b\u0010\t\u001a\u00020\nH&J\b\u0010\u000b\u001a\u00020\fH&J\b\u0010\r\u001a\u00020\u000eH&J\b\u0010\u000f\u001a\u00020\u0010H&\u00a8\u0006\u0011"}, d2 = {"Lcom/example/sharenshop/data/local/database/AppDatabase;", "Landroidx/room/RoomDatabase;", "()V", "customerDao", "Lcom/example/sharenshop/data/local/dao/CustomerDao;", "invoiceDao", "Lcom/example/sharenshop/data/local/dao/InvoiceDao;", "invoiceItemDao", "Lcom/example/sharenshop/data/local/dao/InvoiceItemDao;", "notificationDao", "Lcom/example/sharenshop/data/local/dao/NotificationDao;", "productDao", "Lcom/example/sharenshop/data/local/dao/ProductDao;", "sellerDao", "Lcom/example/sharenshop/data/local/dao/SellerDao;", "userDao", "Lcom/example/sharenshop/data/local/dao/UserDao;", "app_debug"})
@androidx.room.Database(entities = {com.example.sharenshop.data.model.User.class, com.example.sharenshop.data.model.Product.class, com.example.sharenshop.data.model.Customer.class, com.example.sharenshop.data.model.Seller.class, com.example.sharenshop.data.model.Invoice.class, com.example.sharenshop.data.model.InvoiceItem.class, com.example.sharenshop.data.model.Notification.class}, version = 3, exportSchema = false)
@androidx.room.TypeConverters(value = {com.example.sharenshop.data.local.converter.BigDecimalConverter.class})
public abstract class AppDatabase extends androidx.room.RoomDatabase {
    
    public AppDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.data.local.dao.UserDao userDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.data.local.dao.ProductDao productDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.data.local.dao.CustomerDao customerDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.data.local.dao.SellerDao sellerDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.data.local.dao.InvoiceDao invoiceDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.data.local.dao.InvoiceItemDao invoiceItemDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.sharenshop.data.local.dao.NotificationDao notificationDao();
}