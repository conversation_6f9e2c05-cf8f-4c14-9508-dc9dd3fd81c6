package com.example.sharenshop.domain.repository

import com.example.sharenshop.data.model.Seller
import kotlinx.coroutines.flow.Flow

interface SellerRepository {
    fun getSellerById(sellerId: String): Flow<Seller?>
    suspend fun insertSeller(seller: Seller)
    suspend fun updateSeller(seller: Seller)
    suspend fun deleteSellerById(sellerId: String)
    fun getAllSellers(): Flow<List<Seller>>
    fun searchSellers(query: String): Flow<List<Seller>>
} 