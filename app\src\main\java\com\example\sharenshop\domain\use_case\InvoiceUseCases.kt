package com.example.sharenshop.domain.use_case

import com.example.sharenshop.data.model.Invoice
import com.example.sharenshop.domain.repository.InvoiceRepository
import kotlinx.coroutines.flow.Flow

// Grouping all invoice-related use cases
data class InvoiceUseCases(
    val getInvoice: GetInvoice,
    val insertInvoice: InsertInvoice,
    val updateInvoice: UpdateInvoice,
    val deleteInvoice: DeleteInvoice,
    val getAllInvoices: GetAllInvoices,
    val getInvoicesByCustomer: GetInvoicesByCustomer,
    val getInvoicesByUser: GetInvoicesByUser
)

class GetInvoice(private val repository: InvoiceRepository) {
    operator fun invoke(invoiceId: String): Flow<Invoice?> {
        return repository.getInvoiceById(invoiceId)
    }
}

class InsertInvoice(private val repository: InvoiceRepository) {
    suspend operator fun invoke(invoice: Invoice) {
        repository.insertInvoice(invoice)
    }
}

class UpdateInvoice(private val repository: InvoiceRepository) {
    suspend operator fun invoke(invoice: Invoice) {
        repository.updateInvoice(invoice)
    }
}

class DeleteInvoice(private val repository: InvoiceRepository) {
    suspend operator fun invoke(invoiceId: String) {
        repository.deleteInvoiceById(invoiceId)
    }
}

class GetAllInvoices(private val repository: InvoiceRepository) {
    operator fun invoke(): Flow<List<Invoice>> {
        return repository.getAllInvoices()
    }
}

class GetInvoicesByCustomer(private val repository: InvoiceRepository) {
    operator fun invoke(customerId: String): Flow<List<Invoice>> {
        return repository.getInvoicesByCustomerId(customerId)
    }
}

class GetInvoicesByUser(private val repository: InvoiceRepository) {
    operator fun invoke(userId: String): Flow<List<Invoice>> {
        return repository.getInvoicesByUserId(userId)
    }
} 