package com.example.sharenshop.domain.use_case;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\tH\u00c6\u0003J1\u0010\u0017\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u00c6\u0001J\u0013\u0010\u0018\u001a\u00020\u00192\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001J\t\u0010\u001d\u001a\u00020\u001eH\u00d6\u0001R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012\u00a8\u0006\u001f"}, d2 = {"Lcom/example/sharenshop/domain/use_case/SecurityUseCases;", "", "getEncryptionSettings", "Lcom/example/sharenshop/domain/use_case/GetEncryptionSettings;", "saveEncryptionSettings", "Lcom/example/sharenshop/domain/use_case/SaveEncryptionSettings;", "encryptData", "Lcom/example/sharenshop/domain/use_case/EncryptData;", "decryptData", "Lcom/example/sharenshop/domain/use_case/DecryptData;", "(Lcom/example/sharenshop/domain/use_case/GetEncryptionSettings;Lcom/example/sharenshop/domain/use_case/SaveEncryptionSettings;Lcom/example/sharenshop/domain/use_case/EncryptData;Lcom/example/sharenshop/domain/use_case/DecryptData;)V", "getDecryptData", "()Lcom/example/sharenshop/domain/use_case/DecryptData;", "getEncryptData", "()Lcom/example/sharenshop/domain/use_case/EncryptData;", "getGetEncryptionSettings", "()Lcom/example/sharenshop/domain/use_case/GetEncryptionSettings;", "getSaveEncryptionSettings", "()Lcom/example/sharenshop/domain/use_case/SaveEncryptionSettings;", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class SecurityUseCases {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.GetEncryptionSettings getEncryptionSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.SaveEncryptionSettings saveEncryptionSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.EncryptData encryptData = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.DecryptData decryptData = null;
    
    public SecurityUseCases(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetEncryptionSettings getEncryptionSettings, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.SaveEncryptionSettings saveEncryptionSettings, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.EncryptData encryptData, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.DecryptData decryptData) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetEncryptionSettings getGetEncryptionSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.SaveEncryptionSettings getSaveEncryptionSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.EncryptData getEncryptData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.DecryptData getDecryptData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetEncryptionSettings component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.SaveEncryptionSettings component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.EncryptData component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.DecryptData component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.SecurityUseCases copy(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetEncryptionSettings getEncryptionSettings, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.SaveEncryptionSettings saveEncryptionSettings, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.EncryptData encryptData, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.DecryptData decryptData) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}