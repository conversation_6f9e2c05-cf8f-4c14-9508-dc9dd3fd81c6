// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.sharenshop.presentation.notification;

import com.example.sharenshop.domain.repository.NotificationRepository;
import com.example.sharenshop.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NotificationViewModel_Factory implements Factory<NotificationViewModel> {
  private final Provider<NotificationRepository> notificationRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  public NotificationViewModel_Factory(
      Provider<NotificationRepository> notificationRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    this.notificationRepositoryProvider = notificationRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public NotificationViewModel get() {
    return newInstance(notificationRepositoryProvider.get(), userRepositoryProvider.get());
  }

  public static NotificationViewModel_Factory create(
      Provider<NotificationRepository> notificationRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    return new NotificationViewModel_Factory(notificationRepositoryProvider, userRepositoryProvider);
  }

  public static NotificationViewModel newInstance(NotificationRepository notificationRepository,
      UserRepository userRepository) {
    return new NotificationViewModel(notificationRepository, userRepository);
  }
}
