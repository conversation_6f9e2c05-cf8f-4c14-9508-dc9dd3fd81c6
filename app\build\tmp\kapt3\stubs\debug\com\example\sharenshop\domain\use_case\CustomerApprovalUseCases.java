package com.example.sharenshop.domain.use_case;

/**
 * Use Cases برای مدیریت درخواست‌های تایید مشتری
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B7\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\tH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u000bH\u00c6\u0003J\t\u0010 \u001a\u00020\rH\u00c6\u0003JE\u0010!\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u00c6\u0001J\u0013\u0010\"\u001a\u00020#2\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020&H\u00d6\u0001J\t\u0010\'\u001a\u00020(H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001a\u00a8\u0006)"}, d2 = {"Lcom/example/sharenshop/domain/use_case/CustomerApprovalUseCases;", "", "submitApprovalRequest", "Lcom/example/sharenshop/domain/use_case/SubmitApprovalRequest;", "approveCustomer", "Lcom/example/sharenshop/domain/use_case/ApproveCustomer;", "rejectCustomer", "Lcom/example/sharenshop/domain/use_case/RejectCustomer;", "getPendingRequests", "Lcom/example/sharenshop/domain/use_case/GetPendingRequests;", "getRequestsByReferrer", "Lcom/example/sharenshop/domain/use_case/GetRequestsByReferrer;", "validateReferrerCode", "Lcom/example/sharenshop/domain/use_case/ValidateReferrerCode;", "(Lcom/example/sharenshop/domain/use_case/SubmitApprovalRequest;Lcom/example/sharenshop/domain/use_case/ApproveCustomer;Lcom/example/sharenshop/domain/use_case/RejectCustomer;Lcom/example/sharenshop/domain/use_case/GetPendingRequests;Lcom/example/sharenshop/domain/use_case/GetRequestsByReferrer;Lcom/example/sharenshop/domain/use_case/ValidateReferrerCode;)V", "getApproveCustomer", "()Lcom/example/sharenshop/domain/use_case/ApproveCustomer;", "getGetPendingRequests", "()Lcom/example/sharenshop/domain/use_case/GetPendingRequests;", "getGetRequestsByReferrer", "()Lcom/example/sharenshop/domain/use_case/GetRequestsByReferrer;", "getRejectCustomer", "()Lcom/example/sharenshop/domain/use_case/RejectCustomer;", "getSubmitApprovalRequest", "()Lcom/example/sharenshop/domain/use_case/SubmitApprovalRequest;", "getValidateReferrerCode", "()Lcom/example/sharenshop/domain/use_case/ValidateReferrerCode;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class CustomerApprovalUseCases {
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.SubmitApprovalRequest submitApprovalRequest = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.ApproveCustomer approveCustomer = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.RejectCustomer rejectCustomer = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.GetPendingRequests getPendingRequests = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.GetRequestsByReferrer getRequestsByReferrer = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.sharenshop.domain.use_case.ValidateReferrerCode validateReferrerCode = null;
    
    @javax.inject.Inject()
    public CustomerApprovalUseCases(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.SubmitApprovalRequest submitApprovalRequest, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.ApproveCustomer approveCustomer, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.RejectCustomer rejectCustomer, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetPendingRequests getPendingRequests, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetRequestsByReferrer getRequestsByReferrer, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.ValidateReferrerCode validateReferrerCode) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.SubmitApprovalRequest getSubmitApprovalRequest() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.ApproveCustomer getApproveCustomer() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.RejectCustomer getRejectCustomer() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetPendingRequests getGetPendingRequests() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetRequestsByReferrer getGetRequestsByReferrer() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.ValidateReferrerCode getValidateReferrerCode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.SubmitApprovalRequest component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.ApproveCustomer component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.RejectCustomer component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetPendingRequests component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.GetRequestsByReferrer component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.ValidateReferrerCode component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.sharenshop.domain.use_case.CustomerApprovalUseCases copy(@org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.SubmitApprovalRequest submitApprovalRequest, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.ApproveCustomer approveCustomer, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.RejectCustomer rejectCustomer, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetPendingRequests getPendingRequests, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.GetRequestsByReferrer getRequestsByReferrer, @org.jetbrains.annotations.NotNull()
    com.example.sharenshop.domain.use_case.ValidateReferrerCode validateReferrerCode) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}