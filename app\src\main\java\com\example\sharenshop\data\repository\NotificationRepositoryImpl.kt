package com.example.sharenshop.data.repository

import com.example.sharenshop.data.local.dao.NotificationDao
import com.example.sharenshop.data.model.Notification
import com.example.sharenshop.data.model.NotificationType
import com.example.sharenshop.domain.repository.NotificationRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NotificationRepositoryImpl @Inject constructor(
    private val notificationDao: NotificationDao
) : NotificationRepository {

    override suspend fun getNotificationById(id: String): Notification? {
        return notificationDao.getNotificationById(id)
    }

    override fun getNotificationsByUser(userId: String): Flow<List<Notification>> {
        return notificationDao.getNotificationsByUser(userId)
    }

    override fun getUnreadNotificationsByUser(userId: String): Flow<List<Notification>> {
        return notificationDao.getUnreadNotificationsByUser(userId)
    }

    override fun getNotificationsByUserAndType(userId: String, type: NotificationType): Flow<List<Notification>> {
        return notificationDao.getNotificationsByUserAndType(userId, type)
    }

    override suspend fun getUnreadNotificationsCount(userId: String): Int {
        return notificationDao.getUnreadNotificationsCount(userId)
    }

    override suspend fun insertNotification(notification: Notification) {
        notificationDao.insertNotification(notification)
    }

    override suspend fun insertNotifications(notifications: List<Notification>) {
        notificationDao.insertNotifications(notifications)
    }

    override suspend fun updateNotification(notification: Notification) {
        notificationDao.updateNotification(notification)
    }

    override suspend fun deleteNotification(notification: Notification) {
        notificationDao.deleteNotification(notification)
    }

    override suspend fun markAsRead(notificationId: String) {
        notificationDao.markAsRead(notificationId)
    }

    override suspend fun markAllAsReadForUser(userId: String) {
        notificationDao.markAllAsReadForUser(userId)
    }

    override suspend fun markAsReadByType(userId: String, type: NotificationType) {
        notificationDao.markAsReadByType(userId, type)
    }

    override suspend fun deleteReadNotifications(userId: String) {
        notificationDao.deleteReadNotifications(userId)
    }

    override suspend fun deleteOldNotifications(expireDate: Long) {
        notificationDao.deleteOldNotifications(expireDate)
    }

    override suspend fun getRecentNotifications(userId: String, limit: Int): List<Notification> {
        return notificationDao.getRecentNotifications(userId, limit)
    }
}
