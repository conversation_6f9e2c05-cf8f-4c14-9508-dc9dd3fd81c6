package com.example.sharenshop.domain.repository

import com.example.sharenshop.data.model.Product
import kotlinx.coroutines.flow.Flow

interface ProductRepository {
    fun getProductById(productId: String): Flow<Product?>
    suspend fun insertProduct(product: Product)
    suspend fun updateProduct(product: Product)
    suspend fun deleteProductById(productId: String)
    fun getAllProducts(): Flow<List<Product>>
    fun searchProducts(query: String): Flow<List<Product>>
} 