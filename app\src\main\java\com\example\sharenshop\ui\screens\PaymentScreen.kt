package com.example.sharenshop.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.sharenshop.data.model.*
import com.example.sharenshop.ui.theme.SharenShopColors
import java.math.BigDecimal
import java.text.NumberFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PaymentScreen(
    userRole: UserRole = UserRole.CUSTOMER
) {
    var selectedTab by remember { mutableStateOf(0) }
    val tabs = when (userRole) {
        UserRole.CUSTOMER -> listOf("پرداخت جدید", "تاریخچه پرداخت‌ها")
        UserRole.CASHIER -> listOf("درخواست‌های پرداخت", "تاریخچه دریافت‌ها")
        UserRole.SUPER_ADMIN -> listOf("همه پرداخت‌ها", "آمار پرداخت‌ها")
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(SharenShopColors.FutureDusk)
            .padding(16.dp)
    ) {
        // Header
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "💰 مدیریت پرداخت‌ها",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = SharenShopColors.DeepBlue
                )
                Text(
                    text = when (userRole) {
                        UserRole.CUSTOMER -> "پرداخت بدهی‌ها و مشاهده تاریخچه"
                        UserRole.CASHIER -> "تایید پرداخت‌های دریافتی"
                        UserRole.SUPER_ADMIN -> "نظارت بر کل پرداخت‌ها"
                    },
                    fontSize = 14.sp,
                    color = SharenShopColors.SoftGray
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Tab Row
        TabRow(
            selectedTabIndex = selectedTab,
            containerColor = Color.White,
            contentColor = SharenShopColors.DeepBlue
        ) {
            tabs.forEachIndexed { index, title ->
                Tab(
                    selected = selectedTab == index,
                    onClick = { selectedTab = index },
                    text = { Text(title) }
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Content based on selected tab and user role
        when (userRole) {
            UserRole.CUSTOMER -> {
                when (selectedTab) {
                    0 -> CustomerNewPaymentTab()
                    1 -> CustomerPaymentHistoryTab()
                }
            }
            UserRole.CASHIER -> {
                when (selectedTab) {
                    0 -> CashierPendingPaymentsTab()
                    1 -> CashierPaymentHistoryTab()
                }
            }
            UserRole.SUPER_ADMIN -> {
                when (selectedTab) {
                    0 -> AdminAllPaymentsTab()
                    1 -> AdminPaymentStatsTab()
                }
            }
        }
    }
}

@Composable
fun CustomerNewPaymentTab() {
    var selectedSeller by remember { mutableStateOf("") }
    var paymentAmount by remember { mutableStateOf("") }
    var paymentMethod by remember { mutableStateOf(PaymentMethod.CASH) }
    var trackingNumber by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }

    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // Outstanding Debt Summary
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "📊 خلاصه بدهی‌ها",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = SharenShopColors.DeepBlue
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text("کل بدهی:", fontWeight = FontWeight.Medium)
                        Text(
                            "۱۲,۵۰۰,۰۰۰ تومان",
                            color = SharenShopColors.LossColor,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text("معوقه:", fontWeight = FontWeight.Medium)
                        Text(
                            "۳,۲۰۰,۰۰۰ تومان",
                            color = Color.Red,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }

        item {
            // Payment Form
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "💳 پرداخت جدید",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = SharenShopColors.DeepBlue
                    )
                    Spacer(modifier = Modifier.height(16.dp))

                    // Seller Selection
                    OutlinedTextField(
                        value = selectedSeller,
                        onValueChange = { selectedSeller = it },
                        label = { Text("انتخاب فروشنده") },
                        modifier = Modifier.fillMaxWidth(),
                        trailingIcon = {
                            Icon(Icons.Default.ArrowDropDown, contentDescription = null)
                        }
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    // Payment Amount
                    OutlinedTextField(
                        value = paymentAmount,
                        onValueChange = { paymentAmount = it },
                        label = { Text("مبلغ پرداخت (تومان)") },
                        modifier = Modifier.fillMaxWidth(),
                        leadingIcon = {
                            Icon(Icons.Default.Star, contentDescription = null)
                        }
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    // Payment Method
                    Text(
                        text = "روش پرداخت:",
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        PaymentMethod.values().forEach { method ->
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                RadioButton(
                                    selected = paymentMethod == method,
                                    onClick = { paymentMethod = method }
                                )
                                Text(
                                    text = method.displayName,
                                    modifier = Modifier.padding(start = 4.dp)
                                )
                            }
                        }
                    }

                    // Tracking Number (for card to card)
                    if (paymentMethod == PaymentMethod.CARD_TO_CARD) {
                        Spacer(modifier = Modifier.height(12.dp))
                        OutlinedTextField(
                            value = trackingNumber,
                            onValueChange = { trackingNumber = it },
                            label = { Text("شماره پیگیری") },
                            modifier = Modifier.fillMaxWidth(),
                            leadingIcon = {
                                Icon(Icons.Default.Star, contentDescription = null)
                            }
                        )
                    }

                    Spacer(modifier = Modifier.height(12.dp))

                    // Description
                    OutlinedTextField(
                        value = description,
                        onValueChange = { description = it },
                        label = { Text("توضیحات (اختیاری)") },
                        modifier = Modifier.fillMaxWidth(),
                        maxLines = 3
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Submit Button
                    Button(
                        onClick = { /* Handle payment submission */ },
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = SharenShopColors.DeepBlue
                        )
                    ) {
                        @Suppress("DEPRECATION")
                        Icon(Icons.Default.Send, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("ارسال درخواست پرداخت")
                    }
                }
            }
        }
    }
}

@Composable
fun CustomerPaymentHistoryTab() {
    // Sample payment history
    val payments = remember {
        listOf(
            PaymentDetails(
                payment = Payment(
                    id = "1",
                    customerId = "customer1",
                    receiverId = "seller1",
                    amount = BigDecimal("500000"),
                    paymentMethod = PaymentMethod.CASH,
                    status = PaymentStatus.CONFIRMED,
                    paymentDate = System.currentTimeMillis(),
                    createdAt = System.currentTimeMillis()
                ),
                customerName = "شما",
                receiverName = "احمد محمدی",
                remainingDebt = BigDecimal("1200000")
            )
        )
    }

    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(payments) { paymentDetail ->
            PaymentHistoryCard(paymentDetail)
        }
    }
}

@Composable
fun PaymentHistoryCard(paymentDetail: PaymentDetails) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = paymentDetail.receiverName,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
                
                StatusChip(paymentDetail.payment.status)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("مبلغ:")
                Text(
                    text = NumberFormat.getNumberInstance(Locale("fa", "IR"))
                        .format(paymentDetail.payment.amount) + " تومان",
                    fontWeight = FontWeight.Bold,
                    color = SharenShopColors.ProfitColor
                )
            }
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("روش پرداخت:")
                Text(paymentDetail.payment.paymentMethod.displayName)
            }
            
            if (paymentDetail.payment.trackingNumber != null) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("شماره پیگیری:")
                    Text(paymentDetail.payment.trackingNumber)
                }
            }
        }
    }
}

@Composable
fun StatusChip(status: PaymentStatus) {
    val (backgroundColor, textColor) = when (status) {
        PaymentStatus.PENDING -> SharenShopColors.Warning to Color.White
        PaymentStatus.CONFIRMED -> SharenShopColors.Success to Color.White
        PaymentStatus.REJECTED -> SharenShopColors.LossColor to Color.White
    }
    
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundColor)
            .padding(horizontal = 12.dp, vertical = 4.dp)
    ) {
        Text(
            text = status.displayName,
            color = textColor,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
fun CashierPendingPaymentsTab() {
    Text(
        text = "درخواست‌های پرداخت در انتظار تایید",
        modifier = Modifier.fillMaxWidth(),
        textAlign = TextAlign.Center,
        fontSize = 16.sp,
        color = SharenShopColors.SoftGray
    )
}

@Composable
fun CashierPaymentHistoryTab() {
    Text(
        text = "تاریخچه پرداخت‌های تایید شده",
        modifier = Modifier.fillMaxWidth(),
        textAlign = TextAlign.Center,
        fontSize = 16.sp,
        color = SharenShopColors.SoftGray
    )
}

@Composable
fun AdminAllPaymentsTab() {
    Text(
        text = "همه پرداخت‌های سیستم",
        modifier = Modifier.fillMaxWidth(),
        textAlign = TextAlign.Center,
        fontSize = 16.sp,
        color = SharenShopColors.SoftGray
    )
}

@Composable
fun AdminPaymentStatsTab() {
    Text(
        text = "آمار و گزارش پرداخت‌ها",
        modifier = Modifier.fillMaxWidth(),
        textAlign = TextAlign.Center,
        fontSize = 16.sp,
        color = SharenShopColors.SoftGray
    )
}
