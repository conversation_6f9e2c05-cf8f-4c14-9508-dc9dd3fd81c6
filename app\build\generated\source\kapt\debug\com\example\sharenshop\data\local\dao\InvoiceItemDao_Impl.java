package com.example.sharenshop.data.local.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.sharenshop.data.local.converter.BigDecimalConverter;
import com.example.sharenshop.data.model.InvoiceItem;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class InvoiceItemDao_Impl implements InvoiceItemDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<InvoiceItem> __insertionAdapterOfInvoiceItem;

  private final BigDecimalConverter __bigDecimalConverter = new BigDecimalConverter();

  private final EntityDeletionOrUpdateAdapter<InvoiceItem> __updateAdapterOfInvoiceItem;

  private final SharedSQLiteStatement __preparedStmtOfDeleteInvoiceItemById;

  public InvoiceItemDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfInvoiceItem = new EntityInsertionAdapter<InvoiceItem>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `invoice_items` (`id`,`invoiceId`,`productId`,`quantity`,`unitPrice`,`totalPrice`,`createdAt`) VALUES (?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final InvoiceItem entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getInvoiceId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getInvoiceId());
        }
        if (entity.getProductId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getProductId());
        }
        statement.bindLong(4, entity.getQuantity());
        final String _tmp = __bigDecimalConverter.fromBigDecimal(entity.getUnitPrice());
        if (_tmp == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp);
        }
        final String _tmp_1 = __bigDecimalConverter.fromBigDecimal(entity.getTotalPrice());
        if (_tmp_1 == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, _tmp_1);
        }
        statement.bindLong(7, entity.getCreatedAt());
      }
    };
    this.__updateAdapterOfInvoiceItem = new EntityDeletionOrUpdateAdapter<InvoiceItem>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `invoice_items` SET `id` = ?,`invoiceId` = ?,`productId` = ?,`quantity` = ?,`unitPrice` = ?,`totalPrice` = ?,`createdAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final InvoiceItem entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getInvoiceId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getInvoiceId());
        }
        if (entity.getProductId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getProductId());
        }
        statement.bindLong(4, entity.getQuantity());
        final String _tmp = __bigDecimalConverter.fromBigDecimal(entity.getUnitPrice());
        if (_tmp == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp);
        }
        final String _tmp_1 = __bigDecimalConverter.fromBigDecimal(entity.getTotalPrice());
        if (_tmp_1 == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, _tmp_1);
        }
        statement.bindLong(7, entity.getCreatedAt());
        if (entity.getId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteInvoiceItemById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM invoice_items WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertInvoiceItem(final InvoiceItem invoiceItem,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfInvoiceItem.insert(invoiceItem);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertAllInvoiceItems(final List<InvoiceItem> invoiceItems,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfInvoiceItem.insert(invoiceItems);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateInvoiceItem(final InvoiceItem invoiceItem,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfInvoiceItem.handle(invoiceItem);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteInvoiceItemById(final String invoiceItemId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteInvoiceItemById.acquire();
        int _argIndex = 1;
        if (invoiceItemId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, invoiceItemId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteInvoiceItemById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<InvoiceItem> getInvoiceItemById(final String invoiceItemId) {
    final String _sql = "SELECT * FROM invoice_items WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (invoiceItemId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, invoiceItemId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"invoice_items"}, new Callable<InvoiceItem>() {
      @Override
      @Nullable
      public InvoiceItem call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfInvoiceId = CursorUtil.getColumnIndexOrThrow(_cursor, "invoiceId");
          final int _cursorIndexOfProductId = CursorUtil.getColumnIndexOrThrow(_cursor, "productId");
          final int _cursorIndexOfQuantity = CursorUtil.getColumnIndexOrThrow(_cursor, "quantity");
          final int _cursorIndexOfUnitPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "unitPrice");
          final int _cursorIndexOfTotalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPrice");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final InvoiceItem _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpInvoiceId;
            if (_cursor.isNull(_cursorIndexOfInvoiceId)) {
              _tmpInvoiceId = null;
            } else {
              _tmpInvoiceId = _cursor.getString(_cursorIndexOfInvoiceId);
            }
            final String _tmpProductId;
            if (_cursor.isNull(_cursorIndexOfProductId)) {
              _tmpProductId = null;
            } else {
              _tmpProductId = _cursor.getString(_cursorIndexOfProductId);
            }
            final int _tmpQuantity;
            _tmpQuantity = _cursor.getInt(_cursorIndexOfQuantity);
            final BigDecimal _tmpUnitPrice;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfUnitPrice)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfUnitPrice);
            }
            _tmpUnitPrice = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpTotalPrice;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTotalPrice)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTotalPrice);
            }
            _tmpTotalPrice = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _result = new InvoiceItem(_tmpId,_tmpInvoiceId,_tmpProductId,_tmpQuantity,_tmpUnitPrice,_tmpTotalPrice,_tmpCreatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<InvoiceItem>> getInvoiceItemsByInvoiceId(final String invoiceId) {
    final String _sql = "SELECT * FROM invoice_items WHERE invoiceId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (invoiceId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, invoiceId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"invoice_items"}, new Callable<List<InvoiceItem>>() {
      @Override
      @NonNull
      public List<InvoiceItem> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfInvoiceId = CursorUtil.getColumnIndexOrThrow(_cursor, "invoiceId");
          final int _cursorIndexOfProductId = CursorUtil.getColumnIndexOrThrow(_cursor, "productId");
          final int _cursorIndexOfQuantity = CursorUtil.getColumnIndexOrThrow(_cursor, "quantity");
          final int _cursorIndexOfUnitPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "unitPrice");
          final int _cursorIndexOfTotalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPrice");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final List<InvoiceItem> _result = new ArrayList<InvoiceItem>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final InvoiceItem _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpInvoiceId;
            if (_cursor.isNull(_cursorIndexOfInvoiceId)) {
              _tmpInvoiceId = null;
            } else {
              _tmpInvoiceId = _cursor.getString(_cursorIndexOfInvoiceId);
            }
            final String _tmpProductId;
            if (_cursor.isNull(_cursorIndexOfProductId)) {
              _tmpProductId = null;
            } else {
              _tmpProductId = _cursor.getString(_cursorIndexOfProductId);
            }
            final int _tmpQuantity;
            _tmpQuantity = _cursor.getInt(_cursorIndexOfQuantity);
            final BigDecimal _tmpUnitPrice;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfUnitPrice)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfUnitPrice);
            }
            _tmpUnitPrice = __bigDecimalConverter.toBigDecimal(_tmp);
            final BigDecimal _tmpTotalPrice;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTotalPrice)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTotalPrice);
            }
            _tmpTotalPrice = __bigDecimalConverter.toBigDecimal(_tmp_1);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item = new InvoiceItem(_tmpId,_tmpInvoiceId,_tmpProductId,_tmpQuantity,_tmpUnitPrice,_tmpTotalPrice,_tmpCreatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
